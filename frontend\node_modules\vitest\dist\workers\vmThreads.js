import { a as createThreadsRpcOptions } from '../chunks/utils.CAioKnHs.js';
import { r as runVmTests } from '../chunks/vm.BThCzidc.js';
import '@vitest/utils';
import 'node:url';
import 'node:vm';
import 'pathe';
import '../path.js';
import 'node:path';
import '../chunks/console.CtFJOzRO.js';
import 'node:console';
import 'node:stream';
import 'tinyrainbow';
import '../chunks/date.Bq6ZW5rf.js';
import '../chunks/utils.XdZDrNZV.js';
import '../chunks/execute.B7h3T_Hc.js';
import 'node:fs';
import '@vitest/utils/error';
import 'vite-node/client';
import 'vite-node/utils';
import '@vitest/mocker';
import 'node:module';
import 'vite-node/constants';

class ThreadsVmWorker {
	getRpcOptions(ctx) {
		return createThreadsRpcOptions(ctx);
	}
	runTests(state) {
		return runVmTests("run", state);
	}
	collectTests(state) {
		return runVmTests("collect", state);
	}
}
const worker = new ThreadsVmWorker();

export { worker as default };
