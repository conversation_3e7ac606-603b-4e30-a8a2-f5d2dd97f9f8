{"name": "http-proxy-agent", "version": "7.0.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/http-proxy-agent"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}}