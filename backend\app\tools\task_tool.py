"""Task management tool."""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func

from app.models.database import TaskModel
from app.models.pydantic_models import Task, TaskPriority
from app.database.connection import get_async_session

logger = logging.getLogger(__name__)


class TaskTool:
    """Mirascope tool for task operations."""
    
    async def create_task(
        self,
        title: str,
        description: Optional[str] = None,
        category: Optional[str] = None,
        ai_generated_category: Optional[str] = None,
        priority: str = "medium",
        due_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Create a new task.
        
        Args:
            title: Task title
            description: Optional task description
            category: User-defined category
            ai_generated_category: AI-generated category
            priority: Task priority (low, medium, high, urgent)
            due_date: Optional due date
            
        Returns:
            Dictionary with task details and success status
        """
        try:
            async for session in get_async_session():
                # Create new task
                task = TaskModel(
                    title=title,
                    description=description,
                    category=category,
                    ai_generated_category=ai_generated_category,
                    priority=priority,
                    due_date=due_date,
                    completed=False
                )
                
                session.add(task)
                await session.commit()
                await session.refresh(task)
                
                logger.info(f"Created task: {task.title}")
                
                return {
                    "success": True,
                    "task": {
                        "id": task.id,
                        "title": task.title,
                        "description": task.description,
                        "category": task.category,
                        "ai_generated_category": task.ai_generated_category,
                        "priority": task.priority,
                        "due_date": task.due_date.isoformat() if task.due_date else None,
                        "completed": task.completed,
                        "created_at": task.created_at.isoformat()
                    }
                }
                
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_tasks(
        self,
        category: Optional[str] = None,
        ai_category: Optional[str] = None,
        completed: Optional[bool] = None,
        priority: Optional[str] = None,
        limit: int = 100
    ) -> Dict[str, Any]:
        """
        Get tasks with optional filters.
        
        Args:
            category: Filter by user-defined category
            ai_category: Filter by AI-generated category
            completed: Filter by completion status
            priority: Filter by priority level
            limit: Maximum number of tasks to return
            
        Returns:
            Dictionary with tasks list and metadata
        """
        try:
            async for session in get_async_session():
                query = select(TaskModel)
                
                # Apply filters
                if category:
                    query = query.where(TaskModel.category == category)
                if ai_category:
                    query = query.where(TaskModel.ai_generated_category == ai_category)
                if completed is not None:
                    query = query.where(TaskModel.completed == completed)
                if priority:
                    query = query.where(TaskModel.priority == priority)
                
                # Order by priority and creation date
                priority_order = {
                    'urgent': 1,
                    'high': 2,
                    'medium': 3,
                    'low': 4
                }
                
                query = query.order_by(
                    TaskModel.completed,  # Incomplete tasks first
                    TaskModel.due_date.asc().nullslast(),  # Due date ascending
                    TaskModel.created_at.desc()  # Newest first
                ).limit(limit)
                
                result = await session.execute(query)
                tasks = result.scalars().all()
                
                return {
                    "success": True,
                    "tasks": [
                        {
                            "id": task.id,
                            "title": task.title,
                            "description": task.description,
                            "category": task.category,
                            "ai_generated_category": task.ai_generated_category,
                            "priority": task.priority,
                            "due_date": task.due_date.isoformat() if task.due_date else None,
                            "completed": task.completed,
                            "created_at": task.created_at.isoformat()
                        }
                        for task in tasks
                    ],
                    "count": len(tasks)
                }
                
        except Exception as e:
            logger.error(f"Error getting tasks: {e}")
            return {
                "success": False,
                "error": str(e),
                "tasks": []
            }
    
    async def update_task(
        self,
        task_id: int,
        title: Optional[str] = None,
        description: Optional[str] = None,
        category: Optional[str] = None,
        priority: Optional[str] = None,
        due_date: Optional[datetime] = None,
        completed: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        Update an existing task.
        
        Args:
            task_id: ID of task to update
            title: New title (optional)
            description: New description (optional)
            category: New category (optional)
            priority: New priority (optional)
            due_date: New due date (optional)
            completed: New completion status (optional)
            
        Returns:
            Dictionary with updated task details and success status
        """
        try:
            async for session in get_async_session():
                # Get existing task
                result = await session.execute(
                    select(TaskModel).where(TaskModel.id == task_id)
                )
                task = result.scalar_one_or_none()
                
                if not task:
                    return {
                        "success": False,
                        "error": "Task not found"
                    }
                
                # Update fields if provided
                if title is not None:
                    task.title = title
                if description is not None:
                    task.description = description
                if category is not None:
                    task.category = category
                if priority is not None:
                    task.priority = priority
                if due_date is not None:
                    task.due_date = due_date
                if completed is not None:
                    task.completed = completed
                
                await session.commit()
                await session.refresh(task)
                
                logger.info(f"Updated task: {task.title}")
                
                return {
                    "success": True,
                    "task": {
                        "id": task.id,
                        "title": task.title,
                        "description": task.description,
                        "category": task.category,
                        "ai_generated_category": task.ai_generated_category,
                        "priority": task.priority,
                        "due_date": task.due_date.isoformat() if task.due_date else None,
                        "completed": task.completed,
                        "updated_at": task.updated_at.isoformat()
                    }
                }
                
        except Exception as e:
            logger.error(f"Error updating task: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def delete_task(self, task_id: int) -> Dict[str, Any]:
        """
        Delete a task.
        
        Args:
            task_id: ID of task to delete
            
        Returns:
            Dictionary with success status
        """
        try:
            async for session in get_async_session():
                result = await session.execute(
                    select(TaskModel).where(TaskModel.id == task_id)
                )
                task = result.scalar_one_or_none()
                
                if not task:
                    return {
                        "success": False,
                        "error": "Task not found"
                    }
                
                await session.delete(task)
                await session.commit()
                
                logger.info(f"Deleted task: {task.title}")
                
                return {
                    "success": True,
                    "message": f"Task '{task.title}' deleted successfully"
                }
                
        except Exception as e:
            logger.error(f"Error deleting task: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_task_categories(self) -> Dict[str, Any]:
        """
        Get all unique task categories (both user-defined and AI-generated).
        
        Returns:
            Dictionary with categories list
        """
        try:
            async for session in get_async_session():
                # Get AI-generated categories
                ai_categories_result = await session.execute(
                    select(TaskModel.ai_generated_category)
                    .where(TaskModel.ai_generated_category.isnot(None))
                    .distinct()
                )
                ai_categories = [cat[0] for cat in ai_categories_result.fetchall()]
                
                # Get user-defined categories
                user_categories_result = await session.execute(
                    select(TaskModel.category)
                    .where(TaskModel.category.isnot(None))
                    .distinct()
                )
                user_categories = [cat[0] for cat in user_categories_result.fetchall()]
                
                return {
                    "success": True,
                    "ai_categories": sorted(ai_categories),
                    "user_categories": sorted(user_categories),
                    "all_categories": sorted(list(set(ai_categories + user_categories)))
                }
                
        except Exception as e:
            logger.error(f"Error getting task categories: {e}")
            return {
                "success": False,
                "error": str(e),
                "ai_categories": [],
                "user_categories": [],
                "all_categories": []
            }
