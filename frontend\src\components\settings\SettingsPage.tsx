import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Palette } from 'lucide-react';

const SettingsPage: React.FC = () => {
  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="flex items-center gap-3">
        <Settings className="w-8 h-8 text-primary-400" />
        <div>
          <h1 className="text-3xl font-bold text-white">Settings</h1>
          <p className="text-primary-300">Configure your AI dashboard</p>
        </div>
      </div>

      {/* Settings Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* AI Configuration */}
        <div className="card">
          <div className="flex items-center gap-3 mb-4">
            <Brain className="w-6 h-6 text-purple-400" />
            <h3 className="text-lg font-semibold text-primary-200">AI Configuration</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Default Model
              </label>
              <select className="input">
                <option>meta-llama/llama-3.2-3b-instruct:free</option>
                <option>meta-llama/llama-3.1-8b-instruct:free</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                API Keys
              </label>
              <input 
                type="password" 
                placeholder="OpenRouter API Key" 
                className="input mb-2"
              />
              <input 
                type="password" 
                placeholder="LangSearch API Key" 
                className="input"
              />
            </div>
          </div>
        </div>

        {/* Performance */}
        <div className="card">
          <div className="flex items-center gap-3 mb-4">
            <Zap className="w-6 h-6 text-warning" />
            <h3 className="text-lg font-semibold text-primary-200">Performance</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-primary-300">Enable Animations</span>
              <input type="checkbox" defaultChecked className="toggle" />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-primary-300">Reduced Motion</span>
              <input type="checkbox" className="toggle" />
            </div>
            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Animation Duration (ms)
              </label>
              <input 
                type="range" 
                min="100" 
                max="2000" 
                defaultValue="1000"
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Appearance */}
        <div className="card">
          <div className="flex items-center gap-3 mb-4">
            <Palette className="w-6 h-6 text-info" />
            <h3 className="text-lg font-semibold text-primary-200">Appearance</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Theme
              </label>
              <select className="input">
                <option>Dark (Default)</option>
                <option>Auto</option>
              </select>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-primary-300">Compact Mode</span>
              <input type="checkbox" className="toggle" />
            </div>
          </div>
        </div>

        {/* Data & Privacy */}
        <div className="card">
          <div className="flex items-center gap-3 mb-4">
            <Settings className="w-6 h-6 text-primary-400" />
            <h3 className="text-lg font-semibold text-primary-200">Data & Privacy</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-primary-300">Save Input History</span>
              <input type="checkbox" defaultChecked className="toggle" />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-primary-300">Analytics</span>
              <input type="checkbox" defaultChecked className="toggle" />
            </div>
            <button className="btn-danger w-full">
              Clear All Data
            </button>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <motion.button
          className="btn-primary px-8"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Save Settings
        </motion.button>
      </div>
    </motion.div>
  );
};

export default SettingsPage;
