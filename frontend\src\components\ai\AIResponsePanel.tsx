import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Brain, ChevronDown, ChevronUp, ExternalLink, Copy, Check } from 'lucide-react';

import { AIResponse, SearchResult } from '@/types';
import TypewriterText from './TypewriterText';
import SourceAttribution from './SourceAttribution';

interface AIResponsePanelProps {
  response: AIResponse;
  isVisible: boolean;
  onClose?: () => void;
}

const AIResponsePanel: React.FC<AIResponsePanelProps> = ({ 
  response, 
  isVisible, 
  onClose 
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['main']));
  const [copiedText, setCopiedText] = useState<string | null>(null);

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(id);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="bg-primary-800 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="p-6 border-b border-primary-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Brain className="w-6 h-6 text-info" />
              <div>
                <h2 className="text-xl font-semibold text-white">AI Response</h2>
                <p className="text-sm text-primary-400">
                  Generated {new Date(response.timestamp).toLocaleString()}
                </p>
              </div>
            </div>
            
            <motion.button
              className="p-2 hover:bg-primary-700 rounded-lg transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onClose}
            >
              <ChevronUp className="w-5 h-5 text-primary-300" />
            </motion.button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Main Response */}
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-primary-200">Answer</h3>
              <motion.button
                className="flex items-center gap-2 text-sm text-primary-400 hover:text-primary-300"
                whileHover={{ scale: 1.05 }}
                onClick={() => copyToClipboard(response.content, 'main')}
              >
                {copiedText === 'main' ? (
                  <Check className="w-4 h-4 text-success" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
                Copy
              </motion.button>
            </div>

            <div className="prose prose-invert max-w-none">
              <TypewriterText 
                text={response.content}
                speed={30}
                className="text-primary-200 leading-relaxed"
              />
            </div>

            {/* Confidence Score */}
            {response.confidence && (
              <div className="mt-4 p-3 bg-primary-700/30 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-primary-300">Confidence</span>
                  <span className="text-sm text-primary-400">
                    {Math.round(response.confidence * 100)}%
                  </span>
                </div>
                <div className="w-full bg-primary-700 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-error via-warning to-success h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${response.confidence * 100}%` }}
                    transition={{ duration: 1, delay: 0.5 }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Search Results */}
          {response.search_results && response.search_results.length > 0 && (
            <div className="border-t border-primary-700">
              <motion.button
                className="w-full p-4 flex items-center justify-between hover:bg-primary-800/50 transition-colors"
                onClick={() => toggleSection('search')}
              >
                <h3 className="font-semibold text-primary-200">
                  Search Results ({response.search_results.length})
                </h3>
                {expandedSections.has('search') ? (
                  <ChevronUp className="w-5 h-5 text-primary-400" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-primary-400" />
                )}
              </motion.button>

              <AnimatePresence>
                {expandedSections.has('search') && (
                  <motion.div
                    className="px-6 pb-6"
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="space-y-4">
                      {response.search_results.map((result, index) => (
                        <motion.div
                          key={index}
                          className="p-4 bg-primary-700/20 rounded-lg border border-primary-700/50"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <div className="flex items-start justify-between gap-4">
                            <div className="flex-1">
                              <h4 className="font-medium text-primary-200 mb-2">
                                {result.title}
                              </h4>
                              <p className="text-sm text-primary-400 mb-3">
                                {result.snippet}
                              </p>
                              {result.url && (
                                <a
                                  href={result.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="inline-flex items-center gap-1 text-sm text-info hover:text-info/80 transition-colors"
                                >
                                  <ExternalLink className="w-3 h-3" />
                                  View Source
                                </a>
                              )}
                            </div>
                            
                            {result.relevance_score && (
                              <div className="text-xs text-primary-500">
                                {Math.round(result.relevance_score * 100)}% match
                              </div>
                            )}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}

          {/* Sources */}
          {response.sources && response.sources.length > 0 && (
            <div className="border-t border-primary-700">
              <motion.button
                className="w-full p-4 flex items-center justify-between hover:bg-primary-800/50 transition-colors"
                onClick={() => toggleSection('sources')}
              >
                <h3 className="font-semibold text-primary-200">
                  Sources ({response.sources.length})
                </h3>
                {expandedSections.has('sources') ? (
                  <ChevronUp className="w-5 h-5 text-primary-400" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-primary-400" />
                )}
              </motion.button>

              <AnimatePresence>
                {expandedSections.has('sources') && (
                  <motion.div
                    className="px-6 pb-6"
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <SourceAttribution sources={response.sources} />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}

          {/* Metadata */}
          {response.metadata && (
            <div className="border-t border-primary-700">
              <motion.button
                className="w-full p-4 flex items-center justify-between hover:bg-primary-800/50 transition-colors"
                onClick={() => toggleSection('metadata')}
              >
                <h3 className="font-semibold text-primary-200">Details</h3>
                {expandedSections.has('metadata') ? (
                  <ChevronUp className="w-5 h-5 text-primary-400" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-primary-400" />
                )}
              </motion.button>

              <AnimatePresence>
                {expandedSections.has('metadata') && (
                  <motion.div
                    className="px-6 pb-6"
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      {Object.entries(response.metadata).map(([key, value]) => (
                        <div key={key}>
                          <span className="text-primary-400 capitalize">
                            {key.replace(/_/g, ' ')}:
                          </span>
                          <span className="text-primary-300 ml-2">
                            {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AIResponsePanel;
