"""Database search tool combining semantic search with traditional queries."""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from mirascope import BaseTool
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, text

from app.models.database import TaskModel, EventModel, UserInputModel
from app.tools.embedding_tool import EmbeddingTool
from app.database.connection import get_async_session

logger = logging.getLogger(__name__)


class DatabaseSearchTool(BaseTool):
    """Mirascope tool for intelligent database search."""
    
    def __init__(self):
        self.embedding_tool = EmbeddingTool()
    
    async def search_all_content(
        self,
        query: str,
        search_types: Optional[List[str]] = None,
        limit: int = 20,
        use_semantic: bool = True,
        use_keyword: bool = True
    ) -> Dict[str, Any]:
        """
        Search across all content types using both semantic and keyword search.
        
        Args:
            query: Search query
            search_types: Optional filter by content types ('task', 'event', 'input')
            limit: Maximum number of results
            use_semantic: Whether to use semantic search
            use_keyword: Whether to use keyword search
            
        Returns:
            Dictionary with combined search results
        """
        try:
            results = []
            
            # Semantic search if enabled
            if use_semantic:
                semantic_results = await self.embedding_tool.semantic_search(
                    query=query,
                    content_types=search_types,
                    limit=limit // 2 if use_keyword else limit
                )
                
                if semantic_results.get("success"):
                    for result in semantic_results["results"]:
                        results.append({
                            **result,
                            "search_type": "semantic",
                            "score": result["similarity"]
                        })
            
            # Keyword search if enabled
            if use_keyword:
                keyword_results = await self._keyword_search(
                    query=query,
                    search_types=search_types,
                    limit=limit // 2 if use_semantic else limit
                )
                
                if keyword_results.get("success"):
                    for result in keyword_results["results"]:
                        results.append({
                            **result,
                            "search_type": "keyword",
                            "score": result.get("relevance", 0.5)
                        })
            
            # Remove duplicates and sort by score
            unique_results = self._deduplicate_results(results)
            unique_results.sort(key=lambda x: x["score"], reverse=True)
            
            # Limit final results
            final_results = unique_results[:limit]
            
            return {
                "success": True,
                "query": query,
                "results": final_results,
                "total_results": len(final_results),
                "search_methods": {
                    "semantic": use_semantic,
                    "keyword": use_keyword
                }
            }
            
        except Exception as e:
            logger.error(f"Error in database search: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    async def search_tasks(
        self,
        query: str,
        category: Optional[str] = None,
        priority: Optional[str] = None,
        completed: Optional[bool] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Search tasks with filters.
        
        Args:
            query: Search query
            category: Optional category filter
            priority: Optional priority filter
            completed: Optional completion status filter
            limit: Maximum number of results
            
        Returns:
            Dictionary with task search results
        """
        try:
            async for session in get_async_session():
                # Build query
                db_query = select(TaskModel)
                
                # Text search
                if query:
                    search_filter = or_(
                        TaskModel.title.ilike(f"%{query}%"),
                        TaskModel.description.ilike(f"%{query}%"),
                        TaskModel.ai_generated_category.ilike(f"%{query}%")
                    )
                    db_query = db_query.where(search_filter)
                
                # Apply filters
                if category:
                    db_query = db_query.where(
                        or_(
                            TaskModel.category == category,
                            TaskModel.ai_generated_category == category
                        )
                    )
                if priority:
                    db_query = db_query.where(TaskModel.priority == priority)
                if completed is not None:
                    db_query = db_query.where(TaskModel.completed == completed)
                
                # Order and limit
                db_query = db_query.order_by(
                    TaskModel.completed,
                    TaskModel.due_date.asc().nullslast(),
                    TaskModel.created_at.desc()
                ).limit(limit)
                
                result = await session.execute(db_query)
                tasks = result.scalars().all()
                
                return {
                    "success": True,
                    "query": query,
                    "results": [
                        {
                            "id": task.id,
                            "title": task.title,
                            "description": task.description,
                            "category": task.category,
                            "ai_generated_category": task.ai_generated_category,
                            "priority": task.priority,
                            "due_date": task.due_date.isoformat() if task.due_date else None,
                            "completed": task.completed,
                            "created_at": task.created_at.isoformat(),
                            "content_type": "task"
                        }
                        for task in tasks
                    ],
                    "total_results": len(tasks)
                }
                
        except Exception as e:
            logger.error(f"Error searching tasks: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    async def search_events(
        self,
        query: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Search events with date filters.
        
        Args:
            query: Search query
            start_date: Optional start date filter
            end_date: Optional end date filter
            limit: Maximum number of results
            
        Returns:
            Dictionary with event search results
        """
        try:
            async for session in get_async_session():
                # Build query
                db_query = select(EventModel)
                
                # Text search
                if query:
                    search_filter = or_(
                        EventModel.title.ilike(f"%{query}%"),
                        EventModel.description.ilike(f"%{query}%"),
                        EventModel.location.ilike(f"%{query}%")
                    )
                    db_query = db_query.where(search_filter)
                
                # Date filters
                if start_date:
                    db_query = db_query.where(EventModel.start_time >= start_date)
                if end_date:
                    db_query = db_query.where(EventModel.start_time <= end_date)
                
                # Order and limit
                db_query = db_query.order_by(
                    EventModel.start_time.asc()
                ).limit(limit)
                
                result = await session.execute(db_query)
                events = result.scalars().all()
                
                return {
                    "success": True,
                    "query": query,
                    "results": [
                        {
                            "id": event.id,
                            "title": event.title,
                            "description": event.description,
                            "start_time": event.start_time.isoformat(),
                            "end_time": event.end_time.isoformat() if event.end_time else None,
                            "location": event.location,
                            "created_at": event.created_at.isoformat(),
                            "content_type": "event"
                        }
                        for event in events
                    ],
                    "total_results": len(events)
                }
                
        except Exception as e:
            logger.error(f"Error searching events: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    async def _keyword_search(
        self,
        query: str,
        search_types: Optional[List[str]] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """Perform keyword-based search across content types."""
        try:
            results = []
            
            # Search tasks if included
            if not search_types or "task" in search_types:
                task_results = await self.search_tasks(query, limit=limit // 3)
                if task_results.get("success"):
                    results.extend(task_results["results"])
            
            # Search events if included
            if not search_types or "event" in search_types:
                event_results = await self.search_events(query, limit=limit // 3)
                if event_results.get("success"):
                    results.extend(event_results["results"])
            
            # Search user inputs if included
            if not search_types or "input" in search_types:
                input_results = await self._search_user_inputs(query, limit=limit // 3)
                if input_results.get("success"):
                    results.extend(input_results["results"])
            
            return {
                "success": True,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Error in keyword search: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    async def _search_user_inputs(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """Search user input history."""
        try:
            async for session in get_async_session():
                db_query = select(UserInputModel).where(
                    UserInputModel.text.ilike(f"%{query}%")
                ).order_by(
                    UserInputModel.created_at.desc()
                ).limit(limit)
                
                result = await session.execute(db_query)
                inputs = result.scalars().all()
                
                return {
                    "success": True,
                    "results": [
                        {
                            "id": inp.id,
                            "text": inp.text,
                            "category": inp.category,
                            "confidence": inp.confidence,
                            "created_at": inp.created_at.isoformat(),
                            "content_type": "input"
                        }
                        for inp in inputs
                    ]
                }
                
        except Exception as e:
            logger.error(f"Error searching user inputs: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate results based on content type and ID."""
        seen = set()
        unique_results = []
        
        for result in results:
            key = (result.get("content_type"), result.get("id"))
            if key not in seen:
                seen.add(key)
                unique_results.append(result)
        
        return unique_results
