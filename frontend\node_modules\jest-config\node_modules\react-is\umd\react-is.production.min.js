/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
(function(){'use strict';(function(b,c){"object"===typeof exports&&"undefined"!==typeof module?c(exports):"function"===typeof define&&define.amd?define(["exports"],c):(b=b||self,c(b.ReactIs={}))})(this,function(b){function c(a){if("object"===typeof a&&null!==a){var b=a.$$typeof;switch(b){case q:switch(a=a.type,a){case d:case e:case f:case g:case h:return a;default:switch(a=a&&a.$$typeof,a){case t:case k:case l:case m:case n:case p:return a;default:return b}}case r:return b}}}var q=Symbol.for("react.element"),
r=Symbol.for("react.portal"),d=Symbol.for("react.fragment"),f=Symbol.for("react.strict_mode"),e=Symbol.for("react.profiler"),p=Symbol.for("react.provider"),k=Symbol.for("react.context"),t=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),n=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),u=Symbol.for("react.offscreen");var v=Symbol.for("react.module.reference");b.ContextConsumer=k;b.ContextProvider=p;b.Element=
q;b.ForwardRef=l;b.Fragment=d;b.Lazy=m;b.Memo=n;b.Portal=r;b.Profiler=e;b.StrictMode=f;b.Suspense=g;b.SuspenseList=h;b.isAsyncMode=function(a){return!1};b.isConcurrentMode=function(a){return!1};b.isContextConsumer=function(a){return c(a)===k};b.isContextProvider=function(a){return c(a)===p};b.isElement=function(a){return"object"===typeof a&&null!==a&&a.$$typeof===q};b.isForwardRef=function(a){return c(a)===l};b.isFragment=function(a){return c(a)===d};b.isLazy=function(a){return c(a)===m};b.isMemo=
function(a){return c(a)===n};b.isPortal=function(a){return c(a)===r};b.isProfiler=function(a){return c(a)===e};b.isStrictMode=function(a){return c(a)===f};b.isSuspense=function(a){return c(a)===g};b.isSuspenseList=function(a){return c(a)===h};b.isValidElementType=function(a){return"string"===typeof a||"function"===typeof a||a===d||a===e||a===f||a===g||a===h||a===u||"object"===typeof a&&null!==a&&(a.$$typeof===m||a.$$typeof===n||a.$$typeof===p||a.$$typeof===k||a.$$typeof===l||a.$$typeof===v||void 0!==
a.getModuleId)?!0:!1};b.typeOf=c});
})();
