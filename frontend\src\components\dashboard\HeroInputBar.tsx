import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Loader2, Sparkles } from 'lucide-react';

import { HeroInputFormData } from '@/types';
import { useAIOrchestrator } from '@/hooks/useAIOrchestrator';
import { usePersistence } from '@/hooks/usePersistence';
import VisualFeedback from './VisualFeedback';

interface HeroInputBarProps {
  onSubmit?: (input: string) => void;
}

const HeroInputBar: React.FC<HeroInputBarProps> = ({ onSubmit }) => {
  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm<HeroInputFormData>({
    defaultValues: { input: '' }
  });
  
  const { processInput, isProcessing, currentStep, processingSteps, lastResult, error } = useAIOrchestrator();
  const { saveInputState, loadInputState } = usePersistence();
  
  // CRITICAL: Persist input state across page refreshes
  const inputValue = watch('input');
  
  useEffect(() => {
    if (inputValue) {
      saveInputState(inputValue);
    }
  }, [inputValue, saveInputState]);
  
  useEffect(() => {
    const savedInput = loadInputState();
    if (savedInput) {
      setValue('input', savedInput);
    }
  }, [setValue, loadInputState]);

  const onFormSubmit = async (data: HeroInputFormData) => {
    if (!data.input.trim() || isProcessing) return;
    
    // Clear input and start processing with animations
    const inputText = data.input.trim();
    setValue('input', '');
    
    // Call the orchestrator
    await processInput(inputText);
    
    // Call optional callback
    if (onSubmit) {
      onSubmit(inputText);
    }
  };

  return (
    <motion.div
      className="w-full max-w-4xl mx-auto"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {/* Hero Section */}
      <div className="text-center mb-8">
        <motion.div
          className="flex items-center justify-center gap-2 mb-4"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 400, damping: 25 }}
        >
          <Sparkles className="w-8 h-8 text-primary-400" />
          <h1 className="text-4xl font-bold gradient-text">
            AI-Powered Intelligence
          </h1>
          <Sparkles className="w-8 h-8 text-primary-400" />
        </motion.div>
        
        <motion.p
          className="text-lg text-primary-300 max-w-2xl mx-auto"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          Enter anything - I'll intelligently organize it into tasks, events, or answer your questions
          with complete visual transparency.
        </motion.p>
      </div>

      {/* Input Form */}
      <form onSubmit={handleSubmit(onFormSubmit)} className="relative">
        <motion.div
          className="relative"
          whileFocus={{ scale: 1.01 }}
          transition={{ type: "spring", stiffness: 400, damping: 25 }}
        >
          <input
            {...register('input', { 
              required: 'Please enter something to process',
              minLength: { value: 1, message: 'Input cannot be empty' }
            })}
            placeholder="Enter anything - I'll intelligently organize it for you..."
            className="w-full px-6 py-4 pr-16 text-lg bg-primary-800/50 backdrop-blur-md border border-primary-700/50 rounded-2xl 
                     focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 focus:ring-offset-0
                     text-white placeholder-primary-400 transition-all duration-300
                     shadow-lg shadow-primary-900/20"
            disabled={isProcessing}
            autoComplete="off"
            autoFocus
          />
          
          {/* Submit Button */}
          <motion.button
            type="submit"
            disabled={isProcessing || !inputValue?.trim()}
            className="absolute right-2 top-1/2 -translate-y-1/2 p-3 bg-primary-600 hover:bg-primary-700 
                     disabled:bg-primary-800 disabled:cursor-not-allowed text-white rounded-xl 
                     transition-all duration-200 shadow-lg"
            whileHover={{ scale: isProcessing ? 1 : 1.05 }}
            whileTap={{ scale: isProcessing ? 1 : 0.95 }}
            transition={{ type: "spring", stiffness: 400, damping: 25 }}
          >
            <AnimatePresence mode="wait">
              {isProcessing ? (
                <motion.div
                  key="loading"
                  initial={{ opacity: 0, rotate: 0 }}
                  animate={{ opacity: 1, rotate: 360 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <Loader2 className="w-5 h-5 animate-spin" />
                </motion.div>
              ) : (
                <motion.div
                  key="send"
                  initial={{ opacity: 0, x: -5 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <Send className="w-5 h-5" />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.button>
        </motion.div>

        {/* Error Display */}
        <AnimatePresence>
          {errors.input && (
            <motion.div
              className="mt-2 text-error text-sm"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
            >
              {errors.input.message}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Visual Feedback Overlay */}
        <AnimatePresence>
          {isProcessing && (
            <VisualFeedback 
              currentStep={currentStep}
              processingSteps={processingSteps}
              isProcessing={isProcessing}
            />
          )}
        </AnimatePresence>
      </form>

      {/* Results Display */}
      <AnimatePresence>
        {lastResult && !isProcessing && (
          <motion.div
            className="mt-8 p-6 bg-primary-800/30 backdrop-blur-md border border-primary-700/50 rounded-2xl"
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            <div className="flex items-center gap-2 mb-4">
              <div className="w-2 h-2 bg-success rounded-full animate-pulse" />
              <h3 className="text-lg font-semibold text-primary-200">
                Processing Complete!
              </h3>
            </div>
            
            <div className="text-primary-300">
              <p className="mb-2">
                <span className="font-medium">Category:</span> {lastResult.category}
              </p>
              <p className="mb-2">
                <span className="font-medium">Confidence:</span> {(lastResult.confidence * 100).toFixed(1)}%
              </p>
              {lastResult.reasoning && (
                <p className="text-sm opacity-80">
                  <span className="font-medium">Reasoning:</span> {lastResult.reasoning}
                </p>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Display */}
      <AnimatePresence>
        {error && !isProcessing && (
          <motion.div
            className="mt-8 p-6 bg-error/10 border border-error/20 rounded-2xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-error rounded-full" />
              <h3 className="text-lg font-semibold text-error">
                Processing Error
              </h3>
            </div>
            <p className="text-error/80">{error}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Quick Examples */}
      <motion.div
        className="mt-8 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
      >
        <p className="text-sm text-primary-400 mb-3">Try these examples:</p>
        <div className="flex flex-wrap justify-center gap-2">
          {[
            "Do my biology homework",
            "Math exam at 2PM tomorrow",
            "What is photosynthesis?"
          ].map((example, index) => (
            <motion.button
              key={example}
              onClick={() => setValue('input', example)}
              className="px-3 py-1 text-xs bg-primary-800/50 hover:bg-primary-700/50 text-primary-300 
                       rounded-full transition-colors border border-primary-700/30"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 + index * 0.1 }}
            >
              {example}
            </motion.button>
          ))}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default HeroInputBar;
