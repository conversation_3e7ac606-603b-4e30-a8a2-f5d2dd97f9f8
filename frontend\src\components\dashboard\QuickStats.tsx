import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { CheckSquare, Calendar, Brain, TrendingUp } from 'lucide-react';

interface StatItem {
  label: string;
  value: string | number;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  change?: string;
  trend?: 'up' | 'down' | 'neutral';
}

const QuickStats: React.FC = () => {
  const [stats, setStats] = useState<StatItem[]>([
    {
      label: 'Active Tasks',
      value: '...',
      icon: CheckSquare,
      color: 'text-success',
      bgColor: 'bg-success/20',
      change: '+2 today',
      trend: 'up'
    },
    {
      label: 'Upcoming Events',
      value: '...',
      icon: Calendar,
      color: 'text-info',
      bgColor: 'bg-info/20',
      change: '3 this week',
      trend: 'neutral'
    },
    {
      label: 'AI Interactions',
      value: '...',
      icon: Brain,
      color: 'text-purple-400',
      bgColor: 'bg-purple-600/20',
      change: '+5 today',
      trend: 'up'
    },
    {
      label: 'Productivity Score',
      value: '...',
      icon: TrendingUp,
      color: 'text-warning',
      bgColor: 'bg-warning/20',
      change: '+12% this week',
      trend: 'up'
    }
  ]);

  useEffect(() => {
    // Simulate loading stats from API
    const loadStats = async () => {
      try {
        // In a real app, these would come from API calls
        const mockStats = [
          {
            label: 'Active Tasks',
            value: 12,
            icon: CheckSquare,
            color: 'text-success',
            bgColor: 'bg-success/20',
            change: '+2 today',
            trend: 'up' as const
          },
          {
            label: 'Upcoming Events',
            value: 5,
            icon: Calendar,
            color: 'text-info',
            bgColor: 'bg-info/20',
            change: '3 this week',
            trend: 'neutral' as const
          },
          {
            label: 'AI Interactions',
            value: 28,
            icon: Brain,
            color: 'text-purple-400',
            bgColor: 'bg-purple-600/20',
            change: '+5 today',
            trend: 'up' as const
          },
          {
            label: 'Productivity Score',
            value: '87%',
            icon: TrendingUp,
            color: 'text-warning',
            bgColor: 'bg-warning/20',
            change: '+12% this week',
            trend: 'up' as const
          }
        ];

        // Simulate API delay
        setTimeout(() => {
          setStats(mockStats);
        }, 1000);

      } catch (error) {
        console.error('Failed to load stats:', error);
      }
    };

    loadStats();
  }, []);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        
        return (
          <motion.div
            key={stat.label}
            className="card-hover"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ 
              delay: index * 0.1,
              type: "spring",
              stiffness: 300,
              damping: 30
            }}
            whileHover={{ y: -5 }}
          >
            {/* Icon and Value */}
            <div className="flex items-center justify-between mb-4">
              <motion.div
                className={`p-3 rounded-xl ${stat.bgColor}`}
                whileHover={{ scale: 1.1 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <Icon className={`w-6 h-6 ${stat.color}`} />
              </motion.div>
              
              <motion.div
                className="text-right"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: index * 0.1 + 0.3, type: "spring", stiffness: 400 }}
              >
                <div className="text-2xl font-bold text-primary-100">
                  {stat.value}
                </div>
              </motion.div>
            </div>

            {/* Label */}
            <div className="mb-3">
              <h3 className="text-sm font-medium text-primary-300">
                {stat.label}
              </h3>
            </div>

            {/* Change Indicator */}
            {stat.change && (
              <motion.div
                className="flex items-center gap-1"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 + 0.5 }}
              >
                <div className={`text-xs px-2 py-1 rounded-full ${
                  stat.trend === 'up' 
                    ? 'bg-success/20 text-success' 
                    : stat.trend === 'down'
                    ? 'bg-error/20 text-error'
                    : 'bg-primary-600/20 text-primary-300'
                }`}>
                  {stat.trend === 'up' && '↗'}
                  {stat.trend === 'down' && '↘'}
                  {stat.trend === 'neutral' && '→'}
                  {stat.change}
                </div>
              </motion.div>
            )}

            {/* Hover Effect Overlay */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-primary-600/0 to-primary-500/5 rounded-xl opacity-0"
              whileHover={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            />
          </motion.div>
        );
      })}
    </div>
  );
};

export default QuickStats;
