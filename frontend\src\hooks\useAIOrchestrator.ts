import { useState, useCallback, useRef } from 'react';
import { ProcessingUpdate, UserInput } from '@/types';
import { useWebSocket } from './useWebSocket';

interface AIOrchestratorState {
  isProcessing: boolean;
  currentStep: string;
  processingSteps: ProcessingUpdate[];
  lastResult: any;
  error: string | null;
}

export const useAIOrchestrator = () => {
  const [state, setState] = useState<AIOrchestratorState>({
    isProcessing: false,
    currentStep: '',
    processingSteps: [],
    lastResult: null,
    error: null
  });

  const { sendMessage, isConnected } = useWebSocket({
    onMessage: handleWebSocketMessage
  });

  const processingTimeoutRef = useRef<NodeJS.Timeout>();

  function handleWebSocketMessage(message: any) {
    if (message.type === 'processing_update') {
      setState(prev => ({
        ...prev,
        currentStep: message.step,
        processingSteps: [...prev.processingSteps, message],
        error: null
      }));

      // Handle completion
      if (message.step === 'complete' || message.step.includes('_complete') || message.step.includes('successfully')) {
        setState(prev => ({
          ...prev,
          isProcessing: false,
          lastResult: message.result || message,
          currentStep: ''
        }));
      }

      // Handle errors
      if (message.step === 'error') {
        setState(prev => ({
          ...prev,
          isProcessing: false,
          error: message.message || 'An error occurred',
          currentStep: ''
        }));
      }
    }
  }

  const processInput = useCallback(async (inputText: string) => {
    if (!inputText.trim()) return;

    // Clear previous state
    setState({
      isProcessing: true,
      currentStep: 'analyzing',
      processingSteps: [],
      lastResult: null,
      error: null
    });

    // Clear any existing timeout
    if (processingTimeoutRef.current) {
      clearTimeout(processingTimeoutRef.current);
    }

    try {
      if (isConnected) {
        // Send via WebSocket for real-time updates
        sendMessage({
          type: 'process_input',
          input: inputText
        });

        // Set timeout for processing
        processingTimeoutRef.current = setTimeout(() => {
          setState(prev => ({
            ...prev,
            isProcessing: false,
            error: 'Processing timeout - please try again',
            currentStep: ''
          }));
        }, 30000); // 30 second timeout

      } else {
        // Fallback to HTTP API
        await processViaHTTP(inputText);
      }

    } catch (error) {
      console.error('Error processing input:', error);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: error instanceof Error ? error.message : 'Failed to process input',
        currentStep: ''
      }));
    }
  }, [isConnected, sendMessage]);

  const processViaHTTP = async (inputText: string) => {
    try {
      const response = await fetch('/api/process-input', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: inputText,
          timestamp: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              handleWebSocketMessage({
                type: 'processing_update',
                ...data
              });
            } catch (e) {
              console.error('Error parsing SSE data:', e);
            }
          }
        }
      }

    } catch (error) {
      console.error('HTTP processing error:', error);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: error instanceof Error ? error.message : 'Failed to process input',
        currentStep: ''
      }));
    }
  };

  const clearResults = useCallback(() => {
    setState(prev => ({
      ...prev,
      lastResult: null,
      error: null,
      processingSteps: []
    }));
  }, []);

  const retryLastInput = useCallback(() => {
    if (state.processingSteps.length > 0) {
      const firstStep = state.processingSteps[0];
      // Extract original input from first processing step if available
      // This would need to be implemented based on how we store the original input
      console.log('Retry functionality would go here');
    }
  }, [state.processingSteps]);

  return {
    // State
    isProcessing: state.isProcessing,
    currentStep: state.currentStep,
    processingSteps: state.processingSteps,
    lastResult: state.lastResult,
    error: state.error,
    isConnected,

    // Actions
    processInput,
    clearResults,
    retryLastInput
  };
};
