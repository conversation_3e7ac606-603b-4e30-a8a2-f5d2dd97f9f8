const {
  emitForest,
  emitForestLines,
  parseFunctionRanges,
  parseOffsets,
} = require("./ascii");
const {
  cloneFunction<PERSON>ov,
  cloneProcess<PERSON>ov,
  clone<PERSON><PERSON>ov,
  cloneRangeCov,
} = require("./clone");
const {
  compareScriptCovs,
  compareFunctionCovs,
  compareRangeCovs,
} = require("./compare");
const {
  mergeFunctionCovs,
  mergeProcessCovs,
  mergeScriptCovs,
} = require("./merge");
const { RangeTree } = require("./range-tree");

module.exports = {
  emitForest,
  emitForestLines,
  parseFunctionRanges,
  parseOffsets,
  cloneFunctionCov,
  cloneProcessCov,
  clone<PERSON><PERSON>ov,
  cloneRangeCov,
  compareScriptCovs,
  compareFunctionCovs,
  compareRangeCovs,
  mergeFunctionCovs,
  mergeProcessCovs,
  mergeScriptCovs,
  RangeTree,
};
