{"version": 3, "file": "ast.js", "sourceRoot": "", "sources": ["../../src/ast.ts"], "names": [], "mappings": ";AAAA,8BAA8B;;;AAE9B,iEAAmD;AAEnD,+CAAwC;AAwCxC,MAAM,KAAK,GAAG,IAAI,GAAG,CAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;AAC7D,MAAM,aAAa,GAAG,CAAC,CAAS,EAAoB,EAAE,CACpD,KAAK,CAAC,GAAG,CAAC,CAAgB,CAAC,CAAA;AAE7B,iEAAiE;AACjE,gEAAgE;AAChE,0CAA0C;AAC1C,uEAAuE;AACvE,MAAM,gBAAgB,GAAG,2BAA2B,CAAA;AACpD,MAAM,UAAU,GAAG,SAAS,CAAA;AAE5B,uEAAuE;AACvE,qEAAqE;AACrE,qEAAqE;AACrE,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;AAC3C,0DAA0D;AAC1D,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAA;AACrC,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,iBAAiB,CAAC,CAAA;AAC7C,MAAM,YAAY,GAAG,CAAC,CAAS,EAAE,EAAE,CACjC,CAAC,CAAC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAA;AAE/C,gCAAgC;AAChC,MAAM,KAAK,GAAG,MAAM,CAAA;AAEpB,gCAAgC;AAChC,MAAM,IAAI,GAAG,KAAK,GAAG,IAAI,CAAA;AACzB,0EAA0E;AAC1E,sCAAsC;AACtC,MAAM,WAAW,GAAG,KAAK,GAAG,IAAI,CAAA;AAEhC,yEAAyE;AACzE,2DAA2D;AAE3D,MAAa,GAAG;IACd,IAAI,CAAoB;IACf,KAAK,CAAK;IAEnB,SAAS,CAAU;IACnB,MAAM,GAAY,KAAK,CAAA;IACvB,MAAM,GAAqB,EAAE,CAAA;IACpB,OAAO,CAAM;IACb,YAAY,CAAQ;IAC7B,KAAK,CAAO;IACZ,WAAW,GAAY,KAAK,CAAA;IAC5B,QAAQ,CAAkB;IAC1B,SAAS,CAAS;IAClB,kDAAkD;IAClD,uCAAuC;IACvC,SAAS,GAAY,KAAK,CAAA;IAE1B,YACE,IAAwB,EACxB,MAAY,EACZ,UAA4B,EAAE;QAE9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,kCAAkC;QAClC,IAAI,IAAI;YAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAA;QACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;QACrD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAA;QACnE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAA;QACxD,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;YAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;IACnE,CAAC;IAED,IAAI,QAAQ;QACV,qBAAqB;QACrB,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS;YAAE,OAAO,IAAI,CAAC,SAAS,CAAA;QACvD,oBAAoB;QACpB,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YAC3B,IAAI,OAAO,CAAC,KAAK,QAAQ;gBAAE,SAAQ;YACnC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ;gBAAE,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAA;SACzD;QACD,wEAAwE;QACxE,OAAO,IAAI,CAAC,SAAS,CAAA;IACvB,CAAC;IAED,2BAA2B;IAC3B,QAAQ;QACN,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS;YAAE,OAAO,IAAI,CAAC,SAAS,CAAA;QACvD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;SACnE;aAAM;YACL,OAAO,CAAC,IAAI,CAAC,SAAS;gBACpB,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAA;SACrE;IACH,CAAC;IAED,SAAS;QACP,qBAAqB;QACrB,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QACpE,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAA;QACjC,oBAAoB;QAEpB,wCAAwC;QACxC,IAAI,CAAC,QAAQ,EAAE,CAAA;QACf,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACvB,IAAI,CAAkB,CAAA;QACtB,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE;YAC7B,IAAI,CAAC,CAAC,IAAI,KAAK,GAAG;gBAAE,SAAQ;YAC5B,qEAAqE;YACrE,IAAI,CAAC,GAAoB,CAAC,CAAA;YAC1B,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAA;YAClB,OAAO,EAAE,EAAE;gBACT,KACE,IAAI,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,EAC1B,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,EAChC,CAAC,EAAE,EACH;oBACA,KAAK,MAAM,IAAI,IAAI,CAAC,CAAC,MAAM,EAAE;wBAC3B,qBAAqB;wBACrB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;4BAC5B,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;yBAChD;wBACD,oBAAoB;wBACpB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;qBAC1B;iBACF;gBACD,CAAC,GAAG,EAAE,CAAA;gBACN,EAAE,GAAG,CAAC,CAAC,OAAO,CAAA;aACf;SACF;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,CAAC,GAAG,KAAuB;QAC7B,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;YACrB,IAAI,CAAC,KAAK,EAAE;gBAAE,SAAQ;YACtB,qBAAqB;YACrB,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,EAAE;gBACtE,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAA;aACtC;YACD,oBAAoB;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;SACpB;IACH,CAAC;IAED,MAAM;QACJ,MAAM,GAAG,GACP,IAAI,CAAC,IAAI,KAAK,IAAI;YAChB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAE,CAAS,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;QAC/D,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QACjD,IACE,IAAI,CAAC,KAAK,EAAE;YACZ,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK;gBAClB,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC,EACzD;YACA,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SACb;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,OAAO;QACL,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI;YAAE,OAAO,IAAI,CAAA;QACpC,kDAAkD;QAClD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE;YAAE,OAAO,KAAK,CAAA;QAC1C,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC;YAAE,OAAO,IAAI,CAAA;QACxC,yEAAyE;QACzE,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAA;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE;YAC1C,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YACtB,IAAI,CAAC,CAAC,EAAE,YAAY,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;gBAC3C,OAAO,KAAK,CAAA;aACb;SACF;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI;YAAE,OAAO,IAAI,CAAA;QACpC,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,GAAG;YAAE,OAAO,IAAI,CAAA;QAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE;YAAE,OAAO,KAAK,CAAA;QACxC,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,CAAA;QAC5C,0CAA0C;QAC1C,qBAAqB;QACrB,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QACxD,oBAAoB;QACpB,OAAO,IAAI,CAAC,YAAY,KAAK,EAAE,GAAG,CAAC,CAAA;IACrC,CAAC;IAED,MAAM,CAAC,IAAkB;QACvB,IAAI,OAAO,IAAI,KAAK,QAAQ;YAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;;YACxC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAClC,CAAC;IAED,KAAK,CAAC,MAAW;QACf,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QACpC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YAC3B,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;SACZ;QACD,OAAO,CAAC,CAAA;IACV,CAAC;IAED,MAAM,CAAC,SAAS,CACd,GAAW,EACX,GAAQ,EACR,GAAW,EACX,GAAqB;QAErB,IAAI,QAAQ,GAAG,KAAK,CAAA;QACpB,IAAI,OAAO,GAAG,KAAK,CAAA;QACnB,IAAI,UAAU,GAAG,CAAC,CAAC,CAAA;QACnB,IAAI,QAAQ,GAAG,KAAK,CAAA;QACpB,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE;YACrB,qDAAqD;YACrD,IAAI,CAAC,GAAG,GAAG,CAAA;YACX,IAAI,GAAG,GAAG,EAAE,CAAA;YACZ,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE;gBACrB,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAA;gBACzB,2DAA2D;gBAC3D,0BAA0B;gBAC1B,IAAI,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;oBAC1B,QAAQ,GAAG,CAAC,QAAQ,CAAA;oBACpB,GAAG,IAAI,CAAC,CAAA;oBACR,SAAQ;iBACT;gBAED,IAAI,OAAO,EAAE;oBACX,IAAI,CAAC,KAAK,UAAU,GAAG,CAAC,EAAE;wBACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE;4BAC1B,QAAQ,GAAG,IAAI,CAAA;yBAChB;qBACF;yBAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,GAAG,CAAC,IAAI,QAAQ,CAAC,EAAE;wBAC3D,OAAO,GAAG,KAAK,CAAA;qBAChB;oBACD,GAAG,IAAI,CAAC,CAAA;oBACR,SAAQ;iBACT;qBAAM,IAAI,CAAC,KAAK,GAAG,EAAE;oBACpB,OAAO,GAAG,IAAI,CAAA;oBACd,UAAU,GAAG,CAAC,CAAA;oBACd,QAAQ,GAAG,KAAK,CAAA;oBAChB,GAAG,IAAI,CAAC,CAAA;oBACR,SAAQ;iBACT;gBAED,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAC3D,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;oBACb,GAAG,GAAG,EAAE,CAAA;oBACR,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;oBAC3B,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA;oBACnC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;oBACb,SAAQ;iBACT;gBACD,GAAG,IAAI,CAAC,CAAA;aACT;YACD,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,OAAO,CAAC,CAAA;SACT;QAED,wCAAwC;QACxC,uBAAuB;QACvB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;QACf,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QAC7B,MAAM,KAAK,GAAU,EAAE,CAAA;QACvB,IAAI,GAAG,GAAG,EAAE,CAAA;QACZ,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE;YACrB,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAA;YACzB,2DAA2D;YAC3D,0BAA0B;YAC1B,IAAI,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;gBAC1B,QAAQ,GAAG,CAAC,QAAQ,CAAA;gBACpB,GAAG,IAAI,CAAC,CAAA;gBACR,SAAQ;aACT;YAED,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,KAAK,UAAU,GAAG,CAAC,EAAE;oBACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE;wBAC1B,QAAQ,GAAG,IAAI,CAAA;qBAChB;iBACF;qBAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,GAAG,CAAC,IAAI,QAAQ,CAAC,EAAE;oBAC3D,OAAO,GAAG,KAAK,CAAA;iBAChB;gBACD,GAAG,IAAI,CAAC,CAAA;gBACR,SAAQ;aACT;iBAAM,IAAI,CAAC,KAAK,GAAG,EAAE;gBACpB,OAAO,GAAG,IAAI,CAAA;gBACd,UAAU,GAAG,CAAC,CAAA;gBACd,QAAQ,GAAG,KAAK,CAAA;gBAChB,GAAG,IAAI,CAAC,CAAA;gBACR,SAAQ;aACT;YAED,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC7C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACd,GAAG,GAAG,EAAE,CAAA;gBACR,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACd,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA;gBACnC,SAAQ;aACT;YACD,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACd,GAAG,GAAG,EAAE,CAAA;gBACR,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAChB,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;gBACzB,SAAQ;aACT;YACD,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,IAAI,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAA;iBACrB;gBACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACd,GAAG,GAAG,EAAE,CAAA;gBACR,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,CAAA;gBACxB,OAAO,CAAC,CAAA;aACT;YACD,GAAG,IAAI,CAAC,CAAA;SACT;QAED,qBAAqB;QACrB,kEAAkE;QAClE,iCAAiC;QACjC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAA;QACf,GAAG,CAAC,SAAS,GAAG,SAAS,CAAA;QACzB,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;QACrC,OAAO,CAAC,CAAA;IACV,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,OAAe,EAAE,UAA4B,EAAE;QAC7D,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;QAC7C,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,CAAA;QACvC,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,oEAAoE;IACpE,iBAAiB;IACjB,WAAW;QACT,gCAAgC;QAChC,qBAAqB;QACrB,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAA;QACxD,oBAAoB;QACpB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;QAC5B,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;QACzD,+DAA+D;QAC/D,mEAAmE;QACnE,sCAAsC;QACtC,MAAM,QAAQ,GACZ,QAAQ;YACR,IAAI,CAAC,SAAS;YACd,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM;gBACnB,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe;gBAC9B,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,CAAA;QAC9C,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,IAAI,CAAA;SACZ;QAED,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QACpE,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE;YACjD,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,IAAI;SACZ,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAA;IACtB,CAAC;IAED,qEAAqE;IACrE,qEAAqE;IACrE,yEAAyE;IACzE,sEAAsE;IACtE,qEAAqE;IACrE,wEAAwE;IACxE,oEAAoE;IACpE,0DAA0D;IAC1D,EAAE;IACF,uCAAuC;IACvC,4BAA4B;IAC5B,wDAAwD;IACxD,uCAAuC;IACvC,8CAA8C;IAC9C,UAAU;IACV,4BAA4B;IAC5B,YAAY;IACZ,EAAE;IACF,mEAAmE;IACnE,wBAAwB;IACxB,iDAAiD;IACjD,8BAA8B;IAC9B,8DAA8D;IAC9D,uCAAuC;IACvC,8CAA8C;IAC9C,UAAU;IACV,gDAAgD;IAChD,iBAAiB;IACjB,EAAE;IACF,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,eAAe;IACf,EAAE;IACF,wEAAwE;IACxE,4DAA4D;IAC5D,iEAAiE;IACjE,4BAA4B;IAC5B,8DAA8D;IAC9D,6CAA6C;IAC7C,oDAAoD;IACpD,EAAE;IACF,uEAAuE;IACvE,gEAAgE;IAChE,EAAE;IACF,sEAAsE;IACtE,qCAAqC;IACrC,EAAE;IACF,0EAA0E;IAC1E,2EAA2E;IAC3E,EAAE;IACF,kBAAkB;IAClB,+CAA+C;IAC/C,4CAA4C;IAC5C,uEAAuE;IACvE,EAAE;IACF,6EAA6E;IAC7E,0EAA0E;IAC1E,sEAAsE;IACtE,sCAAsC;IACtC,EAAE;IACF,yEAAyE;IACzE,oEAAoE;IACpE,0CAA0C;IAC1C,EAAE;IACF,2BAA2B;IAC3B,sEAAsE;IACtE,qEAAqE;IACrE,uEAAuE;IACvE,cAAc,CACZ,QAAkB;QAElB,MAAM,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAA;QAC3C,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI;YAAE,IAAI,CAAC,SAAS,EAAE,CAAA;QACzC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE,CAAA;YAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM;iBACpB,GAAG,CAAC,CAAC,CAAC,EAAE;gBACP,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,GAC5B,OAAO,CAAC,KAAK,QAAQ;oBACnB,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC;oBAC5C,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;gBAChC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAA;gBAC3C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,CAAA;gBAClC,OAAO,EAAE,CAAA;YACX,CAAC,CAAC;iBACD,IAAI,CAAC,EAAE,CAAC,CAAA;YAEX,IAAI,KAAK,GAAG,EAAE,CAAA;YACd,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;gBAClB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;oBACtC,+DAA+D;oBAC/D,+CAA+C;oBAE/C,gEAAgE;oBAChE,+CAA+C;oBAC/C,MAAM,cAAc,GAClB,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC1D,IAAI,CAAC,cAAc,EAAE;wBACnB,MAAM,GAAG,GAAG,eAAe,CAAA;wBAC3B,sDAAsD;wBACtD,oBAAoB;wBACpB,MAAM,UAAU;wBACd,uDAAuD;wBACvD,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;4BAC/B,8CAA8C;4BAC9C,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;4BACjD,gDAAgD;4BAChD,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;wBACtD,2DAA2D;wBAC3D,4CAA4C;wBAC5C,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;wBAE7D,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;qBACpE;iBACF;aACF;YAED,6DAA6D;YAC7D,IAAI,GAAG,GAAG,EAAE,CAAA;YACZ,IACE,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,WAAW;gBACtB,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,GAAG,EAC1B;gBACA,GAAG,GAAG,WAAW,CAAA;aAClB;YACD,MAAM,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAA;YAC/B,OAAO;gBACL,KAAK;gBACL,IAAA,sBAAQ,EAAC,GAAG,CAAC;gBACb,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;gBACnC,IAAI,CAAC,MAAM;aACZ,CAAA;SACF;QAED,iEAAiE;QACjE,iEAAiE;QACjE,oCAAoC;QAEpC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,CAAA;QACvD,uBAAuB;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAA;QACrD,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;QAEnC,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE;YAChE,mEAAmE;YACnE,2BAA2B;YAC3B,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;YACzB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAA;YACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;YAChB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;YAC1B,OAAO,CAAC,CAAC,EAAE,IAAA,sBAAQ,EAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;SACpD;QAED,mCAAmC;QACnC,IAAI,cAAc,GAChB,CAAC,QAAQ,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC,UAAU;YACzC,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAC/B,IAAI,cAAc,KAAK,IAAI,EAAE;YAC3B,cAAc,GAAG,EAAE,CAAA;SACpB;QACD,IAAI,cAAc,EAAE;YAClB,IAAI,GAAG,MAAM,IAAI,OAAO,cAAc,KAAK,CAAA;SAC5C;QAED,sDAAsD;QACtD,IAAI,KAAK,GAAG,EAAE,CAAA;QACd,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YACvC,KAAK,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,WAAW,CAAA;SACjE;aAAM;YACL,MAAM,KAAK,GACT,IAAI,CAAC,IAAI,KAAK,GAAG;gBACf,CAAC,CAAC,iDAAiD;oBACjD,IAAI;wBACJ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;wBACvD,IAAI;wBACJ,GAAG;gBACL,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG;oBACnB,CAAC,CAAC,GAAG;oBACL,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG;wBACnB,CAAC,CAAC,IAAI;wBACN,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,cAAc;4BACrC,CAAC,CAAC,GAAG;4BACL,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,cAAc;gCACrC,CAAC,CAAC,IAAI;gCACN,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAA;YACrB,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAA;SAC7B;QACD,OAAO;YACL,KAAK;YACL,IAAA,sBAAQ,EAAC,IAAI,CAAC;YACd,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;YACnC,IAAI,CAAC,MAAM;SACZ,CAAA;IACH,CAAC;IAED,cAAc,CAAC,GAAY;QACzB,OAAO,IAAI,CAAC,MAAM;aACf,GAAG,CAAC,CAAC,CAAC,EAAE;YACP,+CAA+C;YAC/C,qBAAqB;YACrB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;aAChD;YACD,oBAAoB;YACpB,iEAAiE;YACjE,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;YACvD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,CAAA;YAClC,OAAO,EAAE,CAAA;QACX,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACrD,IAAI,CAAC,GAAG,CAAC,CAAA;IACd,CAAC;IAED,MAAM,CAAC,UAAU,CACf,IAAY,EACZ,QAA6B,EAC7B,UAAmB,KAAK;QAExB,IAAI,QAAQ,GAAG,KAAK,CAAA;QACpB,IAAI,EAAE,GAAG,EAAE,CAAA;QACX,IAAI,KAAK,GAAG,KAAK,CAAA;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YACxB,IAAI,QAAQ,EAAE;gBACZ,QAAQ,GAAG,KAAK,CAAA;gBAChB,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;gBACzC,SAAQ;aACT;YACD,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;oBACzB,EAAE,IAAI,MAAM,CAAA;iBACb;qBAAM;oBACL,QAAQ,GAAG,IAAI,CAAA;iBAChB;gBACD,SAAQ;aACT;YACD,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,GAAG,IAAA,iCAAU,EAAC,IAAI,EAAE,CAAC,CAAC,CAAA;gBAC7D,IAAI,QAAQ,EAAE;oBACZ,EAAE,IAAI,GAAG,CAAA;oBACT,KAAK,GAAG,KAAK,IAAI,SAAS,CAAA;oBAC1B,CAAC,IAAI,QAAQ,GAAG,CAAC,CAAA;oBACjB,QAAQ,GAAG,QAAQ,IAAI,KAAK,CAAA;oBAC5B,SAAQ;iBACT;aACF;YACD,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,IAAI,OAAO,IAAI,IAAI,KAAK,GAAG;oBAAE,EAAE,IAAI,WAAW,CAAA;;oBACzC,EAAE,IAAI,IAAI,CAAA;gBACf,QAAQ,GAAG,IAAI,CAAA;gBACf,SAAQ;aACT;YACD,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,EAAE,IAAI,KAAK,CAAA;gBACX,QAAQ,GAAG,IAAI,CAAA;gBACf,SAAQ;aACT;YACD,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,CAAA;SACtB;QACD,OAAO,CAAC,EAAE,EAAE,IAAA,sBAAQ,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;IAChD,CAAC;CACF;AA/kBD,kBA+kBC", "sourcesContent": ["// parse a single path portion\n\nimport { parseClass } from './brace-expressions.js'\nimport { MinimatchOptions, MMRegExp } from './index.js'\nimport { unescape } from './unescape.js'\n\n// classes [] are handled by the parseClass method\n// for positive extglobs, we sub-parse the contents, and combine,\n// with the appropriate regexp close.\n// for negative extglobs, we sub-parse the contents, but then\n// have to include the rest of the pattern, then the parent, etc.,\n// as the thing that cannot be because RegExp negative lookaheads\n// are different from globs.\n//\n// So for example:\n// a@(i|w!(x|y)z|j)b => ^a(i|w((!?(x|y)zb).*)z|j)b$\n//   1   2 3   4 5 6      1   2    3   46      5 6\n//\n// Assembling the extglob requires not just the negated patterns themselves,\n// but also anything following the negative patterns up to the boundary\n// of the current pattern, plus anything following in the parent pattern.\n//\n//\n// So, first, we parse the string into an AST of extglobs, without turning\n// anything into regexps yet.\n//\n// ['a', {@ [['i'], ['w', {!['x', 'y']}, 'z'], ['j']]}, 'b']\n//\n// Then, for all the negative extglobs, we append whatever comes after in\n// each parent as their tail\n//\n// ['a', {@ [['i'], ['w', {!['x', 'y'], 'z', 'b'}, 'z'], ['j']]}, 'b']\n//\n// Lastly, we turn each of these pieces into a regexp, and join\n//\n//                                 v----- .* because there's more following,\n//                                 v    v  otherwise, .+ because it must be\n//                                 v    v  *something* there.\n// ['^a', {@ ['i', 'w(?:(!?(?:x|y).*zb$).*)z', 'j' ]}, 'b$']\n//   copy what follows into here--^^^^^\n// ['^a', '(?:i|w(?:(?!(?:x|y).*zb$).*)z|j)', 'b$']\n// ['^a(?:i|w(?:(?!(?:x|y).*zb$).*)z|j)b$']\n\nexport type ExtglobType = '!' | '?' | '+' | '*' | '@'\nconst types = new Set<ExtglobType>(['!', '?', '+', '*', '@'])\nconst isExtglobType = (c: string): c is ExtglobType =>\n  types.has(c as ExtglobType)\n\n// Patterns that get prepended to bind to the start of either the\n// entire string, or just a single path portion, to prevent dots\n// and/or traversal patterns, when needed.\n// Exts don't need the ^ or / bit, because the root binds that already.\nconst startNoTraversal = '(?!(?:^|/)\\\\.\\\\.?(?:$|/))'\nconst startNoDot = '(?!\\\\.)'\n\n// characters that indicate a start of pattern needs the \"no dots\" bit,\n// because a dot *might* be matched. ( is not in the list, because in\n// the case of a child extglob, it will handle the prevention itself.\nconst addPatternStart = new Set(['[', '.'])\n// cases where traversal is A-OK, no dot prevention needed\nconst justDots = new Set(['..', '.'])\nconst reSpecials = new Set('().*{}+?[]^$\\\\!')\nconst regExpEscape = (s: string) =>\n  s.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&')\n\n// any single thing other than /\nconst qmark = '[^/]'\n\n// * => any number of characters\nconst star = qmark + '*?'\n// use + when we need to ensure that *something* matches, because the * is\n// the only thing in the path portion.\nconst starNoEmpty = qmark + '+?'\n\n// remove the \\ chars that we added if we end up doing a nonmagic compare\n// const deslash = (s: string) => s.replace(/\\\\(.)/g, '$1')\n\nexport class AST {\n  type: ExtglobType | null\n  readonly #root: AST\n\n  #hasMagic?: boolean\n  #uflag: boolean = false\n  #parts: (string | AST)[] = []\n  readonly #parent?: AST\n  readonly #parentIndex: number\n  #negs: AST[]\n  #filledNegs: boolean = false\n  #options: MinimatchOptions\n  #toString?: string\n  // set to true if it's an extglob with no children\n  // (which really means one child of '')\n  #emptyExt: boolean = false\n\n  constructor(\n    type: ExtglobType | null,\n    parent?: AST,\n    options: MinimatchOptions = {}\n  ) {\n    this.type = type\n    // extglobs are inherently magical\n    if (type) this.#hasMagic = true\n    this.#parent = parent\n    this.#root = this.#parent ? this.#parent.#root : this\n    this.#options = this.#root === this ? options : this.#root.#options\n    this.#negs = this.#root === this ? [] : this.#root.#negs\n    if (type === '!' && !this.#root.#filledNegs) this.#negs.push(this)\n    this.#parentIndex = this.#parent ? this.#parent.#parts.length : 0\n  }\n\n  get hasMagic(): boolean | undefined {\n    /* c8 ignore start */\n    if (this.#hasMagic !== undefined) return this.#hasMagic\n    /* c8 ignore stop */\n    for (const p of this.#parts) {\n      if (typeof p === 'string') continue\n      if (p.type || p.hasMagic) return (this.#hasMagic = true)\n    }\n    // note: will be undefined until we generate the regexp src and find out\n    return this.#hasMagic\n  }\n\n  // reconstructs the pattern\n  toString(): string {\n    if (this.#toString !== undefined) return this.#toString\n    if (!this.type) {\n      return (this.#toString = this.#parts.map(p => String(p)).join(''))\n    } else {\n      return (this.#toString =\n        this.type + '(' + this.#parts.map(p => String(p)).join('|') + ')')\n    }\n  }\n\n  #fillNegs() {\n    /* c8 ignore start */\n    if (this !== this.#root) throw new Error('should only call on root')\n    if (this.#filledNegs) return this\n    /* c8 ignore stop */\n\n    // call toString() once to fill this out\n    this.toString()\n    this.#filledNegs = true\n    let n: AST | undefined\n    while ((n = this.#negs.pop())) {\n      if (n.type !== '!') continue\n      // walk up the tree, appending everthing that comes AFTER parentIndex\n      let p: AST | undefined = n\n      let pp = p.#parent\n      while (pp) {\n        for (\n          let i = p.#parentIndex + 1;\n          !pp.type && i < pp.#parts.length;\n          i++\n        ) {\n          for (const part of n.#parts) {\n            /* c8 ignore start */\n            if (typeof part === 'string') {\n              throw new Error('string part in extglob AST??')\n            }\n            /* c8 ignore stop */\n            part.copyIn(pp.#parts[i])\n          }\n        }\n        p = pp\n        pp = p.#parent\n      }\n    }\n    return this\n  }\n\n  push(...parts: (string | AST)[]) {\n    for (const p of parts) {\n      if (p === '') continue\n      /* c8 ignore start */\n      if (typeof p !== 'string' && !(p instanceof AST && p.#parent === this)) {\n        throw new Error('invalid part: ' + p)\n      }\n      /* c8 ignore stop */\n      this.#parts.push(p)\n    }\n  }\n\n  toJSON() {\n    const ret: any[] =\n      this.type === null\n        ? this.#parts.slice().map(p => (typeof p === 'string' ? p : p.toJSON()))\n        : [this.type, ...this.#parts.map(p => (p as AST).toJSON())]\n    if (this.isStart() && !this.type) ret.unshift([])\n    if (\n      this.isEnd() &&\n      (this === this.#root ||\n        (this.#root.#filledNegs && this.#parent?.type === '!'))\n    ) {\n      ret.push({})\n    }\n    return ret\n  }\n\n  isStart(): boolean {\n    if (this.#root === this) return true\n    // if (this.type) return !!this.#parent?.isStart()\n    if (!this.#parent?.isStart()) return false\n    if (this.#parentIndex === 0) return true\n    // if everything AHEAD of this is a negation, then it's still the \"start\"\n    const p = this.#parent\n    for (let i = 0; i < this.#parentIndex; i++) {\n      const pp = p.#parts[i]\n      if (!(pp instanceof AST && pp.type === '!')) {\n        return false\n      }\n    }\n    return true\n  }\n\n  isEnd(): boolean {\n    if (this.#root === this) return true\n    if (this.#parent?.type === '!') return true\n    if (!this.#parent?.isEnd()) return false\n    if (!this.type) return this.#parent?.isEnd()\n    // if not root, it'll always have a parent\n    /* c8 ignore start */\n    const pl = this.#parent ? this.#parent.#parts.length : 0\n    /* c8 ignore stop */\n    return this.#parentIndex === pl - 1\n  }\n\n  copyIn(part: AST | string) {\n    if (typeof part === 'string') this.push(part)\n    else this.push(part.clone(this))\n  }\n\n  clone(parent: AST) {\n    const c = new AST(this.type, parent)\n    for (const p of this.#parts) {\n      c.copyIn(p)\n    }\n    return c\n  }\n\n  static #parseAST(\n    str: string,\n    ast: AST,\n    pos: number,\n    opt: MinimatchOptions\n  ): number {\n    let escaping = false\n    let inBrace = false\n    let braceStart = -1\n    let braceNeg = false\n    if (ast.type === null) {\n      // outside of a extglob, append until we find a start\n      let i = pos\n      let acc = ''\n      while (i < str.length) {\n        const c = str.charAt(i++)\n        // still accumulate escapes at this point, but we do ignore\n        // starts that are escaped\n        if (escaping || c === '\\\\') {\n          escaping = !escaping\n          acc += c\n          continue\n        }\n\n        if (inBrace) {\n          if (i === braceStart + 1) {\n            if (c === '^' || c === '!') {\n              braceNeg = true\n            }\n          } else if (c === ']' && !(i === braceStart + 2 && braceNeg)) {\n            inBrace = false\n          }\n          acc += c\n          continue\n        } else if (c === '[') {\n          inBrace = true\n          braceStart = i\n          braceNeg = false\n          acc += c\n          continue\n        }\n\n        if (!opt.noext && isExtglobType(c) && str.charAt(i) === '(') {\n          ast.push(acc)\n          acc = ''\n          const ext = new AST(c, ast)\n          i = AST.#parseAST(str, ext, i, opt)\n          ast.push(ext)\n          continue\n        }\n        acc += c\n      }\n      ast.push(acc)\n      return i\n    }\n\n    // some kind of extglob, pos is at the (\n    // find the next | or )\n    let i = pos + 1\n    let part = new AST(null, ast)\n    const parts: AST[] = []\n    let acc = ''\n    while (i < str.length) {\n      const c = str.charAt(i++)\n      // still accumulate escapes at this point, but we do ignore\n      // starts that are escaped\n      if (escaping || c === '\\\\') {\n        escaping = !escaping\n        acc += c\n        continue\n      }\n\n      if (inBrace) {\n        if (i === braceStart + 1) {\n          if (c === '^' || c === '!') {\n            braceNeg = true\n          }\n        } else if (c === ']' && !(i === braceStart + 2 && braceNeg)) {\n          inBrace = false\n        }\n        acc += c\n        continue\n      } else if (c === '[') {\n        inBrace = true\n        braceStart = i\n        braceNeg = false\n        acc += c\n        continue\n      }\n\n      if (isExtglobType(c) && str.charAt(i) === '(') {\n        part.push(acc)\n        acc = ''\n        const ext = new AST(c, part)\n        part.push(ext)\n        i = AST.#parseAST(str, ext, i, opt)\n        continue\n      }\n      if (c === '|') {\n        part.push(acc)\n        acc = ''\n        parts.push(part)\n        part = new AST(null, ast)\n        continue\n      }\n      if (c === ')') {\n        if (acc === '' && ast.#parts.length === 0) {\n          ast.#emptyExt = true\n        }\n        part.push(acc)\n        acc = ''\n        ast.push(...parts, part)\n        return i\n      }\n      acc += c\n    }\n\n    // unfinished extglob\n    // if we got here, it was a malformed extglob! not an extglob, but\n    // maybe something else in there.\n    ast.type = null\n    ast.#hasMagic = undefined\n    ast.#parts = [str.substring(pos - 1)]\n    return i\n  }\n\n  static fromGlob(pattern: string, options: MinimatchOptions = {}) {\n    const ast = new AST(null, undefined, options)\n    AST.#parseAST(pattern, ast, 0, options)\n    return ast\n  }\n\n  // returns the regular expression if there's magic, or the unescaped\n  // string if not.\n  toMMPattern(): MMRegExp | string {\n    // should only be called on root\n    /* c8 ignore start */\n    if (this !== this.#root) return this.#root.toMMPattern()\n    /* c8 ignore stop */\n    const glob = this.toString()\n    const [re, body, hasMagic, uflag] = this.toRegExpSource()\n    // if we're in nocase mode, and not nocaseMagicOnly, then we do\n    // still need a regular expression if we have to case-insensitively\n    // match capital/lowercase characters.\n    const anyMagic =\n      hasMagic ||\n      this.#hasMagic ||\n      (this.#options.nocase &&\n        !this.#options.nocaseMagicOnly &&\n        glob.toUpperCase() !== glob.toLowerCase())\n    if (!anyMagic) {\n      return body\n    }\n\n    const flags = (this.#options.nocase ? 'i' : '') + (uflag ? 'u' : '')\n    return Object.assign(new RegExp(`^${re}$`, flags), {\n      _src: re,\n      _glob: glob,\n    })\n  }\n\n  get options() {\n    return this.#options\n  }\n\n  // returns the string match, the regexp source, whether there's magic\n  // in the regexp (so a regular expression is required) and whether or\n  // not the uflag is needed for the regular expression (for posix classes)\n  // TODO: instead of injecting the start/end at this point, just return\n  // the BODY of the regexp, along with the start/end portions suitable\n  // for binding the start/end in either a joined full-path makeRe context\n  // (where we bind to (^|/), or a standalone matchPart context (where\n  // we bind to ^, and not /).  Otherwise slashes get duped!\n  //\n  // In part-matching mode, the start is:\n  // - if not isStart: nothing\n  // - if traversal possible, but not allowed: ^(?!\\.\\.?$)\n  // - if dots allowed or not possible: ^\n  // - if dots possible and not allowed: ^(?!\\.)\n  // end is:\n  // - if not isEnd(): nothing\n  // - else: $\n  //\n  // In full-path matching mode, we put the slash at the START of the\n  // pattern, so start is:\n  // - if first pattern: same as part-matching mode\n  // - if not isStart(): nothing\n  // - if traversal possible, but not allowed: /(?!\\.\\.?(?:$|/))\n  // - if dots allowed or not possible: /\n  // - if dots possible and not allowed: /(?!\\.)\n  // end is:\n  // - if last pattern, same as part-matching mode\n  // - else nothing\n  //\n  // Always put the (?:$|/) on negated tails, though, because that has to be\n  // there to bind the end of the negated pattern portion, and it's easier to\n  // just stick it in now rather than try to inject it later in the middle of\n  // the pattern.\n  //\n  // We can just always return the same end, and leave it up to the caller\n  // to know whether it's going to be used joined or in parts.\n  // And, if the start is adjusted slightly, can do the same there:\n  // - if not isStart: nothing\n  // - if traversal possible, but not allowed: (?:/|^)(?!\\.\\.?$)\n  // - if dots allowed or not possible: (?:/|^)\n  // - if dots possible and not allowed: (?:/|^)(?!\\.)\n  //\n  // But it's better to have a simpler binding without a conditional, for\n  // performance, so probably better to return both start options.\n  //\n  // Then the caller just ignores the end if it's not the first pattern,\n  // and the start always gets applied.\n  //\n  // But that's always going to be $ if it's the ending pattern, or nothing,\n  // so the caller can just attach $ at the end of the pattern when building.\n  //\n  // So the todo is:\n  // - better detect what kind of start is needed\n  // - return both flavors of starting pattern\n  // - attach $ at the end of the pattern when creating the actual RegExp\n  //\n  // Ah, but wait, no, that all only applies to the root when the first pattern\n  // is not an extglob. If the first pattern IS an extglob, then we need all\n  // that dot prevention biz to live in the extglob portions, because eg\n  // +(*|.x*) can match .xy but not .yx.\n  //\n  // So, return the two flavors if it's #root and the first child is not an\n  // AST, otherwise leave it to the child AST to handle it, and there,\n  // use the (?:^|/) style of start binding.\n  //\n  // Even simplified further:\n  // - Since the start for a join is eg /(?!\\.) and the start for a part\n  // is ^(?!\\.), we can just prepend (?!\\.) to the pattern (either root\n  // or start or whatever) and prepend ^ or / at the Regexp construction.\n  toRegExpSource(\n    allowDot?: boolean\n  ): [re: string, body: string, hasMagic: boolean, uflag: boolean] {\n    const dot = allowDot ?? !!this.#options.dot\n    if (this.#root === this) this.#fillNegs()\n    if (!this.type) {\n      const noEmpty = this.isStart() && this.isEnd()\n      const src = this.#parts\n        .map(p => {\n          const [re, _, hasMagic, uflag] =\n            typeof p === 'string'\n              ? AST.#parseGlob(p, this.#hasMagic, noEmpty)\n              : p.toRegExpSource(allowDot)\n          this.#hasMagic = this.#hasMagic || hasMagic\n          this.#uflag = this.#uflag || uflag\n          return re\n        })\n        .join('')\n\n      let start = ''\n      if (this.isStart()) {\n        if (typeof this.#parts[0] === 'string') {\n          // this is the string that will match the start of the pattern,\n          // so we need to protect against dots and such.\n\n          // '.' and '..' cannot match unless the pattern is that exactly,\n          // even if it starts with . or dot:true is set.\n          const dotTravAllowed =\n            this.#parts.length === 1 && justDots.has(this.#parts[0])\n          if (!dotTravAllowed) {\n            const aps = addPatternStart\n            // check if we have a possibility of matching . or ..,\n            // and prevent that.\n            const needNoTrav =\n              // dots are allowed, and the pattern starts with [ or .\n              (dot && aps.has(src.charAt(0))) ||\n              // the pattern starts with \\., and then [ or .\n              (src.startsWith('\\\\.') && aps.has(src.charAt(2))) ||\n              // the pattern starts with \\.\\., and then [ or .\n              (src.startsWith('\\\\.\\\\.') && aps.has(src.charAt(4)))\n            // no need to prevent dots if it can't match a dot, or if a\n            // sub-pattern will be preventing it anyway.\n            const needNoDot = !dot && !allowDot && aps.has(src.charAt(0))\n\n            start = needNoTrav ? startNoTraversal : needNoDot ? startNoDot : ''\n          }\n        }\n      }\n\n      // append the \"end of path portion\" pattern to negation tails\n      let end = ''\n      if (\n        this.isEnd() &&\n        this.#root.#filledNegs &&\n        this.#parent?.type === '!'\n      ) {\n        end = '(?:$|\\\\/)'\n      }\n      const final = start + src + end\n      return [\n        final,\n        unescape(src),\n        (this.#hasMagic = !!this.#hasMagic),\n        this.#uflag,\n      ]\n    }\n\n    // We need to calculate the body *twice* if it's a repeat pattern\n    // at the start, once in nodot mode, then again in dot mode, so a\n    // pattern like *(?) can match 'x.y'\n\n    const repeated = this.type === '*' || this.type === '+'\n    // some kind of extglob\n    const start = this.type === '!' ? '(?:(?!(?:' : '(?:'\n    let body = this.#partsToRegExp(dot)\n\n    if (this.isStart() && this.isEnd() && !body && this.type !== '!') {\n      // invalid extglob, has to at least be *something* present, if it's\n      // the entire path portion.\n      const s = this.toString()\n      this.#parts = [s]\n      this.type = null\n      this.#hasMagic = undefined\n      return [s, unescape(this.toString()), false, false]\n    }\n\n    // XXX abstract out this map method\n    let bodyDotAllowed =\n      !repeated || allowDot || dot || !startNoDot\n        ? ''\n        : this.#partsToRegExp(true)\n    if (bodyDotAllowed === body) {\n      bodyDotAllowed = ''\n    }\n    if (bodyDotAllowed) {\n      body = `(?:${body})(?:${bodyDotAllowed})*?`\n    }\n\n    // an empty !() is exactly equivalent to a starNoEmpty\n    let final = ''\n    if (this.type === '!' && this.#emptyExt) {\n      final = (this.isStart() && !dot ? startNoDot : '') + starNoEmpty\n    } else {\n      const close =\n        this.type === '!'\n          ? // !() must match something,but !(x) can match ''\n            '))' +\n            (this.isStart() && !dot && !allowDot ? startNoDot : '') +\n            star +\n            ')'\n          : this.type === '@'\n          ? ')'\n          : this.type === '?'\n          ? ')?'\n          : this.type === '+' && bodyDotAllowed\n          ? ')'\n          : this.type === '*' && bodyDotAllowed\n          ? `)?`\n          : `)${this.type}`\n      final = start + body + close\n    }\n    return [\n      final,\n      unescape(body),\n      (this.#hasMagic = !!this.#hasMagic),\n      this.#uflag,\n    ]\n  }\n\n  #partsToRegExp(dot: boolean) {\n    return this.#parts\n      .map(p => {\n        // extglob ASTs should only contain parent ASTs\n        /* c8 ignore start */\n        if (typeof p === 'string') {\n          throw new Error('string type in extglob ast??')\n        }\n        /* c8 ignore stop */\n        // can ignore hasMagic, because extglobs are already always magic\n        const [re, _, _hasMagic, uflag] = p.toRegExpSource(dot)\n        this.#uflag = this.#uflag || uflag\n        return re\n      })\n      .filter(p => !(this.isStart() && this.isEnd()) || !!p)\n      .join('|')\n  }\n\n  static #parseGlob(\n    glob: string,\n    hasMagic: boolean | undefined,\n    noEmpty: boolean = false\n  ): [re: string, body: string, hasMagic: boolean, uflag: boolean] {\n    let escaping = false\n    let re = ''\n    let uflag = false\n    for (let i = 0; i < glob.length; i++) {\n      const c = glob.charAt(i)\n      if (escaping) {\n        escaping = false\n        re += (reSpecials.has(c) ? '\\\\' : '') + c\n        continue\n      }\n      if (c === '\\\\') {\n        if (i === glob.length - 1) {\n          re += '\\\\\\\\'\n        } else {\n          escaping = true\n        }\n        continue\n      }\n      if (c === '[') {\n        const [src, needUflag, consumed, magic] = parseClass(glob, i)\n        if (consumed) {\n          re += src\n          uflag = uflag || needUflag\n          i += consumed - 1\n          hasMagic = hasMagic || magic\n          continue\n        }\n      }\n      if (c === '*') {\n        if (noEmpty && glob === '*') re += starNoEmpty\n        else re += star\n        hasMagic = true\n        continue\n      }\n      if (c === '?') {\n        re += qmark\n        hasMagic = true\n        continue\n      }\n      re += regExpEscape(c)\n    }\n    return [re, unescape(glob), !!hasMagic, uflag]\n  }\n}\n"]}