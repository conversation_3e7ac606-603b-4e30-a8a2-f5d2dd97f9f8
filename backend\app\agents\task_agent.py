"""Task processing agent for extracting and categorizing tasks."""

import logging
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime, timedelta
import re

from mirascope import llm
from pydantic import BaseModel, Field

from app.models.pydantic_models import UserInput, Task, TaskPriority
from app.config import settings

logger = logging.getLogger(__name__)


class TaskExtraction(BaseModel):
    """Structured response for task extraction."""
    title: str = Field(..., description="Clear, concise task title")
    description: Optional[str] = Field(None, description="Detailed task description")
    category: str = Field(..., description="AI-generated category (e.g., Education, Work, Personal)")
    priority: str = Field(default="medium", description="Priority: low, medium, high, urgent")
    due_date_text: Optional[str] = Field(None, description="Any date/time mentioned in original text")
    has_deadline: bool = Field(default=False, description="Whether task has a specific deadline")


class TaskAgent:
    """Agent specialized for task extraction and categorization."""
    
    def __init__(self):
        self.history = []
    
    @llm.call(
        provider="openai",
        model=settings.default_model,
        response_model=TaskExtraction,
        base_url="https://openrouter.ai/api/v1",
        api_key=settings.openrouter_api_key
    )
    async def extract_task_details(self, user_input: UserInput) -> str:
        """
        Extract task details with AI-driven categorization.
        NO hardcoded keywords - pure AI decision making.
        """
        return f"""
        You are a task extraction specialist. Analyze the user's input and extract task details.

        User input: "{user_input.text}"

        Extract the following information:
        1. **Title**: Create a clear, actionable task title
        2. **Description**: Add helpful details if the input provides them
        3. **Category**: Generate an intelligent category based on the task content
           - Examples: Education, Work, Personal, Health, Finance, Home, Shopping, etc.
           - Be creative and specific - don't use generic categories
        4. **Priority**: Assess urgency based on language and context
           - urgent: immediate action needed, critical deadlines
           - high: important, should be done soon
           - medium: normal priority, can be scheduled
           - low: nice to have, flexible timing
        5. **Due Date**: Extract any time references (tomorrow, next week, by Friday, etc.)
        6. **Has Deadline**: Whether there's a specific time constraint

        Be intelligent about categorization - group similar tasks together.
        For example: "Biology homework" and "Math assignment" should both be "Education".
        """
    
    async def process_task_with_feedback(
        self, 
        user_input: UserInput
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process task with visual feedback following mermaid diagram.
        """
        try:
            # Step 1: Extracting details animation
            yield {
                "step": "extracting_details",
                "message": "⚙️ Extracting details...",
                "animation": "spinner_animation",
                "timestamp": datetime.now().isoformat()
            }
            
            # Extract task details using AI
            task_data = await self.extract_task_details(user_input)
            
            # Step 2: Auto-categorizing animation
            yield {
                "step": "auto_categorizing",
                "message": "🏷️ Auto-categorizing...",
                "animation": "tag_animations",
                "timestamp": datetime.now().isoformat()
            }
            
            # Step 3: Checking for dates animation
            yield {
                "step": "checking_dates",
                "message": "📅 Checking for dates...",
                "animation": "calendar_scan",
                "timestamp": datetime.now().isoformat()
            }
            
            # Parse due date if mentioned
            due_date = None
            if task_data.due_date_text:
                due_date = await self._parse_due_date(task_data.due_date_text)
            
            # Step 4: Date decision
            if due_date:
                yield {
                    "step": "scheduling",
                    "message": "🗓️ Scheduling...",
                    "animation": "slide_to_date",
                    "timestamp": datetime.now().isoformat()
                }
            else:
                yield {
                    "step": "adding_to_today",
                    "message": "📌 Adding to today...",
                    "animation": "drop_animation",
                    "timestamp": datetime.now().isoformat()
                }
            
            # Step 5: Updating task list animation
            yield {
                "step": "updating_task_list",
                "message": "📋 Updating task list...",
                "animation": "list_grow_animation",
                "timestamp": datetime.now().isoformat()
            }
            
            # Create task object
            task = Task(
                title=task_data.title,
                description=task_data.description,
                ai_generated_category=task_data.category,
                priority=TaskPriority(task_data.priority),
                due_date=due_date,
                created_at=datetime.now()
            )
            
            # Step 6: Syncing calendar animation
            yield {
                "step": "syncing_calendar",
                "message": "📅 Syncing calendar...",
                "animation": "sync_spinner",
                "timestamp": datetime.now().isoformat()
            }
            
            # TODO: Save to database using task tool
            
            # Step 7: Success animation
            yield {
                "step": "task_added_successfully",
                "message": "✅ Task added successfully!",
                "animation": "success_confetti",
                "task": task.dict(),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in task processing: {e}")
            yield {
                "step": "error",
                "message": f"❌ Error processing task: {str(e)}",
                "animation": "shake_animation",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _parse_due_date(self, date_text: str) -> Optional[datetime]:
        """Parse natural language date references."""
        date_text = date_text.lower().strip()
        now = datetime.now()
        
        # Simple date parsing - can be enhanced with more sophisticated NLP
        if "tomorrow" in date_text:
            return now + timedelta(days=1)
        elif "next week" in date_text:
            return now + timedelta(weeks=1)
        elif "monday" in date_text:
            days_ahead = 0 - now.weekday()
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            return now + timedelta(days=days_ahead)
        elif "friday" in date_text:
            days_ahead = 4 - now.weekday()
            if days_ahead <= 0:
                days_ahead += 7
            return now + timedelta(days=days_ahead)
        # Add more date parsing logic as needed
        
        return None
