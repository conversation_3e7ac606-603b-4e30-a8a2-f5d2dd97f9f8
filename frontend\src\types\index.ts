// Core data types matching backend Pydantic models

export interface UserInput {
  text: string;
  timestamp: string;
}

export interface CategoryDecision {
  category: InputCategory;
  confidence: number;
  reasoning: string;
  processing_steps: string[];
}

export interface Task {
  id?: string;
  title: string;
  description?: string;
  category?: string;
  ai_generated_category?: string;
  priority: TaskPriority;
  due_date?: string;
  completed: boolean;
  created_at: string;
}

export interface Event {
  id?: string;
  title: string;
  description?: string;
  start_time: string;
  end_time?: string;
  location?: string;
  created_at: string;
}

export interface SearchResult {
  title: string;
  content: string;
  source: string;
  url?: string;
  relevance_score: number;
}

export interface AIResponse {
  original_input: string;
  category: InputCategory;
  processing_steps: string[];
  result: Task | Event | string;
  search_results?: SearchResult[];
  sources?: string[];
  confidence: number;
}

// Enums
export type InputCategory = 'task' | 'event' | 'ai_question';
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';
export type QuestionType = 'simple_knowledge' | 'database_search' | 'web_search';

// Animation types
export interface AnimationStep {
  id: string;
  message: string;
  animation_type: string;
  duration: number;
  completed: boolean;
}

export interface ProcessingState {
  current_step: number;
  steps: AnimationStep[];
  is_processing: boolean;
  error?: string;
}

// WebSocket message types
export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

export interface ProcessingUpdate {
  type: 'processing_update';
  step: string;
  message: string;
  animation: string;
  timestamp: string;
  category?: InputCategory;
  confidence?: number;
  result?: any;
}

// API response types
export interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface TasksResponse {
  tasks: Task[];
  count: number;
}

export interface EventsResponse {
  events: Event[];
  count: number;
}

export interface SearchResponse {
  query: string;
  results: SearchResult[];
  total_results: number;
  search_methods: {
    semantic: boolean;
    keyword: boolean;
  };
}

export interface CategoriesResponse {
  ai_categories: string[];
  user_categories: string[];
  all_categories: string[];
}

// Settings types
export interface AppSettings {
  animation_duration: number;
  enable_animations: boolean;
  models_available: string[];
  ollama_model: string;
  debug: boolean;
}

// Form types
export interface HeroInputFormData {
  input: string;
}

export interface TaskFormData {
  title: string;
  description?: string;
  category?: string;
  priority: TaskPriority;
  due_date?: string;
}

export interface EventFormData {
  title: string;
  description?: string;
  start_time: string;
  end_time?: string;
  location?: string;
}

// Animation configuration
export interface AnimationConfig {
  duration: number;
  ease: string;
  delay?: number;
}

export interface AnimationVariants {
  initial: any;
  animate: any;
  exit?: any;
  transition?: AnimationConfig;
}

// Component props types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

// State management types
export interface DashboardState {
  isProcessing: boolean;
  currentStep: string;
  processingSteps: ProcessingUpdate[];
  lastResult?: any;
  error?: string;
}

export interface TasksState {
  tasks: Task[];
  categories: string[];
  filters: {
    category?: string;
    priority?: TaskPriority;
    completed?: boolean;
  };
  loading: boolean;
  error?: string;
}

export interface EventsState {
  events: Event[];
  selectedDate?: Date;
  viewMode: 'month' | 'week' | 'day';
  loading: boolean;
  error?: string;
}

export interface SettingsState {
  settings: AppSettings;
  loading: boolean;
  error?: string;
}
