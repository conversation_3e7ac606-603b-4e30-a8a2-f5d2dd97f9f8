import React from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { X, Save, Calendar, Clock, MapPin } from 'lucide-react';

import { EventFormData } from '@/types';
import { useEventsStore } from '@/stores/eventsStore';

interface EventFormProps {
  onClose: () => void;
  onSubmit: () => void;
}

const EventForm: React.FC<EventFormProps> = ({ onClose, onSubmit }) => {
  const { createEvent } = useEventsStore();
  
  const { register, handleSubmit, formState: { errors, isSubmitting }, watch } = useForm<EventFormData>({
    defaultValues: {
      title: '',
      description: '',
      location: '',
      start_time: '',
      end_time: '',
    }
  });

  const startTime = watch('start_time');

  const onFormSubmit = async (data: EventFormData) => {
    try {
      await createEvent({
        title: data.title,
        description: data.description || undefined,
        location: data.location || undefined,
        start_time: new Date(data.start_time).toISOString(),
        end_time: new Date(data.end_time).toISOString(),
      });
      onSubmit();
    } catch (error) {
      console.error('Failed to create event:', error);
    }
  };

  // Set minimum end time to start time
  const getMinEndTime = () => {
    if (!startTime) return '';
    return startTime;
  };

  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="bg-primary-800 rounded-2xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-info" />
            <h2 className="text-xl font-semibold text-white">Create New Event</h2>
          </div>
          <motion.button
            className="p-2 hover:bg-primary-700 rounded-lg transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onClose}
          >
            <X className="w-5 h-5 text-primary-300" />
          </motion.button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-primary-300 mb-2">
              Event Title *
            </label>
            <input
              {...register('title', { 
                required: 'Event title is required',
                minLength: { value: 1, message: 'Title cannot be empty' }
              })}
              className="input"
              placeholder="Enter event title..."
              autoFocus
            />
            {errors.title && (
              <p className="text-error text-sm mt-1">{errors.title.message}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-primary-300 mb-2">
              Description
            </label>
            <textarea
              {...register('description')}
              className="input resize-none"
              rows={3}
              placeholder="Optional description..."
            />
          </div>

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-primary-300 mb-2">
              <MapPin className="w-4 h-4 inline mr-1" />
              Location
            </label>
            <input
              {...register('location')}
              className="input"
              placeholder="Event location (optional)"
            />
          </div>

          {/* Start Time */}
          <div>
            <label className="block text-sm font-medium text-primary-300 mb-2">
              <Clock className="w-4 h-4 inline mr-1" />
              Start Time *
            </label>
            <input
              {...register('start_time', { 
                required: 'Start time is required'
              })}
              type="datetime-local"
              className="input"
              min={new Date().toISOString().slice(0, 16)}
            />
            {errors.start_time && (
              <p className="text-error text-sm mt-1">{errors.start_time.message}</p>
            )}
          </div>

          {/* End Time */}
          <div>
            <label className="block text-sm font-medium text-primary-300 mb-2">
              <Clock className="w-4 h-4 inline mr-1" />
              End Time *
            </label>
            <input
              {...register('end_time', { 
                required: 'End time is required',
                validate: (value) => {
                  if (!startTime || !value) return true;
                  return new Date(value) > new Date(startTime) || 'End time must be after start time';
                }
              })}
              type="datetime-local"
              className="input"
              min={getMinEndTime()}
            />
            {errors.end_time && (
              <p className="text-error text-sm mt-1">{errors.end_time.message}</p>
            )}
          </div>

          {/* Quick Duration Buttons */}
          {startTime && (
            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Quick Duration
              </label>
              <div className="grid grid-cols-4 gap-2">
                {[
                  { label: '30m', minutes: 30 },
                  { label: '1h', minutes: 60 },
                  { label: '2h', minutes: 120 },
                  { label: '4h', minutes: 240 },
                ].map(({ label, minutes }) => (
                  <motion.button
                    key={label}
                    type="button"
                    className="text-xs px-2 py-1 bg-primary-700/50 hover:bg-primary-700 rounded transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      const start = new Date(startTime);
                      const end = new Date(start.getTime() + minutes * 60000);
                      const endTimeInput = document.querySelector('input[name="end_time"]') as HTMLInputElement;
                      if (endTimeInput) {
                        endTimeInput.value = end.toISOString().slice(0, 16);
                      }
                    }}
                  >
                    {label}
                  </motion.button>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <motion.button
              type="button"
              className="btn-ghost flex-1"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </motion.button>
            
            <motion.button
              type="submit"
              className="btn-primary flex-1 flex items-center justify-center gap-2"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Create Event
                </>
              )}
            </motion.button>
          </div>
        </form>
      </motion.div>
    </motion.div>
  );
};

export default EventForm;
