import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useDashboardStore } from '@/stores/dashboardStore';
import ProcessingVisualizer from './ProcessingVisualizer';
import { useWebSocket } from '@/hooks/useWebSocket';

interface AnimationOrchestratorProps {
  children: React.ReactNode;
}

const AnimationOrchestrator: React.FC<AnimationOrchestratorProps> = ({ children }) => {
  const {
    isProcessing,
    currentStep,
    processingSteps,
    lastResult,
    error,
    setProcessing,
    addProcessingStep,
    setLastResult,
    setError,
    clearProcessingSteps,
  } = useDashboardStore();

  const [showResult, setShowResult] = useState(false);
  const [resultAnimation, setResultAnimation] = useState<'task' | 'event' | 'ai_response' | null>(null);

  const { sendMessage } = useWebSocket({
    onMessage: (message) => {
      handleWebSocketMessage(message);
    },
  });

  const handleWebSocketMessage = (message: any) => {
    console.log('WebSocket message received:', message);

    switch (message.type) {
      case 'processing_start':
        setProcessing(true);
        clearProcessingSteps();
        setError(null);
        break;

      case 'processing_step':
        addProcessingStep({
          step: message.step,
          details: message.details,
          timestamp: new Date().toISOString(),
        });
        break;

      case 'processing_complete':
        setProcessing(false);
        setLastResult(message.result);
        setResultAnimation(message.category);
        setShowResult(true);
        break;

      case 'processing_error':
        setProcessing(false);
        setError(message.error);
        break;

      case 'pong':
        // Handle ping/pong for connection health
        break;

      default:
        console.log('Unknown message type:', message.type);
    }
  };

  const handleProcessingComplete = () => {
    // Keep the result visible for a moment, then show the result animation
    setTimeout(() => {
      setShowResult(true);
    }, 500);
  };

  const handleResultAnimationComplete = () => {
    setShowResult(false);
    setResultAnimation(null);
  };

  // Simulate processing steps for demo purposes
  const simulateProcessing = (input: string) => {
    setProcessing(true);
    clearProcessingSteps();
    setError(null);

    const steps = [
      { step: 'Analyzing input...', details: 'Understanding user intent' },
      { step: 'AI Categorization', details: 'Determining input type' },
      { step: 'Processing request', details: 'Executing appropriate workflow' },
      { step: 'Generating response', details: 'Preparing final output' },
      { step: 'Complete', details: 'Ready to display results' },
    ];

    steps.forEach((stepData, index) => {
      setTimeout(() => {
        addProcessingStep({
          ...stepData,
          timestamp: new Date().toISOString(),
        });

        if (index === steps.length - 1) {
          setTimeout(() => {
            setProcessing(false);
            setLastResult({
              category: 'task',
              result: { title: 'Sample Task', description: 'Created from: ' + input },
            });
            setResultAnimation('task');
            setShowResult(true);
          }, 800);
        }
      }, index * 1000);
    });
  };

  return (
    <div className="relative">
      {children}

      {/* Processing Visualizer */}
      <AnimatePresence>
        {(isProcessing || processingSteps.length > 0) && (
          <ProcessingVisualizer
            isProcessing={isProcessing}
            currentStep={currentStep}
            processingSteps={processingSteps}
            onComplete={handleProcessingComplete}
          />
        )}
      </AnimatePresence>

      {/* Result Animation */}
      <AnimatePresence>
        {showResult && resultAnimation && lastResult && (
          <ResultAnimation
            type={resultAnimation}
            result={lastResult}
            onComplete={handleResultAnimationComplete}
          />
        )}
      </AnimatePresence>

      {/* Error Display */}
      <AnimatePresence>
        {error && (
          <motion.div
            className="fixed top-4 right-4 bg-error/90 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm"
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            <h4 className="font-medium mb-1">Processing Error</h4>
            <p className="text-sm opacity-90">{error}</p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Result Animation Component
interface ResultAnimationProps {
  type: 'task' | 'event' | 'ai_response';
  result: any;
  onComplete: () => void;
}

const ResultAnimation: React.FC<ResultAnimationProps> = ({ type, result, onComplete }) => {
  useEffect(() => {
    const timer = setTimeout(onComplete, 3000);
    return () => clearTimeout(timer);
  }, [onComplete]);

  const getResultIcon = () => {
    switch (type) {
      case 'task':
        return '✅';
      case 'event':
        return '📅';
      case 'ai_response':
        return '🤖';
      default:
        return '✨';
    }
  };

  const getResultTitle = () => {
    switch (type) {
      case 'task':
        return 'Task Created';
      case 'event':
        return 'Event Scheduled';
      case 'ai_response':
        return 'AI Response Ready';
      default:
        return 'Processing Complete';
    }
  };

  return (
    <motion.div
      className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-primary-800 rounded-2xl p-8 text-center border border-primary-700"
        initial={{ scale: 0, rotate: -10 }}
        animate={{ scale: 1, rotate: 0 }}
        exit={{ scale: 0, rotate: 10 }}
        transition={{ 
          type: "spring", 
          stiffness: 300, 
          damping: 20,
          duration: 0.6
        }}
      >
        <motion.div
          className="text-6xl mb-4"
          animate={{ 
            scale: [1, 1.2, 1],
            rotate: [0, 10, -10, 0]
          }}
          transition={{ 
            duration: 0.8,
            ease: "easeInOut"
          }}
        >
          {getResultIcon()}
        </motion.div>

        <motion.h3
          className="text-xl font-semibold text-white mb-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          {getResultTitle()}
        </motion.h3>

        <motion.p
          className="text-primary-400 text-sm"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          {result.result?.title || result.result?.content || 'Successfully processed your request'}
        </motion.p>

        {/* Celebration particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-info rounded-full"
              initial={{ 
                x: '50%', 
                y: '50%',
                scale: 0,
                opacity: 1
              }}
              animate={{ 
                x: `${50 + (Math.random() - 0.5) * 200}%`,
                y: `${50 + (Math.random() - 0.5) * 200}%`,
                scale: [0, 1, 0],
                opacity: [1, 1, 0]
              }}
              transition={{ 
                duration: 2,
                delay: i * 0.1,
                ease: "easeOut"
              }}
            />
          ))}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AnimationOrchestrator;
