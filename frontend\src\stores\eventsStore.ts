import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { EventsState, Event } from '@/types';
import apiService from '@/services/api';

interface EventsStore extends EventsState {
  // Actions
  setEvents: (events: Event[]) => void;
  addEvent: (event: Event) => void;
  updateEvent: (id: string, updates: Partial<Event>) => void;
  removeEvent: (id: string) => void;
  setSelectedDate: (date: Date | undefined) => void;
  setViewMode: (mode: 'month' | 'week' | 'day') => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Async actions
  fetchEvents: (startDate?: Date, endDate?: Date) => Promise<void>;
  createEvent: (event: Partial<Event>) => Promise<void>;
  updateEventAsync: (id: string, updates: Partial<Event>) => Promise<void>;
  deleteEvent: (id: string) => Promise<void>;
}

const initialState: EventsState = {
  events: [],
  selectedDate: undefined,
  viewMode: 'month',
  loading: false,
  error: undefined,
};

export const useEventsStore = create<EventsStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    // Sync actions
    setEvents: (events: Event[]) => set({ events }),
    
    addEvent: (event: Event) => 
      set((state) => ({ events: [...state.events, event] })),
    
    updateEvent: (id: string, updates: Partial<Event>) => 
      set((state) => ({
        events: state.events.map(event => 
          event.id === id ? { ...event, ...updates } : event
        )
      })),
    
    removeEvent: (id: string) => 
      set((state) => ({
        events: state.events.filter(event => event.id !== id)
      })),
    
    setSelectedDate: (date: Date | undefined) => set({ selectedDate: date }),
    
    setViewMode: (mode: 'month' | 'week' | 'day') => set({ viewMode: mode }),
    
    setLoading: (loading: boolean) => set({ loading }),
    
    setError: (error: string | null) => set({ error }),

    // Async actions
    fetchEvents: async (startDate?: Date, endDate?: Date) => {
      const { setLoading, setError, setEvents } = get();
      
      setLoading(true);
      setError(null);
      
      try {
        const filters = {
          start_date: startDate?.toISOString(),
          end_date: endDate?.toISOString(),
        };
        
        const response = await apiService.getEvents(filters);
        setEvents(response.events);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to fetch events');
      } finally {
        setLoading(false);
      }
    },

    createEvent: async (eventData: Partial<Event>) => {
      const { setLoading, setError, addEvent } = get();
      
      setLoading(true);
      setError(null);
      
      try {
        const newEvent = await apiService.createEvent(eventData);
        addEvent(newEvent);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to create event');
        throw error;
      } finally {
        setLoading(false);
      }
    },

    updateEventAsync: async (id: string, updates: Partial<Event>) => {
      const { setLoading, setError, updateEvent } = get();
      
      setLoading(true);
      setError(null);
      
      try {
        const updatedEvent = await apiService.updateEvent(id, updates);
        updateEvent(id, updatedEvent);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to update event');
        throw error;
      } finally {
        setLoading(false);
      }
    },

    deleteEvent: async (id: string) => {
      const { setLoading, setError, removeEvent } = get();
      
      setLoading(true);
      setError(null);
      
      try {
        await apiService.deleteEvent(id);
        removeEvent(id);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to delete event');
        throw error;
      } finally {
        setLoading(false);
      }
    },
  }))
);

// Selectors
export const selectEvents = (state: EventsStore) => state.events;
export const selectEventsByDate = (date: Date) => (state: EventsStore) => {
  const targetDate = date.toDateString();
  return state.events.filter(event => {
    const eventDate = new Date(event.start_time).toDateString();
    return eventDate === targetDate;
  });
};
export const selectUpcomingEvents = (state: EventsStore) => {
  const now = new Date();
  return state.events
    .filter(event => new Date(event.start_time) > now)
    .sort((a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime());
};
export const selectEventsLoading = (state: EventsStore) => state.loading;
export const selectEventsError = (state: EventsStore) => state.error;
