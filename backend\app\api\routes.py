"""API routes for the dashboard."""

import logging
from typing import List, Optional, AsyncGenerator
import json

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse, StreamingResponse

from app.models.pydantic_models import (
    UserInput,
    AIResponse,
    Task,
    Event,
    CategoryDecision
)
from app.agents.orchestrator import OrchestratorAgent
from app.tools.task_tool import TaskTool
from app.tools.calendar_tool import CalendarTool
from app.tools.database_search_tool import DatabaseSearchTool

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize agents and tools
orchestrator = OrchestratorAgent()
task_tool = TaskTool()
calendar_tool = CalendarTool()
search_tool = DatabaseSearchTool()


@router.post("/process-input")
async def process_input(user_input: UserInput):
    """
    Main orchestration endpoint - processes user input through AI categorization.
    Returns streaming response for real-time visual feedback.
    """
    logger.info(f"Processing input: {user_input.text}")

    async def generate_response():
        """Generate streaming response with visual feedback."""
        try:
            async for update in orchestrator.process_with_visual_feedback(user_input):
                yield f"data: {json.dumps(update)}\n\n"
        except Exception as e:
            logger.error(f"Error in process_input: {e}")
            error_update = {
                "step": "error",
                "message": f"❌ Error: {str(e)}",
                "animation": "shake_animation",
                "timestamp": None
            }
            yield f"data: {json.dumps(error_update)}\n\n"

    return StreamingResponse(
        generate_response(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )


@router.get("/tasks")
async def get_tasks(
    category: Optional[str] = None,
    ai_category: Optional[str] = None,
    completed: Optional[bool] = None,
    priority: Optional[str] = None,
    limit: int = 100
):
    """Get all tasks, optionally filtered by various criteria."""
    logger.info(f"Getting tasks with filters: category={category}, ai_category={ai_category}")

    result = await task_tool.get_tasks(
        category=category,
        ai_category=ai_category,
        completed=completed,
        priority=priority,
        limit=limit
    )

    if result.get("success"):
        return JSONResponse(content={
            "tasks": result["tasks"],
            "count": result["count"]
        })
    else:
        raise HTTPException(status_code=500, detail=result.get("error", "Failed to get tasks"))


@router.post("/tasks")
async def create_task(task_data: dict):
    """Create a new task."""
    logger.info(f"Creating task: {task_data.get('title')}")

    result = await task_tool.create_task(
        title=task_data["title"],
        description=task_data.get("description"),
        category=task_data.get("category"),
        ai_generated_category=task_data.get("ai_generated_category"),
        priority=task_data.get("priority", "medium"),
        due_date=task_data.get("due_date")
    )

    if result.get("success"):
        return JSONResponse(content=result["task"])
    else:
        raise HTTPException(status_code=500, detail=result.get("error", "Failed to create task"))


@router.get("/events")
async def get_events(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 50
):
    """Get all events, optionally filtered by date range."""
    from datetime import datetime

    logger.info(f"Getting events, date range: {start_date} to {end_date}")

    # Parse dates if provided
    start_dt = datetime.fromisoformat(start_date) if start_date else None
    end_dt = datetime.fromisoformat(end_date) if end_date else None

    result = await calendar_tool.get_events(
        start_date=start_dt,
        end_date=end_dt,
        limit=limit
    )

    if result.get("success"):
        return JSONResponse(content={
            "events": result["events"],
            "count": result["count"]
        })
    else:
        raise HTTPException(status_code=500, detail=result.get("error", "Failed to get events"))


@router.post("/events")
async def create_event(event_data: dict):
    """Create a new event."""
    from datetime import datetime

    logger.info(f"Creating event: {event_data.get('title')}")

    # Parse datetime strings
    start_time = datetime.fromisoformat(event_data["start_time"])
    end_time = datetime.fromisoformat(event_data["end_time"]) if event_data.get("end_time") else None

    result = await calendar_tool.create_event(
        title=event_data["title"],
        start_time=start_time,
        description=event_data.get("description"),
        end_time=end_time,
        location=event_data.get("location")
    )

    if result.get("success"):
        return JSONResponse(content=result["event"])
    else:
        raise HTTPException(status_code=500, detail=result.get("error", "Failed to create event"))


@router.get("/settings")
async def get_settings():
    """Get application settings."""
    return JSONResponse(
        content={
            "animation_duration": 1000,
            "enable_animations": True,
            "models_available": [
                "meta-llama/llama-3.2-3b-instruct:free",
                "meta-llama/llama-3.1-8b-instruct:free"
            ]
        }
    )


@router.post("/settings")
async def update_settings(settings_data: dict):
    """Update application settings."""
    # TODO: Implement settings persistence
    logger.info(f"Updating settings: {settings_data}")
    return JSONResponse(content={"status": "updated"})
