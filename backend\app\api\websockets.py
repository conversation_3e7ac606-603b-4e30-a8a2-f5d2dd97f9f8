"""WebSocket implementation for real-time updates."""

import json
import logging
from typing import Dict, Set
from fastapi import APIRouter, WebSocket, WebSocketDisconnect

from app.models.pydantic_models import UserInput
from app.agents.orchestrator import OrchestratorAgent

logger = logging.getLogger(__name__)

websocket_router = APIRouter()

# Store active WebSocket connections
active_connections: Set[WebSocket] = set()

# Initialize orchestrator for WebSocket processing
orchestrator = OrchestratorAgent()


class ConnectionManager:
    """Manages WebSocket connections for real-time updates."""
    
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
    
    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.add(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        self.active_connections.discard(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending message to WebSocket: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: str):
        """Broadcast a message to all connected WebSocket clients."""
        disconnected = set()
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to WebSocket: {e}")
                disconnected.add(connection)
        
        # Remove disconnected connections
        for connection in disconnected:
            self.disconnect(connection)
    
    async def send_processing_update(self, step: str, message: str, animation: str):
        """Send a processing update following the mermaid diagram."""
        update = {
            "type": "processing_update",
            "step": step,
            "message": message,
            "animation": animation,
            "timestamp": None  # Will be set by frontend
        }
        await self.broadcast(json.dumps(update))


# Global connection manager
manager = ConnectionManager()


@websocket_router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication."""
    await manager.connect(websocket)
    
    try:
        while True:
            # Wait for messages from client
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                message_type = message.get("type")
                
                if message_type == "ping":
                    # Respond to ping with pong
                    await manager.send_personal_message(
                        json.dumps({"type": "pong"}), 
                        websocket
                    )
                elif message_type == "process_input":
                    # Handle input processing request
                    user_input_text = message.get("input", "")
                    logger.info(f"Processing input via WebSocket: {user_input_text}")

                    try:
                        # Create UserInput object
                        user_input = UserInput(text=user_input_text)

                        # Process with orchestrator and stream updates
                        async for update in orchestrator.process_with_visual_feedback(user_input):
                            await manager.send_personal_message(
                                json.dumps({
                                    "type": "processing_update",
                                    **update
                                }),
                                websocket
                            )
                    except Exception as e:
                        logger.error(f"Error processing input: {e}")
                        await manager.send_personal_message(
                            json.dumps({
                                "type": "error",
                                "message": f"Error processing input: {str(e)}",
                                "step": "error",
                                "animation": "shake_animation"
                            }),
                            websocket
                        )
                
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received: {data}")
                await manager.send_personal_message(
                    json.dumps({"type": "error", "message": "Invalid JSON"}),
                    websocket
                )
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)
