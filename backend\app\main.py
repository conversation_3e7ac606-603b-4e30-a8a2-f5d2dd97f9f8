"""FastAPI application with WebSocket support and CORS configuration."""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse

from app.config import settings
from app.api.routes import router as api_router
from app.api.websockets import websocket_router
from app.database.connection import init_database


# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    logger.info("Starting AI-Powered Dashboard Backend...")
    
    # Initialize database
    await init_database()
    logger.info("Database initialized")
    
    yield
    
    logger.info("Shutting down AI-Powered Dashboard Backend...")


# Create FastAPI application
app = FastAPI(
    title="AI-Powered Dashboard API",
    description="Backend API for AI-powered dashboard with visual transparency",
    version="1.0.0",
    lifespan=lifespan,
    debug=settings.debug
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(api_router, prefix="/api")
app.include_router(websocket_router)


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return JSONResponse(
        content={
            "status": "healthy",
            "service": "ai-dashboard-backend",
            "version": "1.0.0"
        }
    )


@app.get("/")
async def root():
    """Root endpoint."""
    return JSONResponse(
        content={
            "message": "AI-Powered Dashboard API",
            "docs": "/docs",
            "health": "/health"
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
