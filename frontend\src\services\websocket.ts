import { WebSocketMessage, ProcessingUpdate } from '@/types';

export type WebSocketEventHandler = (message: any) => void;

export interface WebSocketServiceOptions {
  onMessage?: WebSocketEventHandler;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
  reconnectAttempts?: number;
  reconnectInterval?: number;
}

class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private options: WebSocketServiceOptions;
  private reconnectCount = 0;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private messageQueue: any[] = [];
  private isConnected = false;

  constructor(url: string = '/ws', options: WebSocketServiceOptions = {}) {
    this.url = this.getWebSocketUrl(url);
    this.options = {
      reconnectAttempts: 5,
      reconnectInterval: 3000,
      ...options,
    };
  }

  private getWebSocketUrl(path: string): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}${path}`;
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnected = true;
          this.reconnectCount = 0;
          
          // Send queued messages
          while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.send(message);
          }
          
          this.options.onConnect?.();
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.options.onMessage?.(message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnected = false;
          this.ws = null;
          
          this.options.onDisconnect?.();

          // Attempt reconnection if not a clean close
          if (event.code !== 1000 && this.reconnectCount < (this.options.reconnectAttempts || 5)) {
            this.reconnectCount++;
            console.log(`Attempting to reconnect (${this.reconnectCount}/${this.options.reconnectAttempts})...`);
            
            this.reconnectTimeout = setTimeout(() => {
              this.connect().catch(console.error);
            }, this.options.reconnectInterval);
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.options.onError?.(error);
          reject(error);
        };

      } catch (error) {
        console.error('Failed to create WebSocket connection:', error);
        reject(error);
      }
    });
  }

  disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
    
    this.isConnected = false;
    this.reconnectCount = 0;
  }

  send(message: any): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      // Queue message for when connection is established
      this.messageQueue.push(message);
      
      // Try to connect if not already connecting
      if (!this.isConnected && this.ws?.readyState !== WebSocket.CONNECTING) {
        this.connect().catch(console.error);
      }
    }
  }

  sendPing(): void {
    this.send({ type: 'ping' });
  }

  processInput(input: string): void {
    this.send({
      type: 'process_input',
      input: input,
    });
  }

  getConnectionState(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  isWebSocketConnected(): boolean {
    return this.isConnected && this.ws?.readyState === WebSocket.OPEN;
  }
}

// Create singleton instance
export const webSocketService = new WebSocketService();
export default webSocketService;
