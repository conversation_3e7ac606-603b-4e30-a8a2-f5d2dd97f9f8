{"name": "assertion-error", "version": "2.0.1", "description": "Error constructor for test and validation frameworks that implements standardized AssertionError specification.", "author": "<PERSON> <<EMAIL>> (http://qualiancy.com)", "license": "MIT", "types": "./index.d.ts", "keywords": ["test", "assertion", "assertion-error"], "repository": {"type": "git", "url": "**************:chaijs/assertion-error.git"}, "engines": {"node": ">=12"}, "files": ["index.d.ts"], "type": "module", "module": "index.js", "main": "index.js", "scripts": {"build": "deno bundle mod.ts > index.js", "pretest": "rm -rf coverage/", "test": "deno test --coverage=coverage", "posttest": "deno coverage coverage --lcov > coverage/lcov.info && lcov --summary coverage/lcov.info"}}