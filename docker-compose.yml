version: '3.8'

services:
  # Frontend - React with Vite
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:8000
    depends_on:
      - backend
    networks:
      - app-network

  # Backend - FastAPI with Mirascope
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./data:/app/data
    environment:
      - DATABASE_URL=sqlite:///data/dashboard.db
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - LANGSEARCH_API_KEY=${LANGSEARCH_API_KEY}
      - OLLAMA_URL=http://ollama:11434
      - CORS_ORIGINS=http://localhost:3000
    depends_on:
      - ollama
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Ollama - Local embeddings service
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=http://localhost:3000,http://localhost:8000
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  ollama_data:
    driver: local

networks:
  app-network:
    driver: bridge
