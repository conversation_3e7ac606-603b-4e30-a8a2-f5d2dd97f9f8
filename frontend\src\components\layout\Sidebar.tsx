import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Home, 
  CheckSquare, 
  Calendar, 
  Settings, 
  Zap,
  Brain
} from 'lucide-react';

interface NavItem {
  path: string;
  label: string;
  icon: React.ComponentType<any>;
  description: string;
}

const navItems: NavItem[] = [
  {
    path: '/',
    label: 'Dashboard',
    icon: Home,
    description: 'AI-powered overview'
  },
  {
    path: '/tasks',
    label: 'Tasks',
    icon: CheckSquare,
    description: 'Smart task management'
  },
  {
    path: '/calendar',
    label: 'Calendar',
    icon: Calendar,
    description: 'Event scheduling'
  },
  {
    path: '/settings',
    label: 'Settings',
    icon: Settings,
    description: 'App configuration'
  }
];

const Sidebar: React.FC = () => {
  const location = useLocation();

  return (
    <motion.aside
      className="w-64 bg-primary-900/50 backdrop-blur-md border-r border-primary-700/50 flex flex-col"
      initial={{ x: -20, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {/* Logo/Brand */}
      <div className="p-6 border-b border-primary-700/50">
        <motion.div
          className="flex items-center gap-3"
          whileHover={{ scale: 1.02 }}
          transition={{ type: "spring", stiffness: 400, damping: 25 }}
        >
          <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
            <Brain className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">AI Dashboard</h1>
            <p className="text-xs text-primary-400">Intelligent & Alive</p>
          </div>
        </motion.div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {navItems.map((item, index) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;

            return (
              <motion.li
                key={item.path}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ 
                  duration: 0.3, 
                  delay: index * 0.1,
                  ease: "easeOut" 
                }}
              >
                <NavLink
                  to={item.path}
                  className={({ isActive }) =>
                    `group relative flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200 ${
                      isActive
                        ? 'bg-primary-600 text-white shadow-lg shadow-primary-600/25'
                        : 'text-primary-300 hover:text-white hover:bg-primary-800/50'
                    }`
                  }
                >
                  {/* Active indicator */}
                  {isActive && (
                    <motion.div
                      className="absolute left-0 top-0 bottom-0 w-1 bg-primary-400 rounded-r-full"
                      layoutId="activeIndicator"
                      transition={{ type: "spring", stiffness: 400, damping: 30 }}
                    />
                  )}

                  {/* Icon */}
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <Icon className="w-5 h-5" />
                  </motion.div>

                  {/* Label and description */}
                  <div className="flex-1">
                    <div className="font-medium">{item.label}</div>
                    <div className="text-xs opacity-70 group-hover:opacity-100 transition-opacity">
                      {item.description}
                    </div>
                  </div>

                  {/* Hover effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary-600/0 to-primary-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity"
                    whileHover={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  />
                </NavLink>
              </motion.li>
            );
          })}
        </ul>
      </nav>

      {/* Status indicator */}
      <div className="p-4 border-t border-primary-700/50">
        <motion.div
          className="flex items-center gap-3 px-4 py-3 bg-primary-800/30 rounded-xl"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <motion.div
            className="w-2 h-2 bg-success rounded-full"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
          <div className="flex-1">
            <div className="text-sm font-medium text-primary-200">AI Online</div>
            <div className="text-xs text-primary-400">Ready to assist</div>
          </div>
          <Zap className="w-4 h-4 text-primary-400" />
        </motion.div>
      </div>
    </motion.aside>
  );
};

export default Sidebar;
