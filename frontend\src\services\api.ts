import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  UserInput, 
  Task, 
  Event, 
  TasksResponse, 
  EventsResponse, 
  SearchResponse, 
  CategoriesResponse,
  AppSettings 
} from '@/types';

class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: '/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Input processing
  async processInput(input: UserInput): Promise<ReadableStream> {
    const response = await fetch('/api/process-input', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(input),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    if (!response.body) {
      throw new Error('No response body');
    }

    return response.body;
  }

  // Tasks
  async getTasks(filters?: {
    category?: string;
    ai_category?: string;
    completed?: boolean;
    priority?: string;
    limit?: number;
  }): Promise<TasksResponse> {
    const response = await this.client.get<TasksResponse>('/tasks', {
      params: filters,
    });
    return response.data;
  }

  async createTask(task: Partial<Task>): Promise<Task> {
    const response = await this.client.post<Task>('/tasks', task);
    return response.data;
  }

  async updateTask(id: string, updates: Partial<Task>): Promise<Task> {
    const response = await this.client.put<Task>(`/tasks/${id}`, updates);
    return response.data;
  }

  async deleteTask(id: string): Promise<void> {
    await this.client.delete(`/tasks/${id}`);
  }

  async getTaskCategories(): Promise<CategoriesResponse> {
    const response = await this.client.get<CategoriesResponse>('/task-categories');
    return response.data;
  }

  // Events
  async getEvents(filters?: {
    start_date?: string;
    end_date?: string;
    limit?: number;
  }): Promise<EventsResponse> {
    const response = await this.client.get<EventsResponse>('/events', {
      params: filters,
    });
    return response.data;
  }

  async createEvent(event: Partial<Event>): Promise<Event> {
    const response = await this.client.post<Event>('/events', event);
    return response.data;
  }

  async updateEvent(id: string, updates: Partial<Event>): Promise<Event> {
    const response = await this.client.put<Event>(`/events/${id}`, updates);
    return response.data;
  }

  async deleteEvent(id: string): Promise<void> {
    await this.client.delete(`/events/${id}`);
  }

  // Search
  async searchContent(query: string, options?: {
    search_types?: string[];
    limit?: number;
    use_semantic?: boolean;
    use_keyword?: boolean;
  }): Promise<SearchResponse> {
    const response = await this.client.get<SearchResponse>('/search', {
      params: {
        query,
        ...options,
      },
    });
    return response.data;
  }

  // Settings
  async getSettings(): Promise<AppSettings> {
    const response = await this.client.get<AppSettings>('/settings');
    return response.data;
  }

  async updateSettings(settings: Partial<AppSettings>): Promise<AppSettings> {
    const response = await this.client.post<AppSettings>('/settings', settings);
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string }> {
    const response = await this.client.get('/health');
    return response.data;
  }
}

// Create singleton instance
export const apiService = new ApiService();
export default apiService;
