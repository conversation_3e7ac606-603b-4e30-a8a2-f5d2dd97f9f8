# AI-Powered Dashboard

A modern, intelligent dashboard with visual transparency and smooth 60fps animations. Built with React, FastAPI, and Mirascope for AI orchestration.

## 🚀 Features

- **Hero Input Bar**: Natural language processing for any input
- **AI Categorization**: Automatically categorizes inputs into Tasks, Events, or AI Questions
- **Visual Transparency**: See every AI processing step with smooth animations
- **Smart Task Management**: AI-generated categories and priority detection
- **Calendar Integration**: Natural language event creation
- **Semantic Search**: Ollama-powered embeddings for intelligent search
- **Real-time Updates**: WebSocket integration for live feedback
- **Persistence**: Input state persists across page refreshes

## 🏗️ Architecture

### Backend (FastAPI + Mirascope)
- **Orchestrator Agent**: Main categorization using AI
- **Specialized Agents**: Task, Calendar, and Search processing
- **Tools**: Calendar, Task, Web Search (LangSearch), and Embedding tools
- **Database**: SQLite with WAL mode for concurrent access
- **WebSocket**: Real-time visual feedback streaming

### Frontend (React + TypeScript)
- **Hero Input Bar**: Main feature with React Hook Form
- **Visual Feedback**: Framer Motion animations following mermaid diagram
- **AI Orchestrator Hook**: Manages complete AI workflow
- **WebSocket Integration**: Real-time communication
- **Persistence**: Local storage for state management

## 🛠️ Setup

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local development)
- Python 3.11+ (for local development)

### Quick Start with Docker

1. **Clone and setup environment**:
```bash
git clone <repository>
cd ai-powered-dashboard
cp .env.example .env
```

2. **Edit `.env` with your API keys**:
```bash
OPENROUTER_API_KEY=your_openrouter_api_key_here
LANGSEARCH_API_KEY=your_langsearch_api_key_here
```

3. **Start the application**:
```bash
npm run dev
```

This will start:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- Ollama: http://localhost:11434

4. **Pull Ollama model** (first time only):
```bash
npm run ollama:pull
```

### Development Setup

#### Backend Development
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### Frontend Development
```bash
cd frontend
npm install
npm run dev
```

## 📋 Available Scripts

### Root Scripts
- `npm run dev` - Start all services with Docker
- `npm run build` - Build all services
- `npm run start` - Start production services
- `npm run stop` - Stop all services
- `npm run clean` - Clean all containers and volumes
- `npm run logs` - View all logs
- `npm run health` - Check service health
- `npm run ollama:pull` - Pull Ollama embedding model

### Frontend Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run type-check` - TypeScript type checking

### Backend Scripts
- `uvicorn app.main:app --reload` - Start development server
- `pytest` - Run tests
- `black app/` - Format code
- `ruff check app/` - Lint code
- `mypy app/` - Type checking

## 🎯 Usage

### Basic Usage
1. Enter any text in the hero input bar
2. Watch the AI processing steps with visual feedback
3. See results automatically categorized and organized

### Example Inputs
- **Tasks**: "Do my biology homework", "Buy groceries"
- **Events**: "Math exam at 2PM tomorrow", "Team meeting next Friday"
- **Questions**: "What is photosynthesis?", "How does AI work?"

### Visual Feedback Flow
The application follows a precise mermaid diagram for visual feedback:
1. **Analyzing** → **Categorizing** → **Category Decision**
2. **Task Flow**: Extract details → Auto-categorize → Check dates → Update lists
3. **Event Flow**: Extract datetime → Check completeness → Add to calendar
4. **Question Flow**: Analyze type → Route to appropriate search → Generate answer

## 🔧 Configuration

### Environment Variables
- `OPENROUTER_API_KEY`: Required for AI processing
- `LANGSEARCH_API_KEY`: Required for web search
- `OLLAMA_URL`: Ollama service URL (default: http://ollama:11434)
- `DATABASE_URL`: SQLite database path
- `DEBUG`: Enable debug mode
- `CORS_ORIGINS`: Allowed CORS origins

### AI Models
- **Default**: meta-llama/llama-3.2-3b-instruct:free
- **Backup**: meta-llama/llama-3.1-8b-instruct:free
- **Embeddings**: nomic-embed-text (via Ollama)

## 🧪 Testing

### Run All Tests
```bash
npm run test
```

### Frontend Tests
```bash
cd frontend && npm test
```

### Backend Tests
```bash
cd backend && pytest
```

### E2E Tests
```bash
npm run test:e2e
```

## 📊 Monitoring

### Health Checks
- Frontend: http://localhost:3000
- Backend: http://localhost:8000/health
- Ollama: http://localhost:11434/api/tags

### Logs
```bash
npm run logs:frontend  # Frontend logs
npm run logs:backend   # Backend logs
npm run logs:ollama    # Ollama logs
```

## 🚀 Deployment

### Production Build
```bash
npm run build
npm run start
```

### Docker Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- **Mirascope**: AI orchestration framework
- **OpenRouter**: LLM API access
- **LangSearch**: Web search API
- **Ollama**: Local embeddings
- **Framer Motion**: Smooth animations
- **Tailwind CSS**: Styling framework
