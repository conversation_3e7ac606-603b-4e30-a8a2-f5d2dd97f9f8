import React from 'react';
import { motion } from 'framer-motion';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday, isSameMonth } from 'date-fns';

import { Event } from '@/types';

interface CalendarViewProps {
  currentDate: Date;
  events: Event[];
  viewMode: 'month' | 'week' | 'day';
  loading: boolean;
}

const CalendarView: React.FC<CalendarViewProps> = ({ 
  currentDate, 
  events, 
  viewMode, 
  loading 
}) => {
  const getEventsForDate = (date: Date) => {
    return events.filter(event => 
      isSameDay(new Date(event.start_time), date)
    );
  };

  const renderMonthView = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    
    // Get all days in the month
    const days = eachDayOfInterval({ start: monthStart, end: monthEnd });
    
    // Add padding days to start from Sunday
    const startPadding = monthStart.getDay();
    const paddingDays = Array.from({ length: startPadding }, (_, i) => {
      const date = new Date(monthStart);
      date.setDate(date.getDate() - (startPadding - i));
      return date;
    });
    
    // Add padding days to end on Saturday
    const endPadding = 6 - monthEnd.getDay();
    const endPaddingDays = Array.from({ length: endPadding }, (_, i) => {
      const date = new Date(monthEnd);
      date.setDate(date.getDate() + (i + 1));
      return date;
    });
    
    const allDays = [...paddingDays, ...days, ...endPaddingDays];

    return (
      <div className="card">
        {/* Calendar Header */}
        <div className="grid grid-cols-7 gap-1 mb-4">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="p-2 text-center text-sm font-medium text-primary-400">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1">
          {allDays.map((day, index) => {
            const dayEvents = getEventsForDate(day);
            const isCurrentMonth = isSameMonth(day, currentDate);
            const isDayToday = isToday(day);

            return (
              <motion.div
                key={day.toISOString()}
                className={`min-h-[100px] p-2 border border-primary-700/50 rounded-lg transition-colors ${
                  isCurrentMonth 
                    ? 'bg-primary-800/50 hover:bg-primary-800' 
                    : 'bg-primary-900/30 text-primary-500'
                } ${
                  isDayToday ? 'ring-2 ring-info/50' : ''
                }`}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.01 }}
                whileHover={{ scale: 1.02 }}
              >
                {/* Day Number */}
                <div className={`text-sm font-medium mb-1 ${
                  isDayToday 
                    ? 'text-info font-bold' 
                    : isCurrentMonth 
                      ? 'text-primary-200' 
                      : 'text-primary-500'
                }`}>
                  {format(day, 'd')}
                </div>

                {/* Events */}
                <div className="space-y-1">
                  {dayEvents.slice(0, 3).map((event, eventIndex) => (
                    <motion.div
                      key={event.id}
                      className="text-xs p-1 bg-info/20 text-info rounded truncate"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: eventIndex * 0.05 }}
                      title={event.title}
                    >
                      {event.title}
                    </motion.div>
                  ))}
                  
                  {dayEvents.length > 3 && (
                    <div className="text-xs text-primary-400">
                      +{dayEvents.length - 3} more
                    </div>
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderWeekView = () => {
    // Get the week containing the current date
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
    
    const weekDays = Array.from({ length: 7 }, (_, i) => {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      return date;
    });

    return (
      <div className="card">
        <div className="grid grid-cols-7 gap-4">
          {weekDays.map((day) => {
            const dayEvents = getEventsForDate(day);
            const isDayToday = isToday(day);

            return (
              <div key={day.toISOString()} className="space-y-2">
                {/* Day Header */}
                <div className={`text-center p-2 rounded-lg ${
                  isDayToday ? 'bg-info/20 text-info' : 'text-primary-300'
                }`}>
                  <div className="text-xs font-medium">
                    {format(day, 'EEE')}
                  </div>
                  <div className="text-lg font-bold">
                    {format(day, 'd')}
                  </div>
                </div>

                {/* Events */}
                <div className="space-y-1 min-h-[200px]">
                  {dayEvents.map((event) => (
                    <motion.div
                      key={event.id}
                      className="text-xs p-2 bg-info/20 text-info rounded"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="font-medium truncate">{event.title}</div>
                      <div className="text-primary-400">
                        {format(new Date(event.start_time), 'HH:mm')}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderDayView = () => {
    const dayEvents = getEventsForDate(currentDate);

    return (
      <div className="card">
        {/* Day Header */}
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-primary-200">
            {format(currentDate, 'EEEE, MMMM d, yyyy')}
          </h3>
        </div>

        {/* Events */}
        <div className="space-y-3">
          {dayEvents.length === 0 ? (
            <div className="text-center py-8 text-primary-400">
              No events scheduled for this day
            </div>
          ) : (
            dayEvents.map((event) => (
              <motion.div
                key={event.id}
                className="p-4 bg-info/10 border border-info/20 rounded-lg"
                whileHover={{ scale: 1.02 }}
              >
                <h4 className="font-medium text-primary-200 mb-1">
                  {event.title}
                </h4>
                <div className="text-sm text-primary-400 mb-2">
                  {format(new Date(event.start_time), 'HH:mm')} - 
                  {format(new Date(event.end_time), 'HH:mm')}
                </div>
                {event.description && (
                  <p className="text-sm text-primary-300">
                    {event.description}
                  </p>
                )}
              </motion.div>
            ))
          )}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="card animate-pulse">
        <div className="h-64 bg-primary-700 rounded"></div>
      </div>
    );
  }

  switch (viewMode) {
    case 'week':
      return renderWeekView();
    case 'day':
      return renderDayView();
    default:
      return renderMonthView();
  }
};

export default CalendarView;
