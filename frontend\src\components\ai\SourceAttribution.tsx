import React from 'react';
import { motion } from 'framer-motion';
import { ExternalLink, Globe, FileText, Database } from 'lucide-react';

import { Source } from '@/types';

interface SourceAttributionProps {
  sources: Source[];
}

const SourceAttribution: React.FC<SourceAttributionProps> = ({ sources }) => {
  const getSourceIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'web':
        return Globe;
      case 'document':
        return FileText;
      case 'database':
        return Database;
      default:
        return Globe;
    }
  };

  const getSourceTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'web':
        return 'text-blue-400 bg-blue-400/20';
      case 'document':
        return 'text-green-400 bg-green-400/20';
      case 'database':
        return 'text-purple-400 bg-purple-400/20';
      default:
        return 'text-primary-400 bg-primary-400/20';
    }
  };

  if (sources.length === 0) {
    return (
      <div className="text-center py-4 text-primary-400">
        No sources available
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {sources.map((source, index) => {
        const IconComponent = getSourceIcon(source.type);
        const colorClass = getSourceTypeColor(source.type);

        return (
          <motion.div
            key={index}
            className="p-4 bg-primary-700/20 rounded-lg border border-primary-700/50 hover:border-primary-600/50 transition-colors"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.01 }}
          >
            <div className="flex items-start gap-3">
              {/* Source Icon */}
              <div className={`p-2 rounded-lg ${colorClass}`}>
                <IconComponent className="w-4 h-4" />
              </div>

              {/* Source Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1">
                    <h4 className="font-medium text-primary-200 mb-1">
                      {source.title}
                    </h4>
                    
                    {source.description && (
                      <p className="text-sm text-primary-400 mb-2">
                        {source.description}
                      </p>
                    )}

                    {/* Source Metadata */}
                    <div className="flex items-center gap-4 text-xs text-primary-500">
                      <span className={`px-2 py-1 rounded-full ${colorClass}`}>
                        {source.type}
                      </span>
                      
                      {source.author && (
                        <span>By {source.author}</span>
                      )}
                      
                      {source.date && (
                        <span>{new Date(source.date).toLocaleDateString()}</span>
                      )}
                      
                      {source.relevance_score && (
                        <span>
                          {Math.round(source.relevance_score * 100)}% relevant
                        </span>
                      )}
                    </div>
                  </div>

                  {/* External Link */}
                  {source.url && (
                    <motion.a
                      href={source.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 text-sm text-info hover:text-info/80 transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <ExternalLink className="w-3 h-3" />
                      <span className="hidden sm:inline">View</span>
                    </motion.a>
                  )}
                </div>

                {/* Source Excerpt */}
                {source.excerpt && (
                  <div className="mt-3 p-3 bg-primary-800/50 rounded border-l-2 border-primary-600">
                    <p className="text-sm text-primary-300 italic">
                      "{source.excerpt}"
                    </p>
                  </div>
                )}

                {/* Tags */}
                {source.tags && source.tags.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-1">
                    {source.tags.map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-2 py-1 bg-primary-600/20 text-primary-400 rounded text-xs"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        );
      })}

      {/* Source Summary */}
      <motion.div
        className="mt-4 p-3 bg-primary-800/30 rounded-lg border border-primary-700/30"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: sources.length * 0.1 + 0.2 }}
      >
        <div className="flex items-center justify-between text-sm">
          <span className="text-primary-400">
            {sources.length} source{sources.length !== 1 ? 's' : ''} referenced
          </span>
          
          <div className="flex gap-4">
            {/* Source type breakdown */}
            {Object.entries(
              sources.reduce((acc, source) => {
                acc[source.type] = (acc[source.type] || 0) + 1;
                return acc;
              }, {} as Record<string, number>)
            ).map(([type, count]) => (
              <span key={type} className="text-primary-500">
                {count} {type}
              </span>
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default SourceAttribution;
