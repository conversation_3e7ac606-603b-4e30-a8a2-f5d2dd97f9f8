import { useCallback } from 'react';

interface PersistenceData {
  inputState?: string;
  lastResults?: any[];
  settings?: any;
  timestamp?: string;
}

const STORAGE_KEYS = {
  INPUT_STATE: 'ai-dashboard-input-state',
  LAST_RESULTS: 'ai-dashboard-last-results',
  SETTINGS: 'ai-dashboard-settings',
  SESSION_DATA: 'ai-dashboard-session'
} as const;

export const usePersistence = () => {
  // Input state persistence (for hero bar)
  const saveInputState = useCallback((inputValue: string) => {
    try {
      localStorage.setItem(STORAGE_KEYS.INPUT_STATE, inputValue);
    } catch (error) {
      console.warn('Failed to save input state:', error);
    }
  }, []);

  const loadInputState = useCallback((): string | null => {
    try {
      return localStorage.getItem(STORAGE_KEYS.INPUT_STATE);
    } catch (error) {
      console.warn('Failed to load input state:', error);
      return null;
    }
  }, []);

  const clearInputState = useCallback(() => {
    try {
      localStorage.removeItem(STORAGE_KEYS.INPUT_STATE);
    } catch (error) {
      console.warn('Failed to clear input state:', error);
    }
  }, []);

  // Results persistence
  const saveLastResults = useCallback((results: any[]) => {
    try {
      const data = {
        results,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem(STORAGE_KEYS.LAST_RESULTS, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save last results:', error);
    }
  }, []);

  const loadLastResults = useCallback((): any[] | null => {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.LAST_RESULTS);
      if (!data) return null;
      
      const parsed = JSON.parse(data);
      
      // Check if data is not too old (e.g., 24 hours)
      const timestamp = new Date(parsed.timestamp);
      const now = new Date();
      const hoursDiff = (now.getTime() - timestamp.getTime()) / (1000 * 60 * 60);
      
      if (hoursDiff > 24) {
        localStorage.removeItem(STORAGE_KEYS.LAST_RESULTS);
        return null;
      }
      
      return parsed.results;
    } catch (error) {
      console.warn('Failed to load last results:', error);
      return null;
    }
  }, []);

  // Settings persistence
  const saveSettings = useCallback((settings: any) => {
    try {
      localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
    } catch (error) {
      console.warn('Failed to save settings:', error);
    }
  }, []);

  const loadSettings = useCallback((): any | null => {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.SETTINGS);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn('Failed to load settings:', error);
      return null;
    }
  }, []);

  // Session data persistence
  const saveSessionData = useCallback((data: PersistenceData) => {
    try {
      const sessionData = {
        ...data,
        timestamp: new Date().toISOString()
      };
      sessionStorage.setItem(STORAGE_KEYS.SESSION_DATA, JSON.stringify(sessionData));
    } catch (error) {
      console.warn('Failed to save session data:', error);
    }
  }, []);

  const loadSessionData = useCallback((): PersistenceData | null => {
    try {
      const data = sessionStorage.getItem(STORAGE_KEYS.SESSION_DATA);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn('Failed to load session data:', error);
      return null;
    }
  }, []);

  const clearSessionData = useCallback(() => {
    try {
      sessionStorage.removeItem(STORAGE_KEYS.SESSION_DATA);
    } catch (error) {
      console.warn('Failed to clear session data:', error);
    }
  }, []);

  // Clear all data
  const clearAllData = useCallback(() => {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
      });
    } catch (error) {
      console.warn('Failed to clear all data:', error);
    }
  }, []);

  // Check storage availability
  const isStorageAvailable = useCallback((type: 'localStorage' | 'sessionStorage' = 'localStorage') => {
    try {
      const storage = type === 'localStorage' ? localStorage : sessionStorage;
      const test = '__storage_test__';
      storage.setItem(test, test);
      storage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }, []);

  // Get storage usage info
  const getStorageInfo = useCallback(() => {
    if (!isStorageAvailable()) {
      return { available: false, used: 0, total: 0 };
    }

    try {
      let used = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }

      // Estimate total available (usually 5-10MB)
      const total = 5 * 1024 * 1024; // 5MB estimate

      return {
        available: true,
        used,
        total,
        percentage: (used / total) * 100
      };
    } catch (error) {
      console.warn('Failed to get storage info:', error);
      return { available: false, used: 0, total: 0 };
    }
  }, [isStorageAvailable]);

  return {
    // Input state
    saveInputState,
    loadInputState,
    clearInputState,

    // Results
    saveLastResults,
    loadLastResults,

    // Settings
    saveSettings,
    loadSettings,

    // Session data
    saveSessionData,
    loadSessionData,
    clearSessionData,

    // Utilities
    clearAllData,
    isStorageAvailable,
    getStorageInfo
  };
};
