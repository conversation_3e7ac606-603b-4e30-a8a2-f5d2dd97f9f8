import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Filter, ChevronDown, X } from 'lucide-react';

import { TaskPriority } from '@/types';
import { useTasksStore } from '@/stores/tasksStore';

const TaskFilters: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  const { 
    categories, 
    filters, 
    setFilters,
    fetchTasks 
  } = useTasksStore();

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    fetchTasks();
  };

  const clearFilters = () => {
    setFilters({});
    fetchTasks();
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== undefined && value !== '');

  return (
    <div className="relative">
      {/* Filter Button */}
      <motion.button
        className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
          hasActiveFilters
            ? 'bg-primary-600 border-primary-500 text-white'
            : 'bg-primary-800 border-primary-700 text-primary-300 hover:border-primary-600'
        }`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => setIsOpen(!isOpen)}
      >
        <Filter className="w-4 h-4" />
        <span>Filters</span>
        {hasActiveFilters && (
          <span className="bg-primary-400 text-primary-900 text-xs px-1.5 py-0.5 rounded-full">
            {Object.values(filters).filter(v => v !== undefined && v !== '').length}
          </span>
        )}
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </motion.button>

      {/* Filter Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute top-full right-0 mt-2 w-80 bg-primary-800 border border-primary-700 rounded-xl shadow-xl z-10"
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <div className="p-4 space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-primary-200">Filter Tasks</h3>
                {hasActiveFilters && (
                  <motion.button
                    className="text-xs text-primary-400 hover:text-primary-300 flex items-center gap-1"
                    whileHover={{ scale: 1.05 }}
                    onClick={clearFilters}
                  >
                    <X className="w-3 h-3" />
                    Clear All
                  </motion.button>
                )}
              </div>

              {/* Completion Status */}
              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Status
                </label>
                <select
                  value={filters.completed === undefined ? '' : filters.completed.toString()}
                  onChange={(e) => {
                    const value = e.target.value;
                    handleFilterChange('completed', value === '' ? undefined : value === 'true');
                  }}
                  className="input text-sm"
                >
                  <option value="">All Tasks</option>
                  <option value="false">Pending</option>
                  <option value="true">Completed</option>
                </select>
              </div>

              {/* Priority */}
              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Priority
                </label>
                <select
                  value={filters.priority || ''}
                  onChange={(e) => handleFilterChange('priority', e.target.value || undefined)}
                  className="input text-sm"
                >
                  <option value="">All Priorities</option>
                  <option value="urgent">🔥 Urgent</option>
                  <option value="high">⚡ High</option>
                  <option value="medium">📋 Medium</option>
                  <option value="low">🌱 Low</option>
                </select>
              </div>

              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Category
                </label>
                <select
                  value={filters.category || ''}
                  onChange={(e) => handleFilterChange('category', e.target.value || undefined)}
                  className="input text-sm"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              {/* Quick Filters */}
              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Quick Filters
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <motion.button
                    className="text-xs px-3 py-2 bg-primary-700/50 hover:bg-primary-700 rounded-lg transition-colors text-left"
                    whileHover={{ scale: 1.02 }}
                    onClick={() => {
                      handleFilterChange('completed', false);
                      handleFilterChange('priority', 'urgent');
                    }}
                  >
                    🔥 Urgent Tasks
                  </motion.button>
                  
                  <motion.button
                    className="text-xs px-3 py-2 bg-primary-700/50 hover:bg-primary-700 rounded-lg transition-colors text-left"
                    whileHover={{ scale: 1.02 }}
                    onClick={() => {
                      handleFilterChange('completed', true);
                    }}
                  >
                    ✅ Completed
                  </motion.button>
                  
                  <motion.button
                    className="text-xs px-3 py-2 bg-primary-700/50 hover:bg-primary-700 rounded-lg transition-colors text-left"
                    whileHover={{ scale: 1.02 }}
                    onClick={() => {
                      const today = new Date();
                      today.setHours(23, 59, 59, 999);
                      // Note: This would need backend support for due date filtering
                    }}
                  >
                    📅 Due Today
                  </motion.button>
                  
                  <motion.button
                    className="text-xs px-3 py-2 bg-primary-700/50 hover:bg-primary-700 rounded-lg transition-colors text-left"
                    whileHover={{ scale: 1.02 }}
                    onClick={() => {
                      handleFilterChange('category', 'Education');
                    }}
                  >
                    🎓 Education
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 z-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default TaskFilters;
