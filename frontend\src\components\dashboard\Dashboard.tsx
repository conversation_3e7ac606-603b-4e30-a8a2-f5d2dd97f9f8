import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Calendar, CheckSquare, Brain, TrendingUp } from 'lucide-react';

import HeroInputBar from './HeroInputBar';
import QuickStats from './QuickStats';
import RecentActivity from './RecentActivity';
import { usePersistence } from '@/hooks/usePersistence';

const Dashboard: React.FC = () => {
  const [recentResults, setRecentResults] = useState<any[]>([]);
  const { loadLastResults, saveLastResults } = usePersistence();

  useEffect(() => {
    // Load recent results on mount
    const savedResults = loadLastResults();
    if (savedResults) {
      setRecentResults(savedResults);
    }
  }, [loadLastResults]);

  const handleNewInput = (input: string) => {
    // Add to recent activity
    const newActivity = {
      id: Date.now(),
      input,
      timestamp: new Date().toISOString(),
      type: 'input'
    };

    const updatedResults = [newActivity, ...recentResults.slice(0, 9)]; // Keep last 10
    setRecentResults(updatedResults);
    saveLastResults(updatedResults);
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section with Input Bar */}
      <section className="mb-12">
        <HeroInputBar onSubmit={handleNewInput} />
      </section>

      {/* Quick Stats Grid */}
      <section className="mb-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <QuickStats />
        </motion.div>
      </section>

      {/* Main Content Grid */}
      <section className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Activity */}
        <motion.div
          className="lg:col-span-2"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <RecentActivity activities={recentResults} />
        </motion.div>

        {/* Quick Actions Sidebar */}
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          {/* AI Insights Card */}
          <div className="card">
            <div className="flex items-center gap-3 mb-4">
              <Brain className="w-6 h-6 text-primary-400" />
              <h3 className="text-lg font-semibold text-primary-200">AI Insights</h3>
            </div>
            <div className="space-y-3">
              <div className="p-3 bg-primary-700/30 rounded-lg">
                <p className="text-sm text-primary-300">
                  You've been most productive with <span className="text-primary-200 font-medium">Education</span> tasks this week.
                </p>
              </div>
              <div className="p-3 bg-primary-700/30 rounded-lg">
                <p className="text-sm text-primary-300">
                  Consider scheduling more time for <span className="text-primary-200 font-medium">Personal</span> tasks.
                </p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card">
            <h3 className="text-lg font-semibold text-primary-200 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <motion.button
                className="w-full flex items-center gap-3 p-3 bg-primary-700/30 hover:bg-primary-700/50 rounded-lg transition-colors text-left"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <CheckSquare className="w-5 h-5 text-success" />
                <div>
                  <div className="font-medium text-primary-200">View All Tasks</div>
                  <div className="text-xs text-primary-400">Manage your to-do list</div>
                </div>
              </motion.button>

              <motion.button
                className="w-full flex items-center gap-3 p-3 bg-primary-700/30 hover:bg-primary-700/50 rounded-lg transition-colors text-left"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Calendar className="w-5 h-5 text-info" />
                <div>
                  <div className="font-medium text-primary-200">Open Calendar</div>
                  <div className="text-xs text-primary-400">Check your schedule</div>
                </div>
              </motion.button>

              <motion.button
                className="w-full flex items-center gap-3 p-3 bg-primary-700/30 hover:bg-primary-700/50 rounded-lg transition-colors text-left"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <TrendingUp className="w-5 h-5 text-warning" />
                <div>
                  <div className="font-medium text-primary-200">View Analytics</div>
                  <div className="text-xs text-primary-400">Track your progress</div>
                </div>
              </motion.button>
            </div>
          </div>

          {/* Tips Card */}
          <div className="card">
            <h3 className="text-lg font-semibold text-primary-200 mb-4">💡 Pro Tips</h3>
            <div className="space-y-3 text-sm text-primary-300">
              <div className="p-3 bg-primary-700/20 rounded-lg">
                <p>Try natural language: "Math exam tomorrow at 2PM" automatically creates a calendar event.</p>
              </div>
              <div className="p-3 bg-primary-700/20 rounded-lg">
                <p>Ask questions like "What is photosynthesis?" for instant AI-powered answers.</p>
              </div>
              <div className="p-3 bg-primary-700/20 rounded-lg">
                <p>Tasks are automatically categorized and prioritized by AI intelligence.</p>
              </div>
            </div>
          </div>
        </motion.div>
      </section>
    </div>
  );
};

export default Dashboard;
