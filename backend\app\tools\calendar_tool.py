"""Calendar operations tool using Mirascope patterns."""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from mirascope import BaseTool
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.models.database import EventModel
from app.models.pydantic_models import Event
from app.database.connection import get_async_session

logger = logging.getLogger(__name__)


class CalendarTool(BaseTool):
    """Mirascope tool for calendar operations."""
    
    async def create_event(
        self,
        title: str,
        start_time: datetime,
        description: Optional[str] = None,
        end_time: Optional[datetime] = None,
        location: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a new calendar event.
        
        Args:
            title: Event title
            start_time: Event start time
            description: Optional event description
            end_time: Optional event end time
            location: Optional event location
            
        Returns:
            Dictionary with event details and success status
        """
        try:
            async for session in get_async_session():
                # Check for conflicts
                conflicts = await self._check_conflicts(session, start_time, end_time)
                
                if conflicts:
                    return {
                        "success": False,
                        "error": "Time conflict detected",
                        "conflicts": [
                            {
                                "title": conflict.title,
                                "start_time": conflict.start_time.isoformat(),
                                "end_time": conflict.end_time.isoformat() if conflict.end_time else None
                            }
                            for conflict in conflicts
                        ]
                    }
                
                # Create new event
                event = EventModel(
                    title=title,
                    description=description,
                    start_time=start_time,
                    end_time=end_time,
                    location=location
                )
                
                session.add(event)
                await session.commit()
                await session.refresh(event)
                
                logger.info(f"Created event: {event.title}")
                
                return {
                    "success": True,
                    "event": {
                        "id": event.id,
                        "title": event.title,
                        "description": event.description,
                        "start_time": event.start_time.isoformat(),
                        "end_time": event.end_time.isoformat() if event.end_time else None,
                        "location": event.location,
                        "created_at": event.created_at.isoformat()
                    }
                }
                
        except Exception as e:
            logger.error(f"Error creating event: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_events(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 50
    ) -> Dict[str, Any]:
        """
        Get events within a date range.
        
        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter
            limit: Maximum number of events to return
            
        Returns:
            Dictionary with events list and metadata
        """
        try:
            async for session in get_async_session():
                query = select(EventModel)
                
                # Apply date filters
                if start_date:
                    query = query.where(EventModel.start_time >= start_date)
                if end_date:
                    query = query.where(EventModel.start_time <= end_date)
                
                # Order by start time and limit
                query = query.order_by(EventModel.start_time).limit(limit)
                
                result = await session.execute(query)
                events = result.scalars().all()
                
                return {
                    "success": True,
                    "events": [
                        {
                            "id": event.id,
                            "title": event.title,
                            "description": event.description,
                            "start_time": event.start_time.isoformat(),
                            "end_time": event.end_time.isoformat() if event.end_time else None,
                            "location": event.location,
                            "created_at": event.created_at.isoformat()
                        }
                        for event in events
                    ],
                    "count": len(events)
                }
                
        except Exception as e:
            logger.error(f"Error getting events: {e}")
            return {
                "success": False,
                "error": str(e),
                "events": []
            }
    
    async def update_event(
        self,
        event_id: int,
        title: Optional[str] = None,
        description: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        location: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update an existing event.
        
        Args:
            event_id: ID of event to update
            title: New title (optional)
            description: New description (optional)
            start_time: New start time (optional)
            end_time: New end time (optional)
            location: New location (optional)
            
        Returns:
            Dictionary with updated event details and success status
        """
        try:
            async for session in get_async_session():
                # Get existing event
                result = await session.execute(
                    select(EventModel).where(EventModel.id == event_id)
                )
                event = result.scalar_one_or_none()
                
                if not event:
                    return {
                        "success": False,
                        "error": "Event not found"
                    }
                
                # Update fields if provided
                if title is not None:
                    event.title = title
                if description is not None:
                    event.description = description
                if start_time is not None:
                    event.start_time = start_time
                if end_time is not None:
                    event.end_time = end_time
                if location is not None:
                    event.location = location
                
                await session.commit()
                await session.refresh(event)
                
                logger.info(f"Updated event: {event.title}")
                
                return {
                    "success": True,
                    "event": {
                        "id": event.id,
                        "title": event.title,
                        "description": event.description,
                        "start_time": event.start_time.isoformat(),
                        "end_time": event.end_time.isoformat() if event.end_time else None,
                        "location": event.location,
                        "updated_at": event.updated_at.isoformat()
                    }
                }
                
        except Exception as e:
            logger.error(f"Error updating event: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def delete_event(self, event_id: int) -> Dict[str, Any]:
        """
        Delete an event.
        
        Args:
            event_id: ID of event to delete
            
        Returns:
            Dictionary with success status
        """
        try:
            async for session in get_async_session():
                result = await session.execute(
                    select(EventModel).where(EventModel.id == event_id)
                )
                event = result.scalar_one_or_none()
                
                if not event:
                    return {
                        "success": False,
                        "error": "Event not found"
                    }
                
                await session.delete(event)
                await session.commit()
                
                logger.info(f"Deleted event: {event.title}")
                
                return {
                    "success": True,
                    "message": f"Event '{event.title}' deleted successfully"
                }
                
        except Exception as e:
            logger.error(f"Error deleting event: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _check_conflicts(
        self, 
        session: AsyncSession, 
        start_time: datetime, 
        end_time: Optional[datetime]
    ) -> List[EventModel]:
        """Check for scheduling conflicts."""
        if not end_time:
            end_time = start_time + timedelta(hours=1)  # Default 1 hour duration
        
        # Find overlapping events
        query = select(EventModel).where(
            and_(
                EventModel.start_time < end_time,
                EventModel.end_time > start_time
            )
        )
        
        result = await session.execute(query)
        return result.scalars().all()
