"""Web search tool using LangSearch API integration."""

import logging
from typing import List, Dict, Any, Optional
import httpx
import asyncio

# from mirascope import BaseTool

from app.models.pydantic_models import SearchResult
from app.config import settings

logger = logging.getLogger(__name__)


class WebSearchTool:
    """Mirascope tool for web search using LangSearch API."""
    
    def __init__(self):
        self.api_key = settings.langsearch_api_key
        self.base_url = "https://api.langsearch.com/v1"
        self.timeout = 30
    
    async def search_web(
        self,
        query: str,
        num_results: int = 5,
        include_snippets: bool = True,
        filter_domains: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Search the web using LangSearch API.
        
        Args:
            query: Search query
            num_results: Number of results to return (max 10)
            include_snippets: Whether to include content snippets
            filter_domains: Optional list of domains to search within
            
        Returns:
            Dictionary with search results and metadata
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                payload = {
                    "query": query,
                    "num_results": min(num_results, 10),
                    "include_snippets": include_snippets,
                    "format": "json"
                }
                
                if filter_domains:
                    payload["domains"] = filter_domains
                
                logger.info(f"Searching web for: {query}")
                
                response = await client.post(
                    f"{self.base_url}/search",
                    headers=headers,
                    json=payload
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Process results into SearchResult format
                    search_results = []
                    for result in data.get("results", []):
                        search_results.append({
                            "title": result.get("title", ""),
                            "content": result.get("snippet", ""),
                            "source": result.get("domain", ""),
                            "url": result.get("url", ""),
                            "relevance_score": result.get("score", 0.5)
                        })
                    
                    return {
                        "success": True,
                        "query": query,
                        "results": search_results,
                        "total_results": len(search_results),
                        "search_time": data.get("search_time", 0),
                        "sources": list(set([r["source"] for r in search_results]))
                    }
                else:
                    logger.error(f"LangSearch API error: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "error": f"API error: {response.status_code}",
                        "results": []
                    }
                    
        except httpx.TimeoutException:
            logger.error(f"Timeout searching for: {query}")
            return {
                "success": False,
                "error": "Search timeout",
                "results": []
            }
        except Exception as e:
            logger.error(f"Error in web search: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    async def search_multiple_queries(
        self,
        queries: List[str],
        num_results_per_query: int = 3
    ) -> Dict[str, Any]:
        """
        Search multiple queries concurrently.
        
        Args:
            queries: List of search queries
            num_results_per_query: Number of results per query
            
        Returns:
            Dictionary with combined search results
        """
        try:
            # Create tasks for concurrent searches
            tasks = [
                self.search_web(query, num_results_per_query)
                for query in queries
            ]
            
            # Execute searches concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Combine results
            all_results = []
            all_sources = set()
            successful_searches = 0
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Error in query '{queries[i]}': {result}")
                    continue
                
                if result.get("success"):
                    successful_searches += 1
                    all_results.extend(result.get("results", []))
                    all_sources.update(result.get("sources", []))
            
            # Sort by relevance score
            all_results.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
            
            return {
                "success": successful_searches > 0,
                "queries": queries,
                "results": all_results,
                "total_results": len(all_results),
                "successful_searches": successful_searches,
                "sources": list(all_sources)
            }
            
        except Exception as e:
            logger.error(f"Error in multiple query search: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    async def search_with_context(
        self,
        query: str,
        context: str,
        num_results: int = 5
    ) -> Dict[str, Any]:
        """
        Search with additional context to improve relevance.
        
        Args:
            query: Main search query
            context: Additional context to improve search
            num_results: Number of results to return
            
        Returns:
            Dictionary with contextual search results
        """
        try:
            # Enhance query with context
            enhanced_query = f"{query} {context}".strip()
            
            # Perform search
            result = await self.search_web(enhanced_query, num_results)
            
            if result.get("success"):
                # Add context information to result
                result["original_query"] = query
                result["context"] = context
                result["enhanced_query"] = enhanced_query
            
            return result
            
        except Exception as e:
            logger.error(f"Error in contextual search: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    async def get_page_content(self, url: str) -> Dict[str, Any]:
        """
        Get full content from a specific URL.
        
        Args:
            url: URL to fetch content from
            
        Returns:
            Dictionary with page content
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                payload = {
                    "url": url,
                    "format": "text"
                }
                
                response = await client.post(
                    f"{self.base_url}/extract",
                    headers=headers,
                    json=payload
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    return {
                        "success": True,
                        "url": url,
                        "title": data.get("title", ""),
                        "content": data.get("content", ""),
                        "word_count": len(data.get("content", "").split()),
                        "extracted_at": data.get("extracted_at")
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Failed to extract content: {response.status_code}",
                        "url": url
                    }
                    
        except Exception as e:
            logger.error(f"Error extracting page content: {e}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
