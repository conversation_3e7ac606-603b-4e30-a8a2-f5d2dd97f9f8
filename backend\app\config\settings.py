"""Application configuration using Pydantic settings."""

import os
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # API Configuration
    openrouter_api_key: str = Field(default="", env="OPENROUTER_API_KEY")
    langsearch_api_key: str = Field(default="", env="LANGSEARCH_API_KEY")
    
    # Database Configuration
    database_url: str = Field(default="sqlite:///data/dashboard.db", env="DATABASE_URL")
    
    # Ollama Configuration
    ollama_url: str = Field(default="http://ollama:11434", env="OLLAMA_URL")
    ollama_model: str = Field(default="nomic-embed-text", env="OLLAMA_MODEL")
    
    # LLM Configuration
    default_model: str = Field(
        default="tngtech/deepseek-r1t2-chimera:free", 
        env="DEFAULT_MODEL"
    )
    backup_model: str = Field(
        default="google/gemini-2.0-flash-exp:free", 
        env="BACKUP_MODEL"
    )
    
    # CORS Configuration
    cors_origins: List[str] = Field(
        default=["http://localhost:3000"], 
        env="CORS_ORIGINS"
    )
    
    # Development Settings
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Performance Settings
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    retry_attempts: int = Field(default=3, env="RETRY_ATTEMPTS")
    
    # Animation Settings
    animation_duration: int = Field(default=1000, env="ANIMATION_DURATION")
    enable_animations: bool = Field(default=True, env="ENABLE_ANIMATIONS")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()

# Set OpenAI environment variables for Mirascope
os.environ["OPENAI_API_KEY"] = settings.openrouter_api_key
os.environ["OPENAI_BASE_URL"] = "https://openrouter.ai/api/v1"
