"""SQLAlchemy database models."""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, JSON
from sqlalchemy.sql import func
from datetime import datetime

from app.database.connection import Base


class TaskModel(Base):
    """Task database model."""
    __tablename__ = "tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=True)
    ai_generated_category = Column(String(100), nullable=True, index=True)
    priority = Column(String(20), default="medium")
    due_date = Column(DateTime, nullable=True)
    completed = Column(Boolean, default=False, index=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class EventModel(Base):
    """Event database model."""
    __tablename__ = "events"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    start_time = Column(DateTime, nullable=False, index=True)
    end_time = Column(DateTime, nullable=True)
    location = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class UserInputModel(Base):
    """User input history model."""
    __tablename__ = "user_inputs"
    
    id = Column(Integer, primary_key=True, index=True)
    text = Column(Text, nullable=False)
    category = Column(String(50), nullable=True, index=True)
    confidence = Column(Float, nullable=True)
    processing_steps = Column(JSON, nullable=True)
    result_type = Column(String(50), nullable=True)
    result_id = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=func.now())


class EmbeddingModel(Base):
    """Embeddings for semantic search."""
    __tablename__ = "embeddings"
    
    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    content_type = Column(String(50), nullable=False, index=True)  # 'task', 'event', 'input'
    content_id = Column(Integer, nullable=False)
    embedding = Column(JSON, nullable=False)  # Store as JSON array
    model_name = Column(String(100), nullable=False)
    created_at = Column(DateTime, default=func.now())
    
    # Create composite index for efficient lookups
    __table_args__ = (
        {'sqlite_autoincrement': True}
    )
