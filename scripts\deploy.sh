#!/bin/bash

# AI-Powered Dashboard Deployment Script
# This script handles the complete deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ai-powered-dashboard"
BACKEND_PORT=8000
FRONTEND_PORT=3000

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 18+ and try again."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed. Please install Python 3.11+ and try again."
        exit 1
    fi
    
    # Check pip
    if ! command -v pip &> /dev/null && ! command -v pip3 &> /dev/null; then
        log_error "pip is not installed. Please install pip and try again."
        exit 1
    fi
    
    log_success "System requirements check passed"
}

check_environment() {
    log_info "Checking environment configuration..."
    
    if [ ! -f ".env" ]; then
        log_warning ".env file not found. Creating from template..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_warning "Please edit .env file with your API keys before continuing."
            log_warning "Required: OPENROUTER_API_KEY, LANGSEARCH_API_KEY"
            read -p "Press Enter after updating .env file..."
        else
            log_error ".env.example file not found. Please create .env file manually."
            exit 1
        fi
    fi
    
    # Check for required environment variables
    source .env
    
    if [ -z "$OPENROUTER_API_KEY" ] || [ "$OPENROUTER_API_KEY" = "your_openrouter_api_key_here" ]; then
        log_warning "OPENROUTER_API_KEY not set in .env file"
    fi
    
    if [ -z "$LANGSEARCH_API_KEY" ] || [ "$LANGSEARCH_API_KEY" = "your_langsearch_api_key_here" ]; then
        log_warning "LANGSEARCH_API_KEY not set in .env file"
    fi
    
    log_success "Environment configuration checked"
}

install_backend_dependencies() {
    log_info "Installing backend dependencies..."
    
    cd backend
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        log_info "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    pip install -r requirements.txt
    
    cd ..
    log_success "Backend dependencies installed"
}

install_frontend_dependencies() {
    log_info "Installing frontend dependencies..."
    
    cd frontend
    npm install
    cd ..
    
    log_success "Frontend dependencies installed"
}

run_tests() {
    log_info "Running tests..."
    
    # Backend tests
    log_info "Running backend tests..."
    cd backend
    source venv/bin/activate
    
    if command -v pytest &> /dev/null; then
        pytest tests/ -v
        log_success "Backend tests passed"
    else
        log_warning "pytest not found, skipping backend tests"
    fi
    
    cd ..
    
    # Frontend tests
    log_info "Running frontend tests..."
    cd frontend
    
    if npm run test:run &> /dev/null; then
        log_success "Frontend tests passed"
    else
        log_warning "Frontend tests failed or not configured"
    fi
    
    cd ..
}

build_frontend() {
    log_info "Building frontend for production..."
    
    cd frontend
    npm run build
    cd ..
    
    log_success "Frontend built successfully"
}

start_backend() {
    log_info "Starting backend server..."
    
    cd backend
    source venv/bin/activate
    
    # Initialize database
    python -c "
import asyncio
from app.database.connection import init_database, create_sample_data
async def main():
    await init_database()
    await create_sample_data()
asyncio.run(main())
"
    
    # Start server in background
    nohup uvicorn app.main:app --host 0.0.0.0 --port $BACKEND_PORT > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../logs/backend.pid
    
    cd ..
    
    # Wait for backend to start
    sleep 5
    
    # Check if backend is running
    if curl -f http://localhost:$BACKEND_PORT/health > /dev/null 2>&1; then
        log_success "Backend server started on port $BACKEND_PORT"
    else
        log_error "Failed to start backend server"
        exit 1
    fi
}

start_frontend() {
    log_info "Starting frontend server..."
    
    cd frontend
    
    # Start server in background
    nohup npm run preview > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../logs/frontend.pid
    
    cd ..
    
    # Wait for frontend to start
    sleep 5
    
    # Check if frontend is running
    if curl -f http://localhost:$FRONTEND_PORT > /dev/null 2>&1; then
        log_success "Frontend server started on port $FRONTEND_PORT"
    else
        log_error "Failed to start frontend server"
        exit 1
    fi
}

create_logs_directory() {
    if [ ! -d "logs" ]; then
        mkdir logs
        log_info "Created logs directory"
    fi
}

stop_servers() {
    log_info "Stopping servers..."
    
    if [ -f "logs/backend.pid" ]; then
        BACKEND_PID=$(cat logs/backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            log_info "Backend server stopped"
        fi
        rm logs/backend.pid
    fi
    
    if [ -f "logs/frontend.pid" ]; then
        FRONTEND_PID=$(cat logs/frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            log_info "Frontend server stopped"
        fi
        rm logs/frontend.pid
    fi
}

show_status() {
    log_info "Checking server status..."
    
    # Check backend
    if curl -f http://localhost:$BACKEND_PORT/health > /dev/null 2>&1; then
        log_success "Backend: Running on http://localhost:$BACKEND_PORT"
    else
        log_error "Backend: Not running"
    fi
    
    # Check frontend
    if curl -f http://localhost:$FRONTEND_PORT > /dev/null 2>&1; then
        log_success "Frontend: Running on http://localhost:$FRONTEND_PORT"
    else
        log_error "Frontend: Not running"
    fi
}

# Main deployment function
deploy() {
    log_info "Starting deployment of $PROJECT_NAME..."
    
    create_logs_directory
    check_requirements
    check_environment
    install_backend_dependencies
    install_frontend_dependencies
    
    if [ "$1" != "--skip-tests" ]; then
        run_tests
    fi
    
    build_frontend
    start_backend
    start_frontend
    
    log_success "Deployment completed successfully!"
    echo ""
    log_info "🚀 AI-Powered Dashboard is now running:"
    log_info "   Frontend: http://localhost:$FRONTEND_PORT"
    log_info "   Backend:  http://localhost:$BACKEND_PORT"
    log_info "   API Docs: http://localhost:$BACKEND_PORT/docs"
    echo ""
    log_info "📝 Logs are available in the 'logs' directory"
    log_info "🛑 To stop servers, run: $0 stop"
}

# Command handling
case "$1" in
    "deploy"|"")
        deploy $2
        ;;
    "stop")
        stop_servers
        ;;
    "status")
        show_status
        ;;
    "restart")
        stop_servers
        sleep 2
        deploy --skip-tests
        ;;
    *)
        echo "Usage: $0 {deploy|stop|status|restart}"
        echo ""
        echo "Commands:"
        echo "  deploy    - Full deployment (default)"
        echo "  stop      - Stop all servers"
        echo "  status    - Check server status"
        echo "  restart   - Restart servers without tests"
        echo ""
        echo "Options:"
        echo "  --skip-tests  - Skip running tests during deployment"
        exit 1
        ;;
esac
