import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { TasksState, Task, TaskPriority } from '@/types';
import apiService from '@/services/api';

interface TasksStore extends TasksState {
  // Actions
  setTasks: (tasks: Task[]) => void;
  addTask: (task: Task) => void;
  updateTask: (id: string, updates: Partial<Task>) => void;
  removeTask: (id: string) => void;
  setCategories: (categories: string[]) => void;
  setFilters: (filters: Partial<TasksState['filters']>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Async actions
  fetchTasks: () => Promise<void>;
  createTask: (task: Partial<Task>) => Promise<void>;
  updateTaskAsync: (id: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
  fetchCategories: () => Promise<void>;
}

const initialState: TasksState = {
  tasks: [],
  categories: [],
  filters: {},
  loading: false,
  error: undefined,
};

export const useTasksStore = create<TasksStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    // Sync actions
    setTasks: (tasks: Task[]) => set({ tasks }),
    
    addTask: (task: Task) => 
      set((state) => ({ tasks: [...state.tasks, task] })),
    
    updateTask: (id: string, updates: Partial<Task>) => 
      set((state) => ({
        tasks: state.tasks.map(task => 
          task.id === id ? { ...task, ...updates } : task
        )
      })),
    
    removeTask: (id: string) => 
      set((state) => ({
        tasks: state.tasks.filter(task => task.id !== id)
      })),
    
    setCategories: (categories: string[]) => set({ categories }),
    
    setFilters: (filters: Partial<TasksState['filters']>) => 
      set((state) => ({ filters: { ...state.filters, ...filters } })),
    
    setLoading: (loading: boolean) => set({ loading }),
    
    setError: (error: string | null) => set({ error }),

    // Async actions
    fetchTasks: async () => {
      const { setLoading, setError, setTasks, filters } = get();
      
      setLoading(true);
      setError(null);
      
      try {
        const response = await apiService.getTasks(filters);
        setTasks(response.tasks);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to fetch tasks');
      } finally {
        setLoading(false);
      }
    },

    createTask: async (taskData: Partial<Task>) => {
      const { setLoading, setError, addTask } = get();
      
      setLoading(true);
      setError(null);
      
      try {
        const newTask = await apiService.createTask(taskData);
        addTask(newTask);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to create task');
        throw error;
      } finally {
        setLoading(false);
      }
    },

    updateTaskAsync: async (id: string, updates: Partial<Task>) => {
      const { setLoading, setError, updateTask } = get();
      
      setLoading(true);
      setError(null);
      
      try {
        const updatedTask = await apiService.updateTask(id, updates);
        updateTask(id, updatedTask);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to update task');
        throw error;
      } finally {
        setLoading(false);
      }
    },

    deleteTask: async (id: string) => {
      const { setLoading, setError, removeTask } = get();
      
      setLoading(true);
      setError(null);
      
      try {
        await apiService.deleteTask(id);
        removeTask(id);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to delete task');
        throw error;
      } finally {
        setLoading(false);
      }
    },

    fetchCategories: async () => {
      const { setError, setCategories } = get();
      
      try {
        const response = await apiService.getTaskCategories();
        setCategories(response.all_categories);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to fetch categories');
      }
    },
  }))
);

// Selectors
export const selectTasks = (state: TasksStore) => state.tasks;
export const selectTasksByCategory = (category: string) => (state: TasksStore) => 
  state.tasks.filter(task => 
    task.ai_generated_category === category || task.category === category
  );
export const selectTasksByPriority = (priority: TaskPriority) => (state: TasksStore) => 
  state.tasks.filter(task => task.priority === priority);
export const selectCompletedTasks = (state: TasksStore) => 
  state.tasks.filter(task => task.completed);
export const selectPendingTasks = (state: TasksStore) => 
  state.tasks.filter(task => !task.completed);
export const selectTasksLoading = (state: TasksStore) => state.loading;
export const selectTasksError = (state: TasksStore) => state.error;
