{"name": "@jest/types", "version": "29.6.3", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-types"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "devDependencies": {"@tsd/typescript": "^5.0.4", "tsd-lite": "^0.7.0"}, "publishConfig": {"access": "public"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b"}