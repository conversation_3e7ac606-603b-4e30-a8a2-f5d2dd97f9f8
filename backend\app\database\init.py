"""Database initialization and setup."""

import asyncio
import logging
from pathlib import Path
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy import text

from app.models.database import Base
from app.config.settings import settings

logger = logging.getLogger(__name__)

# Create async engine with SQLite
DATABASE_URL = f"sqlite+aiosqlite:///{settings.database_path}"

engine = create_async_engine(
    DATABASE_URL,
    echo=settings.debug,
    poolclass=StaticPool,
    connect_args={
        "check_same_thread": False,
        "timeout": 30,
    },
    pool_pre_ping=True,
    pool_recycle=3600,
)

# Create session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=True,
    autocommit=False,
)


async def get_async_session() -> AsyncSession:
    """Get async database session."""
    async with Async<PERSON>essionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()


async def init_database():
    """Initialize database with tables and WAL mode."""
    try:
        # Ensure database directory exists
        db_path = Path(settings.database_path)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Initializing database at: {settings.database_path}")
        
        async with engine.begin() as conn:
            # Enable WAL mode for better concurrency
            await conn.execute(text("PRAGMA journal_mode=WAL"))
            await conn.execute(text("PRAGMA synchronous=NORMAL"))
            await conn.execute(text("PRAGMA cache_size=10000"))
            await conn.execute(text("PRAGMA temp_store=memory"))
            await conn.execute(text("PRAGMA mmap_size=268435456"))  # 256MB
            
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            
        logger.info("Database initialized successfully")
        
        # Verify database connection
        await verify_database_connection()
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def verify_database_connection():
    """Verify database connection and basic functionality."""
    try:
        async with AsyncSessionLocal() as session:
            # Test basic query
            result = await session.execute(text("SELECT 1"))
            assert result.scalar() == 1
            
            # Check if tables exist
            tables_query = text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            result = await session.execute(tables_query)
            tables = [row[0] for row in result.fetchall()]
            
            expected_tables = ['tasks', 'events', 'embeddings']
            for table in expected_tables:
                if table not in tables:
                    logger.warning(f"Table '{table}' not found in database")
                else:
                    logger.debug(f"Table '{table}' verified")
            
            logger.info("Database connection verified successfully")
            
    except Exception as e:
        logger.error(f"Database connection verification failed: {e}")
        raise


async def create_sample_data():
    """Create sample data for development and testing."""
    if not settings.debug:
        return
        
    try:
        from app.models.database import TaskModel, EventModel
        from datetime import datetime, timedelta
        
        async with AsyncSessionLocal() as session:
            # Check if sample data already exists
            existing_tasks = await session.execute(text("SELECT COUNT(*) FROM tasks"))
            if existing_tasks.scalar() > 0:
                logger.info("Sample data already exists, skipping creation")
                return
            
            # Create sample tasks
            sample_tasks = [
                TaskModel(
                    title="Complete project documentation",
                    description="Write comprehensive documentation for the AI dashboard project",
                    priority="high",
                    category="work",
                    ai_generated_category="productivity",
                    completed=False,
                    due_date=datetime.now() + timedelta(days=3),
                ),
                TaskModel(
                    title="Review code changes",
                    description="Review and approve pending pull requests",
                    priority="medium",
                    category="work",
                    ai_generated_category="development",
                    completed=False,
                    due_date=datetime.now() + timedelta(days=1),
                ),
                TaskModel(
                    title="Buy groceries",
                    description="Weekly grocery shopping",
                    priority="low",
                    category="personal",
                    ai_generated_category="errands",
                    completed=True,
                    due_date=datetime.now() - timedelta(days=1),
                ),
            ]
            
            # Create sample events
            sample_events = [
                EventModel(
                    title="Team standup meeting",
                    description="Daily team synchronization meeting",
                    start_time=datetime.now() + timedelta(hours=2),
                    end_time=datetime.now() + timedelta(hours=2, minutes=30),
                    location="Conference Room A",
                    ai_generated_category="meeting",
                ),
                EventModel(
                    title="Doctor appointment",
                    description="Annual health checkup",
                    start_time=datetime.now() + timedelta(days=5, hours=10),
                    end_time=datetime.now() + timedelta(days=5, hours=11),
                    location="Medical Center",
                    ai_generated_category="health",
                ),
                EventModel(
                    title="Project deadline",
                    description="Final submission for Q1 project",
                    start_time=datetime.now() + timedelta(days=7),
                    end_time=datetime.now() + timedelta(days=7, hours=1),
                    ai_generated_category="deadline",
                ),
            ]
            
            # Add to session
            for task in sample_tasks:
                session.add(task)
            for event in sample_events:
                session.add(event)
            
            await session.commit()
            logger.info("Sample data created successfully")
            
    except Exception as e:
        logger.error(f"Failed to create sample data: {e}")


async def cleanup_database():
    """Cleanup database connections."""
    try:
        await engine.dispose()
        logger.info("Database connections cleaned up")
    except Exception as e:
        logger.error(f"Error cleaning up database: {e}")


async def get_database_stats():
    """Get database statistics for monitoring."""
    try:
        async with AsyncSessionLocal() as session:
            stats = {}
            
            # Get table counts
            for table in ['tasks', 'events', 'embeddings']:
                try:
                    result = await session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    stats[f"{table}_count"] = result.scalar()
                except Exception:
                    stats[f"{table}_count"] = 0
            
            # Get database size
            result = await session.execute(text("PRAGMA page_count"))
            page_count = result.scalar()
            result = await session.execute(text("PRAGMA page_size"))
            page_size = result.scalar()
            stats["database_size_bytes"] = page_count * page_size
            
            # Get WAL mode status
            result = await session.execute(text("PRAGMA journal_mode"))
            stats["journal_mode"] = result.scalar()
            
            return stats
            
    except Exception as e:
        logger.error(f"Failed to get database stats: {e}")
        return {}


# Database health check
async def health_check():
    """Perform database health check."""
    try:
        async with AsyncSessionLocal() as session:
            await session.execute(text("SELECT 1"))
            return {"status": "healthy", "database": "connected"}
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {"status": "unhealthy", "database": "disconnected", "error": str(e)}


# Export commonly used items
__all__ = [
    "engine",
    "AsyncSessionLocal", 
    "get_async_session",
    "init_database",
    "verify_database_connection",
    "create_sample_data",
    "cleanup_database",
    "get_database_stats",
    "health_check",
]
