"""Database migrations and initialization."""

import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.connection import async_engine, Base

logger = logging.getLogger(__name__)


async def create_indexes():
    """Create additional indexes for performance."""
    indexes = [
        # Task indexes
        "CREATE INDEX IF NOT EXISTS idx_task_category ON tasks(ai_generated_category)",
        "CREATE INDEX IF NOT EXISTS idx_task_completed ON tasks(completed)",
        "CREATE INDEX IF NOT EXISTS idx_task_due_date ON tasks(due_date)",
        "CREATE INDEX IF NOT EXISTS idx_task_created_at ON tasks(created_at)",
        
        # Event indexes
        "CREATE INDEX IF NOT EXISTS idx_event_start_time ON events(start_time)",
        "CREATE INDEX IF NOT EXISTS idx_event_end_time ON events(end_time)",
        "CREATE INDEX IF NOT EXISTS idx_event_created_at ON events(created_at)",
        
        # User input indexes
        "CREATE INDEX IF NOT EXISTS idx_input_category ON user_inputs(category)",
        "CREATE INDEX IF NOT EXISTS idx_input_created_at ON user_inputs(created_at)",
        
        # Embedding indexes
        "CREATE INDEX IF NOT EXISTS idx_embedding_content_type ON embeddings(content_type)",
        "CREATE INDEX IF NOT EXISTS idx_embedding_content_id ON embeddings(content_id)",
        "CREATE INDEX IF NOT EXISTS idx_embedding_model ON embeddings(model_name)",
        "CREATE INDEX IF NOT EXISTS idx_embedding_composite ON embeddings(content_type, content_id)",
    ]
    
    async with async_engine.begin() as conn:
        for index_sql in indexes:
            await conn.execute(text(index_sql))
    
    logger.info("Database indexes created")


async def run_migrations():
    """Run database migrations."""
    logger.info("Running database migrations...")
    
    # Create all tables
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create additional indexes
    await create_indexes()
    
    logger.info("Database migrations completed")


async def seed_initial_data():
    """Seed database with initial data for development."""
    from app.models.database import TaskModel, EventModel
    from app.database.connection import AsyncSessionLocal
    from datetime import datetime, timedelta
    
    async with AsyncSessionLocal() as session:
        # Check if we already have data
        result = await session.execute(text("SELECT COUNT(*) FROM tasks"))
        task_count = result.scalar()
        
        if task_count > 0:
            logger.info("Database already has data, skipping seed")
            return
        
        # Create sample tasks
        sample_tasks = [
            TaskModel(
                title="Complete Biology Homework",
                description="Finish chapter 5 exercises on photosynthesis",
                ai_generated_category="Education",
                priority="high",
                due_date=datetime.now() + timedelta(days=2)
            ),
            TaskModel(
                title="Buy groceries",
                description="Milk, bread, eggs, vegetables",
                ai_generated_category="Personal",
                priority="medium",
                due_date=datetime.now() + timedelta(days=1)
            ),
            TaskModel(
                title="Call dentist",
                description="Schedule annual checkup appointment",
                ai_generated_category="Health",
                priority="low"
            )
        ]
        
        # Create sample events
        sample_events = [
            EventModel(
                title="Math Exam",
                description="Calculus final exam",
                start_time=datetime.now() + timedelta(days=7, hours=9),
                end_time=datetime.now() + timedelta(days=7, hours=11),
                location="Room 101"
            ),
            EventModel(
                title="Team Meeting",
                description="Weekly project sync",
                start_time=datetime.now() + timedelta(days=3, hours=14),
                end_time=datetime.now() + timedelta(days=3, hours=15),
                location="Conference Room A"
            )
        ]
        
        # Add to session
        for task in sample_tasks:
            session.add(task)
        
        for event in sample_events:
            session.add(event)
        
        await session.commit()
        logger.info("Sample data seeded successfully")
