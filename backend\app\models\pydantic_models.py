"""Core data structures using Pydantic models."""

from pydantic import BaseModel, Field
from typing import List, Optional, Union, Literal
from datetime import datetime
from enum import Enum


class InputCategory(str, Enum):
    """AI-determined input categories."""
    TASK = "task"
    EVENT = "event" 
    AI_QUESTION = "ai_question"


class TaskPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class QuestionType(str, Enum):
    """AI-determined question sub-categories."""
    SIMPLE_KNOWLEDGE = "simple_knowledge"
    DATABASE_SEARCH = "database_search"
    WEB_SEARCH = "web_search"


class UserInput(BaseModel):
    """Raw user input from hero bar."""
    text: str = Field(..., min_length=1, description="User's input text")
    timestamp: datetime = Field(default_factory=datetime.now)


class CategoryDecision(BaseModel):
    """AI categorization result."""
    category: InputCategory = Field(..., description="Determined category")
    confidence: float = Field(..., ge=0.0, le=1.0, description="AI confidence score")
    reasoning: str = Field(..., description="Why this category was chosen")
    processing_steps: List[str] = Field(default=[], description="Visual feedback steps")


class Task(BaseModel):
    """Task model."""
    id: Optional[str] = None
    title: str = Field(..., min_length=1)
    description: Optional[str] = None
    category: Optional[str] = None  # AI-generated category
    priority: TaskPriority = TaskPriority.MEDIUM
    due_date: Optional[datetime] = None
    completed: bool = False
    created_at: datetime = Field(default_factory=datetime.now)
    ai_generated_category: Optional[str] = None


class Event(BaseModel):
    """Calendar event model."""
    id: Optional[str] = None
    title: str = Field(..., min_length=1)
    description: Optional[str] = None
    start_time: datetime
    end_time: Optional[datetime] = None
    location: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)


class AIQuestion(BaseModel):
    """AI question processing."""
    text: str = Field(..., min_length=1)
    question_type: QuestionType
    reasoning: str = Field(..., description="Why this question type")
    

class SearchResult(BaseModel):
    """Search result from web or database."""
    title: str
    content: str
    source: str
    url: Optional[str] = None
    relevance_score: float = Field(..., ge=0.0, le=1.0)


class AIResponse(BaseModel):
    """Final AI response to user."""
    original_input: str
    category: InputCategory
    processing_steps: List[str]  # For visual feedback
    result: Union[Task, Event, str]  # Task/Event object or answer string
    search_results: Optional[List[SearchResult]] = None
    sources: Optional[List[str]] = None
    confidence: float = Field(..., ge=0.0, le=1.0)


# Animation state models
class AnimationStep(BaseModel):
    """Individual animation step."""
    id: str
    message: str
    animation_type: str  # "pulse", "spin", "typewriter", etc.
    duration: float = 1.0
    completed: bool = False


class ProcessingState(BaseModel):
    """Overall processing state for animations."""
    current_step: int = 0
    steps: List[AnimationStep] = []
    is_processing: bool = False
    error: Optional[str] = None
