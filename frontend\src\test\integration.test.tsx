import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/test/utils';
import { BrowserRouter } from 'react-router-dom';
import App from '@/App';
import { mockApiService, mockWebSocketService } from '@/test/utils';

// Mock API service
vi.mock('@/services/api', () => ({
  default: mockApiService,
  apiService: mockApiService,
}));

// Mock WebSocket service
vi.mock('@/services/websocket', () => ({
  default: mockWebSocketService,
  webSocketService: mockWebSocketService,
}));

describe('Dashboard Integration Tests', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Setup default mock responses
    mockApiService.healthCheck.mockResolvedValue({
      status: 'healthy',
      service: 'ai-dashboard-backend',
      version: '1.0.0',
    });
    
    mockApiService.getTasks.mockResolvedValue({
      tasks: [],
      total: 0,
    });
    
    mockApiService.getEvents.mockResolvedValue({
      events: [],
      total: 0,
    });
    
    mockApiService.getSettings.mockResolvedValue({
      default_model: 'test-model',
      enable_animations: true,
      max_search_results: 10,
    });
    
    mockWebSocketService.connect.mockResolvedValue(undefined);
    mockWebSocketService.getConnectionState.mockReturnValue('connected');
    mockWebSocketService.isWebSocketConnected.mockReturnValue(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Application Startup', () => {
    it('renders the main dashboard', async () => {
      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      // Check for main navigation elements
      expect(screen.getByText('AI Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Tasks')).toBeInTheDocument();
      expect(screen.getByText('Calendar')).toBeInTheDocument();
      expect(screen.getByText('Settings')).toBeInTheDocument();
    });

    it('establishes WebSocket connection on startup', async () => {
      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockWebSocketService.connect).toHaveBeenCalled();
      });
    });

    it('loads initial data on startup', async () => {
      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockApiService.healthCheck).toHaveBeenCalled();
      });
    });
  });

  describe('Hero Input Bar Integration', () => {
    it('processes task input end-to-end', async () => {
      const mockTask = {
        id: '1',
        title: 'Write documentation',
        description: 'Complete project documentation',
        priority: 'high',
        category: 'work',
        completed: false,
        created_at: '2024-01-01T00:00:00Z',
      };

      mockApiService.processInput.mockResolvedValue(
        new ReadableStream({
          start(controller) {
            controller.enqueue(new TextEncoder().encode(JSON.stringify({
              type: 'processing_start'
            })));
            controller.enqueue(new TextEncoder().encode(JSON.stringify({
              type: 'processing_step',
              step: 'Analyzing input...'
            })));
            controller.enqueue(new TextEncoder().encode(JSON.stringify({
              type: 'processing_complete',
              category: 'task',
              result: mockTask
            })));
            controller.close();
          }
        })
      );

      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      // Find and interact with the hero input
      const input = screen.getByPlaceholderText(/what would you like to do/i);
      const submitButton = screen.getByRole('button', { name: /submit/i });

      fireEvent.change(input, { target: { value: 'Write documentation for the project' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockApiService.processInput).toHaveBeenCalledWith({
          text: 'Write documentation for the project',
          timestamp: expect.any(String),
        });
      });
    });

    it('handles processing errors gracefully', async () => {
      mockApiService.processInput.mockRejectedValue(new Error('Processing failed'));

      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      const input = screen.getByPlaceholderText(/what would you like to do/i);
      const submitButton = screen.getByRole('button', { name: /submit/i });

      fireEvent.change(input, { target: { value: 'Test input' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/processing failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('Task Management Integration', () => {
    it('creates and displays tasks', async () => {
      const mockTask = {
        id: '1',
        title: 'Test Task',
        description: 'Test Description',
        priority: 'medium',
        category: 'work',
        completed: false,
        created_at: '2024-01-01T00:00:00Z',
      };

      mockApiService.createTask.mockResolvedValue(mockTask);
      mockApiService.getTasks.mockResolvedValue({
        tasks: [mockTask],
        total: 1,
      });

      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      // Navigate to tasks page
      fireEvent.click(screen.getByText('Tasks'));

      await waitFor(() => {
        expect(mockApiService.getTasks).toHaveBeenCalled();
      });

      // Create new task
      const addButton = screen.getByText('Add Task');
      fireEvent.click(addButton);

      // Fill out task form
      const titleInput = screen.getByPlaceholderText(/enter task title/i);
      const createButton = screen.getByText('Create Task');

      fireEvent.change(titleInput, { target: { value: 'Test Task' } });
      fireEvent.click(createButton);

      await waitFor(() => {
        expect(mockApiService.createTask).toHaveBeenCalledWith({
          title: 'Test Task',
          description: undefined,
          priority: 'medium',
          due_date: undefined,
          completed: false,
        });
      });
    });

    it('updates task completion status', async () => {
      const mockTask = {
        id: '1',
        title: 'Test Task',
        description: 'Test Description',
        priority: 'medium',
        category: 'work',
        completed: false,
        created_at: '2024-01-01T00:00:00Z',
      };

      const updatedTask = { ...mockTask, completed: true };

      mockApiService.getTasks.mockResolvedValue({
        tasks: [mockTask],
        total: 1,
      });
      mockApiService.updateTask.mockResolvedValue(updatedTask);

      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      // Navigate to tasks page
      fireEvent.click(screen.getByText('Tasks'));

      await waitFor(() => {
        expect(screen.getByText('Test Task')).toBeInTheDocument();
      });

      // Click completion checkbox
      const checkbox = screen.getByRole('button'); // Assuming the checkbox is a button
      fireEvent.click(checkbox);

      await waitFor(() => {
        expect(mockApiService.updateTask).toHaveBeenCalledWith('1', {
          completed: true,
        });
      });
    });
  });

  describe('Calendar Integration', () => {
    it('creates and displays events', async () => {
      const mockEvent = {
        id: '1',
        title: 'Test Event',
        description: 'Test Description',
        start_time: '2024-01-01T10:00:00Z',
        end_time: '2024-01-01T11:00:00Z',
        location: 'Test Location',
        created_at: '2024-01-01T00:00:00Z',
      };

      mockApiService.createEvent.mockResolvedValue(mockEvent);
      mockApiService.getEvents.mockResolvedValue({
        events: [mockEvent],
        total: 1,
      });

      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      // Navigate to calendar page
      fireEvent.click(screen.getByText('Calendar'));

      await waitFor(() => {
        expect(mockApiService.getEvents).toHaveBeenCalled();
      });

      // Create new event
      const addButton = screen.getByText('Add Event');
      fireEvent.click(addButton);

      // Fill out event form
      const titleInput = screen.getByPlaceholderText(/enter event title/i);
      const createButton = screen.getByText('Create Event');

      fireEvent.change(titleInput, { target: { value: 'Test Event' } });
      fireEvent.click(createButton);

      await waitFor(() => {
        expect(mockApiService.createEvent).toHaveBeenCalled();
      });
    });
  });

  describe('Settings Integration', () => {
    it('loads and updates settings', async () => {
      const mockSettings = {
        default_model: 'test-model',
        enable_animations: true,
        max_search_results: 10,
      };

      const updatedSettings = {
        ...mockSettings,
        enable_animations: false,
      };

      mockApiService.getSettings.mockResolvedValue(mockSettings);
      mockApiService.updateSettings.mockResolvedValue(updatedSettings);

      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      // Navigate to settings page
      fireEvent.click(screen.getByText('Settings'));

      await waitFor(() => {
        expect(mockApiService.getSettings).toHaveBeenCalled();
      });

      // Update a setting
      const animationToggle = screen.getByLabelText(/enable animations/i);
      fireEvent.click(animationToggle);

      // Save settings
      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockApiService.updateSettings).toHaveBeenCalled();
      });
    });
  });

  describe('Error Handling', () => {
    it('displays error messages for API failures', async () => {
      mockApiService.getTasks.mockRejectedValue(new Error('API Error'));

      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      // Navigate to tasks page
      fireEvent.click(screen.getByText('Tasks'));

      await waitFor(() => {
        expect(screen.getByText(/api error/i)).toBeInTheDocument();
      });
    });

    it('handles WebSocket disconnection', async () => {
      mockWebSocketService.getConnectionState.mockReturnValue('disconnected');
      mockWebSocketService.isWebSocketConnected.mockReturnValue(false);

      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(screen.getByText(/disconnected/i)).toBeInTheDocument();
      });
    });
  });

  describe('Responsive Design', () => {
    it('adapts to mobile viewport', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <BrowserRouter>
          <App />
        </BrowserRouter>
      );

      // Check for mobile-specific elements
      expect(screen.getByRole('button', { name: /menu/i })).toBeInTheDocument();
    });
  });
});
