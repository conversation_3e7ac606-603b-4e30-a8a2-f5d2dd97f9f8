Metadata-Version: 2.4
Name: mirascope
Version: 1.25.4
Summary: LLM abstractions that aren't obstructions
Project-URL: Homepage, https://mirascope.com
Project-URL: Documentation, https://mirascope.com/WELCOME
Project-URL: Repository, https://github.com/Mirascope/mirascope
Project-URL: Issues, https://github.com/Mirascope/mirascope/issues
Project-URL: Changelog, https://github.com/Mirascope/mirascope/releases
Author-email: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>
Maintainer-email: <PERSON> <<EMAIL>>
License: MIT License
        
        Copyright (c) 2023 Mirascope, Inc.
        
        Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all
        copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.
License-File: LICENSE
Keywords: agents,anthropic,artificial intelligence,bedrock,cohere,developer tools,gemini,groq,llm,llm tools,mistral,openai,prompt engineering,pydantic,vertex
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: Pydantic :: 2
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: File Formats :: JSON
Classifier: Topic :: File Formats :: JSON :: JSON Schema
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Software Development :: Libraries
Requires-Python: >=3.10
Requires-Dist: docstring-parser<1.0,>=0.15
Requires-Dist: jiter>=0.5.0
Requires-Dist: pydantic<3.0,>=2.7.4
Requires-Dist: typing-extensions>=4.10.0
Provides-Extra: anthropic
Requires-Dist: anthropic<1.0,>=0.29.0; extra == 'anthropic'
Provides-Extra: azure
Requires-Dist: aiohttp<4.0,>=3.10.5; extra == 'azure'
Requires-Dist: azure-ai-inference<2.0,>=1.0.0b4; extra == 'azure'
Provides-Extra: bedrock
Requires-Dist: aioboto3<14,>=13.1.1; extra == 'bedrock'
Requires-Dist: boto3-stubs[bedrock-runtime]<2,>=1.35.32; extra == 'bedrock'
Requires-Dist: boto3<2,>=1.34.70; extra == 'bedrock'
Requires-Dist: types-aioboto3[bedrock-runtime]<14,>=13.1.1; extra == 'bedrock'
Provides-Extra: cohere
Requires-Dist: cohere<6,>=5.5.8; extra == 'cohere'
Provides-Extra: gemini
Requires-Dist: google-generativeai<1,>=0.4.0; extra == 'gemini'
Requires-Dist: pillow<11,>=10.4.0; extra == 'gemini'
Provides-Extra: google
Requires-Dist: google-genai<2,>=1.2.0; extra == 'google'
Requires-Dist: pillow<11,>=10.4.0; extra == 'google'
Requires-Dist: proto-plus>=1.24.0; extra == 'google'
Provides-Extra: groq
Requires-Dist: groq<1,>=0.9.0; extra == 'groq'
Provides-Extra: hyperdx
Requires-Dist: hyperdx-opentelemetry<1,>=0.1.0; extra == 'hyperdx'
Provides-Extra: langfuse
Requires-Dist: langfuse<3,>=2.30.0; extra == 'langfuse'
Provides-Extra: litellm
Requires-Dist: litellm<2,>=1.42.12; extra == 'litellm'
Provides-Extra: logfire
Requires-Dist: logfire<4,>=1.0.0; extra == 'logfire'
Provides-Extra: mcp
Requires-Dist: mcp>=1.3.0; extra == 'mcp'
Provides-Extra: mistral
Requires-Dist: mistralai<2,>=1.0.0; extra == 'mistral'
Provides-Extra: openai
Requires-Dist: openai<2,>=1.6.0; extra == 'openai'
Provides-Extra: opentelemetry
Requires-Dist: opentelemetry-api<2,>=1.22.0; extra == 'opentelemetry'
Requires-Dist: opentelemetry-sdk<2,>=1.22.0; extra == 'opentelemetry'
Provides-Extra: realtime
Requires-Dist: numpy<2,>=1.26.4; extra == 'realtime'
Requires-Dist: pydub<1,>=0.25.1; extra == 'realtime'
Requires-Dist: sounddevice<1,>=0.5.1; extra == 'realtime'
Requires-Dist: websockets<14,>=13.1; extra == 'realtime'
Provides-Extra: tenacity
Requires-Dist: tenacity<9,>=8.4.2; extra == 'tenacity'
Provides-Extra: vertex
Requires-Dist: google-cloud-aiplatform<2,>=1.38.0; extra == 'vertex'
Provides-Extra: xai
Requires-Dist: openai<2,>=1.6.0; extra == 'xai'
Description-Content-Type: text/markdown

<p align="center">
    <a href="https://mirascope.com/#mirascope">
        <img src="https://github.com/user-attachments/assets/58b04850-8f30-40a6-be68-96ed2aa9b6d8" />
    </a>
</p>

<p align="center">
    <a href="https://github.com/Mirascope/mirascope/actions/workflows/tests.yml" target="_blank"><img src="https://github.com/Mirascope/mirascope/actions/workflows/tests.yml/badge.svg?branch=main" alt="Tests"/></a>
    <a href="https://codecov.io/github/Mirascope/mirascope" target="_blank"><img src="https://codecov.io/github/Mirascope/mirascope/graph/badge.svg?token=HAEAWT3KC9" alt="Coverage"/></a>
    <a href="https://mirascope.com/docs/mirascope" target="_blank"><img src="https://img.shields.io/badge/docs-available-brightgreen" alt="Docs"/></a>
    <a href="https://pypi.python.org/pypi/mirascope" target="_blank"><img src="https://img.shields.io/pypi/v/mirascope.svg" alt="PyPI Version"/></a>
    <a href="https://pypi.python.org/pypi/mirascope" target="_blank"><img src="https://img.shields.io/pypi/pyversions/mirascope.svg" alt="Stars"/></a>
    <a href="https://github.com/Mirascope/mirascope/tree/main/LICENSE"><img src="https://img.shields.io/github/license/Mirascope/mirascope.svg" alt="License"/></a>
    <a href="https://github.com/Mirascope/mirascope/stargazers" target="_blank"><img src="https://img.shields.io/github/stars/Mirascope/mirascope.svg" alt="Stars"/></a>
</p>

---

Mirascope is a powerful, flexible, and user-friendly library that simplifies the process of working with LLMs through a unified interface that works across various supported providers, including [OpenAI](https://openai.com/), [Anthropic](https://www.anthropic.com/), [Mistral](https://mistral.ai/), [Google (Gemini/Vertex)](https://googleapis.github.io/python-genai/), [Groq](https://groq.com/), [Cohere](https://cohere.com/), [LiteLLM](https://www.litellm.ai/), [Azure AI](https://azure.microsoft.com/en-us/solutions/ai), and [Bedrock](https://aws.amazon.com/bedrock/).

Whether you're generating text, extracting structured information, or developing complex AI-driven agent systems, Mirascope provides the tools you need to streamline your development process and create powerful, robust applications.

## 30 Second Quickstart

Install Mirascope, specifying the provider(s) you intend to use, and set your API key:

```bash
pip install "mirascope[openai]"
export OPENAI_API_KEY=XXXXX
```

Make your first call to an LLM to extract the title and author of a book from unstructured text:

```python
from mirascope import llm
from pydantic import BaseModel

class Book(BaseModel):
    title: str
    author: str

@llm.call(provider="openai", model="gpt-4o-mini", response_model=Book)
def extract_book(text: str) -> str:
    return f"Extract {text}"

book = extract_book("The Name of the Wind by Patrick Rothfuss")
assert isinstance(book, Book)
print(book)
# Output: title='The Name of the Wind' author='Patrick Rothfuss'
```

## Tutorials

Check out our [quickstart tutorial](https://mirascope.com/docs/mirascope/getting-started/quickstart) and many other tutorials for an interactive way to getting started with Mirascope.

## Usage

For a complete guide on how to use all of the various features Mirascope has to offer, read through our [Learn](https://mirascope.com/learn) documentation.

## Versioning

Mirascope uses [Semantic Versioning](https://semver.org/).

## Licence

This project is licensed under the terms of the [MIT License](https://github.com/Mirascope/mirascope/tree/main/LICENSE).
