export { l as loadDiffConfig, b as loadSnapshotSerializers, c as setupCommonEnv, s as startCoverageInsideWorker, a as stopCoverageInsideWorker, t as takeCoverageInsideWorker } from './chunks/setup-common.Dd054P77.js';
export { collectTests, processError, startTests } from '@vitest/runner';
import * as spy from '@vitest/spy';
export { spy as SpyModule };
export { format, getSafeTimers, inspect, stringify } from '@vitest/utils';
export { TraceMap, originalPositionFor } from '@vitest/utils/source-map';
import './chunks/coverage.DVF1vEu8.js';
import '@vitest/snapshot';
