!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactHookForm={},e.React)}(this,function(e,t){"use strict";function r(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if("default"!==r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var s=r(t),a=e=>"checkbox"===e.type,i=e=>e instanceof Date,n=e=>null==e;const o=e=>"object"==typeof e;var l=e=>!n(e)&&!Array.isArray(e)&&o(e)&&!i(e),u=e=>l(e)&&e.target?a(e.target)?e.target.checked:e.target.value:e,d=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),c="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function f(e){let t;const r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else{if(c&&(e instanceof Blob||s)||!r&&!l(e))return e;if(t=r?[]:{},r||(e=>{const t=e.constructor&&e.constructor.prototype;return l(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=f(e[r]));else t=e}return t}var m=e=>/^\w*$/.test(e),y=e=>void 0===e,g=e=>Array.isArray(e)?e.filter(Boolean):[],b=e=>g(e.replace(/["|']|\]/g,"").split(/\.|\[/)),_=(e,t,r)=>{if(!t||!l(e))return r;const s=(m(t)?[t]:b(t)).reduce((e,t)=>n(e)?e:e[t],e);return y(s)||s===e?y(e[t])?r:e[t]:s},p=e=>"boolean"==typeof e,h=(e,t,r)=>{let s=-1;const a=m(t)?[t]:b(t),i=a.length,n=i-1;for(;++s<i;){const t=a[s];let i=r;if(s!==n){const r=e[t];i=l(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};const v="blur",V="focusout",F="change",A="onBlur",x="onChange",S="onSubmit",k="onTouched",w="all",D="max",C="min",E="maxLength",O="minLength",j="pattern",M="required",T="validate",U=t.createContext(null);U.displayName="HookFormContext";const N=()=>t.useContext(U);var R=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==w&&(t._proxyFormState[a]=!s||w),r&&(r[a]=!0),e[a]}});return a};const B="undefined"!=typeof window?s.useLayoutEffect:s.useEffect;function L(e){const r=N(),{control:s=r.control,disabled:a,name:i,exact:n}=e||{},[o,l]=t.useState(s._formState),u=t.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return B(()=>s._subscribe({name:i,formState:u.current,exact:n,callback:e=>{!a&&l({...s._formState,...e})}}),[i,a,n]),t.useEffect(()=>{u.current.isValid&&s._setValid(!0)},[s]),t.useMemo(()=>R(o,s,u.current,!1),[o,s])}var P=e=>"string"==typeof e,I=(e,t,r,s,a)=>P(e)?(s&&t.watch.add(e),_(r,e,a)):Array.isArray(e)?e.map(e=>(s&&t.watch.add(e),_(r,e))):(s&&(t.watchAll=!0),r);function W(e){const r=N(),{control:s=r.control,name:a,defaultValue:i,disabled:n,exact:o}=e||{},l=t.useRef(i),[u,d]=t.useState(s._getWatch(a,l.current));return B(()=>s._subscribe({name:a,formState:{values:!0},exact:o,callback:e=>!n&&d(I(a,s._names,e.values||s._formValues,!1,l.current))}),[a,s,n,o]),t.useEffect(()=>s._removeUnmounted()),u}function q(e){const r=N(),{name:s,disabled:a,control:i=r.control,shouldUnregister:n}=e,o=d(i._names.array,s),l=W({control:i,name:s,defaultValue:_(i._formValues,s,_(i._defaultValues,s,e.defaultValue)),exact:!0}),c=L({control:i,name:s,exact:!0}),m=t.useRef(e),g=t.useRef(i.register(s,{...e.rules,value:l,...p(e.disabled)?{disabled:e.disabled}:{}})),b=t.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!_(c.errors,s)},isDirty:{enumerable:!0,get:()=>!!_(c.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!_(c.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!_(c.validatingFields,s)},error:{enumerable:!0,get:()=>_(c.errors,s)}}),[c,s]),V=t.useCallback(e=>g.current.onChange({target:{value:u(e),name:s},type:F}),[s]),A=t.useCallback(()=>g.current.onBlur({target:{value:_(i._formValues,s),name:s},type:v}),[s,i._formValues]),x=t.useCallback(e=>{const t=_(i._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})},[i._fields,s]),S=t.useMemo(()=>({name:s,value:l,...p(a)||c.disabled?{disabled:c.disabled||a}:{},onChange:V,onBlur:A,ref:x}),[s,a,c.disabled,V,A,x,l]);return t.useEffect(()=>{const e=i._options.shouldUnregister||n;i.register(s,{...m.current.rules,...p(m.current.disabled)?{disabled:m.current.disabled}:{}});const t=(e,t)=>{const r=_(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=f(_(i._options.defaultValues,s));h(i._defaultValues,s,e),y(_(i._formValues,s))&&h(i._formValues,s,e)}return!o&&i.register(s),()=>{(o?e&&!i._state.action:e)?i.unregister(s):t(s,!1)}},[s,i,o,n]),t.useEffect(()=>{i._setDisabledField({disabled:a,name:s})},[a,s,i]),t.useMemo(()=>({field:S,formState:c,fieldState:b}),[S,c,b])}const $=e=>{const t={};for(const r of Object.keys(e))if(o(e[r])&&null!==e[r]){const s=$(e[r]);for(const e of Object.keys(s))t[`${r}.${e}`]=s[e]}else t[r]=e[r];return t},H="post";var z=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},J=e=>Array.isArray(e)?e:[e],G=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},K=e=>n(e)||!o(e);function Q(e,t,r=new WeakSet){if(K(e)||K(t))return e===t;if(i(e)&&i(t))return e.getTime()===t.getTime();const s=Object.keys(e),a=Object.keys(t);if(s.length!==a.length)return!1;if(r.has(e)||r.has(t))return!0;r.add(e),r.add(t);for(const n of s){const s=e[n];if(!a.includes(n))return!1;if("ref"!==n){const e=t[n];if(i(s)&&i(e)||l(s)&&l(e)||Array.isArray(s)&&Array.isArray(e)?!Q(s,e,r):s!==e)return!1}}return!0}var X=e=>l(e)&&!Object.keys(e).length,Y=e=>"file"===e.type,Z=e=>"function"==typeof e,ee=e=>{if(!c)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},te=e=>"select-multiple"===e.type,re=e=>"radio"===e.type,se=e=>ee(e)&&e.isConnected;function ae(e,t){const r=Array.isArray(t)?t:m(t)?[t]:b(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=y(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(l(s)&&X(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!y(e[t]))return!1;return!0}(s))&&ae(e,r.slice(0,-1)),e}var ie=e=>{for(const t in e)if(Z(e[t]))return!0;return!1};function ne(e,t={}){const r=Array.isArray(e);if(l(e)||r)for(const r in e)Array.isArray(e[r])||l(e[r])&&!ie(e[r])?(t[r]=Array.isArray(e[r])?[]:{},ne(e[r],t[r])):n(e[r])||(t[r]=!0);return t}function oe(e,t,r){const s=Array.isArray(e);if(l(e)||s)for(const s in e)Array.isArray(e[s])||l(e[s])&&!ie(e[s])?y(t)||K(r[s])?r[s]=Array.isArray(e[s])?ne(e[s],[]):{...ne(e[s])}:oe(e[s],n(t)?{}:t[s],r[s]):r[s]=!Q(e[s],t[s]);return r}var le=(e,t)=>oe(e,t,ne(t));const ue={value:!1,isValid:!1},de={value:!0,isValid:!0};var ce=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!y(e[0].attributes.value)?y(e[0].value)||""===e[0].value?de:{value:e[0].value,isValid:!0}:de:ue}return ue},fe=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>y(e)?e:t?""===e?NaN:e?+e:e:r&&P(e)?new Date(e):s?s(e):e;const me={isValid:!1,value:null};var ye=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,me):me;function ge(e){const t=e.ref;return Y(t)?t.files:re(t)?ye(e.refs).value:te(t)?[...t.selectedOptions].map(({value:e})=>e):a(t)?ce(e.refs).value:fe(y(t.value)?e.ref.value:t.value,e)}var be=e=>e instanceof RegExp,_e=e=>y(e)?e:be(e)?e.source:l(e)?be(e.value)?e.value.source:e.value:e,pe=e=>({isOnSubmit:!e||e===S,isOnBlur:e===A,isOnChange:e===x,isOnAll:e===w,isOnTouch:e===k});const he="AsyncFunction";var ve=e=>!!e&&!!e.validate&&!!(Z(e.validate)&&e.validate.constructor.name===he||l(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===he)),Ve=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));const Fe=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=_(e,a);if(r){const{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(Fe(i,t))break}else if(l(i)&&Fe(i,t))break}}};function Ae(e,t,r){const s=_(e,r);if(s||m(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=_(t,s),n=_(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};if(n&&n.root&&n.root.type)return{name:`${s}.root`,error:n.root};a.pop()}return{name:r}}var xe=(e,t,r)=>{const s=J(_(e,r));return h(s,"root",t[r]),h(e,r,s),e},Se=e=>P(e);function ke(e,t,r="validate"){if(Se(e)||Array.isArray(e)&&e.every(Se)||p(e)&&!e)return{type:r,message:Se(e)?e:"",ref:t}}var we=e=>l(e)&&!be(e)?e:{value:e,message:""},De=async(e,t,r,s,i,o)=>{const{ref:u,refs:d,required:c,maxLength:f,minLength:m,min:g,max:b,pattern:h,validate:v,name:V,valueAsNumber:F,mount:A}=e._f,x=_(r,V);if(!A||t.has(V))return{};const S=d?d[0]:u,k=e=>{i&&S.reportValidity&&(S.setCustomValidity(p(e)?"":e||""),S.reportValidity())},w={},U=re(u),N=a(u),R=U||N,B=(F||Y(u))&&y(u.value)&&y(x)||ee(u)&&""===u.value||""===x||Array.isArray(x)&&!x.length,L=z.bind(null,V,s,w),I=(e,t,r,s=E,a=O)=>{const i=e?t:r;w[V]={type:e?s:a,message:i,ref:u,...L(e?s:a,i)}};if(o?!Array.isArray(x)||!x.length:c&&(!R&&(B||n(x))||p(x)&&!x||N&&!ce(d).isValid||U&&!ye(d).isValid)){const{value:e,message:t}=Se(c)?{value:!!c,message:c}:we(c);if(e&&(w[V]={type:M,message:t,ref:S,...L(M,t)},!s))return k(t),w}if(!(B||n(g)&&n(b))){let e,t;const r=we(b),a=we(g);if(n(x)||isNaN(x)){const s=u.valueAsDate||new Date(x),i=e=>new Date((new Date).toDateString()+" "+e),n="time"==u.type,o="week"==u.type;P(r.value)&&x&&(e=n?i(x)>i(r.value):o?x>r.value:s>new Date(r.value)),P(a.value)&&x&&(t=n?i(x)<i(a.value):o?x<a.value:s<new Date(a.value))}else{const s=u.valueAsNumber||(x?+x:x);n(r.value)||(e=s>r.value),n(a.value)||(t=s<a.value)}if((e||t)&&(I(!!e,r.message,a.message,D,C),!s))return k(w[V].message),w}if((f||m)&&!B&&(P(x)||o&&Array.isArray(x))){const e=we(f),t=we(m),r=!n(e.value)&&x.length>+e.value,a=!n(t.value)&&x.length<+t.value;if((r||a)&&(I(r,e.message,t.message),!s))return k(w[V].message),w}if(h&&!B&&P(x)){const{value:e,message:t}=we(h);if(be(e)&&!x.match(e)&&(w[V]={type:j,message:t,ref:u,...L(j,t)},!s))return k(t),w}if(v)if(Z(v)){const e=ke(await v(x,r),S);if(e&&(w[V]={...e,...L(T,e.message)},!s))return k(e.message),w}else if(l(v)){let e={};for(const t in v){if(!X(e)&&!s)break;const a=ke(await v[t](x,r),S,t);a&&(e={...a,...L(t,a.message)},k(a.message),s&&(w[V]=e))}if(!X(e)&&(w[V]={ref:S,...e},!s))return w}return k(!0),w};const Ce={mode:S,reValidateMode:x,shouldFocusError:!0};function Ee(e={}){let t,r={...Ce,...e},s={submitCount:0,isDirty:!1,isReady:!1,isLoading:Z(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},o={},m=(l(r.defaultValues)||l(r.values))&&f(r.defaultValues||r.values)||{},b=r.shouldUnregister?{}:f(m),F={action:!1,mount:!1,watch:!1},A={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},x=0;const S={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let k={...S};const D={array:G(),state:G()},C=r.criteriaMode===w,E=async e=>{if(!r.disabled&&(S.isValid||k.isValid||e)){const e=r.resolver?X((await U()).errors):await N(o,!0);e!==s.isValid&&D.state.next({isValid:e})}},O=(e,t)=>{!r.disabled&&(S.isValidating||S.validatingFields||k.isValidating||k.validatingFields)&&((e||Array.from(A.mount)).forEach(e=>{e&&(t?h(s.validatingFields,e,t):ae(s.validatingFields,e))}),D.state.next({validatingFields:s.validatingFields,isValidating:!X(s.validatingFields)}))},j=(e,t,r,s)=>{const a=_(o,e);if(a){const i=_(b,e,y(r)?_(m,e):r);y(i)||s&&s.defaultChecked||t?h(b,e,t?i:ge(a._f)):L(e,i),F.mount&&E()}},M=(e,t,a,i,n)=>{let o=!1,l=!1;const u={name:e};if(!r.disabled){if(!a||i){(S.isDirty||k.isDirty)&&(l=s.isDirty,s.isDirty=u.isDirty=R(),o=l!==u.isDirty);const r=Q(_(m,e),t);l=!!_(s.dirtyFields,e),r?ae(s.dirtyFields,e):h(s.dirtyFields,e,!0),u.dirtyFields=s.dirtyFields,o=o||(S.dirtyFields||k.dirtyFields)&&l!==!r}if(a){const t=_(s.touchedFields,e);t||(h(s.touchedFields,e,a),u.touchedFields=s.touchedFields,o=o||(S.touchedFields||k.touchedFields)&&t!==a)}o&&n&&D.state.next(u)}return o?u:{}},T=(e,a,i,n)=>{const o=_(s.errors,e),l=(S.isValid||k.isValid)&&p(a)&&s.isValid!==a;var u;if(r.delayError&&i?(u=()=>((e,t)=>{h(s.errors,e,t),D.state.next({errors:s.errors})})(e,i),t=e=>{clearTimeout(x),x=setTimeout(u,e)},t(r.delayError)):(clearTimeout(x),t=null,i?h(s.errors,e,i):ae(s.errors,e)),(i?!Q(o,i):o)||!X(n)||l){const t={...n,...l&&p(a)?{isValid:a}:{},errors:s.errors,name:e};s={...s,...t},D.state.next(t)}},U=async e=>{O(e,!0);const t=await r.resolver(b,r.context,((e,t,r,s)=>{const a={};for(const r of e){const e=_(t,r);e&&h(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||A.mount,o,r.criteriaMode,r.shouldUseNativeValidation));return O(e),t},N=async(e,t,a={valid:!0})=>{for(const i in e){const n=e[i];if(n){const{_f:e,...o}=n;if(e){const o=A.array.has(e.name),l=n._f&&ve(n._f);l&&S.validatingFields&&O([i],!0);const u=await De(n,A.disabled,b,C,r.shouldUseNativeValidation&&!t,o);if(l&&S.validatingFields&&O([i]),u[e.name]&&(a.valid=!1,t))break;!t&&(_(u,e.name)?o?xe(s.errors,u,e.name):h(s.errors,e.name,u[e.name]):ae(s.errors,e.name))}!X(o)&&await N(o,t,a)}}return a.valid},R=(e,t)=>!r.disabled&&(e&&t&&h(b,e,t),!Q(K(),m)),B=(e,t,r)=>I(e,A,{...F.mount?b:y(t)?m:P(e)?{[e]:t}:t},r,t),L=(e,t,r={})=>{const s=_(o,e);let i=t;if(s){const r=s._f;r&&(!r.disabled&&h(b,e,fe(t,r)),i=ee(r.ref)&&n(t)?"":t,te(r.ref)?[...r.ref.options].forEach(e=>e.selected=i.includes(e.value)):r.refs?a(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(i)?e.checked=!!i.find(t=>t===e.value):e.checked=i===e.value||!!i)}):r.refs.forEach(e=>e.checked=e.value===i):Y(r.ref)?r.ref.value="":(r.ref.value=i,r.ref.type||D.state.next({name:e,values:f(b)})))}(r.shouldDirty||r.shouldTouch)&&M(e,i,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&z(e)},W=(e,t,r)=>{for(const s in t){if(!t.hasOwnProperty(s))return;const a=t[s],n=e+"."+s,u=_(o,n);(A.array.has(e)||l(a)||u&&!u._f)&&!i(a)?W(n,a,r):L(n,a,r)}},q=(e,t,r={})=>{const a=_(o,e),i=A.array.has(e),l=f(t);h(b,e,l),i?(D.array.next({name:e,values:f(b)}),(S.isDirty||S.dirtyFields||k.isDirty||k.dirtyFields)&&r.shouldDirty&&D.state.next({name:e,dirtyFields:le(m,b),isDirty:R(e,l)})):!a||a._f||n(l)?L(e,l,r):W(e,l,r),Ve(e,A)&&D.state.next({...s}),D.state.next({name:F.mount?e:void 0,values:f(b)})},$=async e=>{F.mount=!0;const a=e.target;let n=a.name,l=!0;const d=_(o,n),c=e=>{l=Number.isNaN(e)||i(e)&&isNaN(e.getTime())||Q(e,_(b,n,e))},m=pe(r.mode),y=pe(r.reValidateMode);if(d){let i,p;const F=a.type?ge(d._f):u(e),x=e.type===v||e.type===V,w=!((g=d._f).mount&&(g.required||g.min||g.max||g.maxLength||g.minLength||g.pattern||g.validate)||r.resolver||_(s.errors,n)||d._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(x,_(s.touchedFields,n),s.isSubmitted,y,m),j=Ve(n,A,x);h(b,n,F),x?(d._f.onBlur&&d._f.onBlur(e),t&&t(0)):d._f.onChange&&d._f.onChange(e);const R=M(n,F,x),B=!X(R)||j;if(!x&&D.state.next({name:n,type:e.type,values:f(b)}),w)return(S.isValid||k.isValid)&&("onBlur"===r.mode?x&&E():x||E()),B&&D.state.next({name:n,...j?{}:R});if(!x&&j&&D.state.next({...s}),r.resolver){const{errors:e}=await U([n]);if(c(F),l){const t=Ae(s.errors,o,n),r=Ae(e,o,t.name||n);i=r.error,n=r.name,p=X(e)}}else O([n],!0),i=(await De(d,A.disabled,b,C,r.shouldUseNativeValidation))[n],O([n]),c(F),l&&(i?p=!1:(S.isValid||k.isValid)&&(p=await N(o,!0)));l&&(d._f.deps&&z(d._f.deps),T(n,p,i,R))}var g},H=(e,t)=>{if(_(s.errors,t)&&e.focus)return e.focus(),1},z=async(e,t={})=>{let a,i;const n=J(e);if(r.resolver){const t=await(async e=>{const{errors:t}=await U(e);if(e)for(const r of e){const e=_(t,r);e?h(s.errors,r,e):ae(s.errors,r)}else s.errors=t;return t})(y(e)?e:n);a=X(t),i=e?!n.some(e=>_(t,e)):a}else e?(i=(await Promise.all(n.map(async e=>{const t=_(o,e);return await N(t&&t._f?{[e]:t}:t)}))).every(Boolean),(i||s.isValid)&&E()):i=a=await N(o);return D.state.next({...!P(e)||(S.isValid||k.isValid)&&a!==s.isValid?{}:{name:e},...r.resolver||!e?{isValid:a}:{},errors:s.errors}),t.shouldFocus&&!i&&Fe(o,H,e?n:A.mount),i},K=e=>{const t={...F.mount?b:m};return y(e)?t:P(e)?_(t,e):e.map(e=>_(t,e))},ie=(e,t)=>({invalid:!!_((t||s).errors,e),isDirty:!!_((t||s).dirtyFields,e),error:_((t||s).errors,e),isValidating:!!_(s.validatingFields,e),isTouched:!!_((t||s).touchedFields,e)}),ne=(e,t,r)=>{const a=(_(o,e,{_f:{}})._f||{}).ref,i=_(s.errors,e)||{},{ref:n,message:l,type:u,...d}=i;h(s.errors,e,{...d,...t,ref:a}),D.state.next({name:e,errors:s.errors,isValid:!1}),r&&r.shouldFocus&&a&&a.focus&&a.focus()},oe=e=>D.state.subscribe({next:t=>{var r,a,i;r=e.name,a=t.name,i=e.exact,r&&a&&r!==a&&!J(r).some(e=>e&&(i?e===a:e.startsWith(a)||a.startsWith(e)))||!((e,t,r,s)=>{r(e);const{name:a,...i}=e;return X(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!s||w))})(t,e.formState||S,Se,e.reRenderRoot)||e.callback({values:{...b},...s,...t})}}).unsubscribe,ue=(e,t={})=>{for(const a of e?J(e):A.mount)A.mount.delete(a),A.array.delete(a),t.keepValue||(ae(o,a),ae(b,a)),!t.keepError&&ae(s.errors,a),!t.keepDirty&&ae(s.dirtyFields,a),!t.keepTouched&&ae(s.touchedFields,a),!t.keepIsValidating&&ae(s.validatingFields,a),!r.shouldUnregister&&!t.keepDefaultValue&&ae(m,a);D.state.next({values:f(b)}),D.state.next({...s,...t.keepDirty?{isDirty:R()}:{}}),!t.keepIsValid&&E()},de=({disabled:e,name:t})=>{(p(e)&&F.mount||e||A.disabled.has(t))&&(e?A.disabled.add(t):A.disabled.delete(t))},ce=(e,t={})=>{let s=_(o,e);const i=p(t.disabled)||p(r.disabled);return h(o,e,{...s||{},_f:{...s&&s._f?s._f:{ref:{name:e}},name:e,mount:!0,...t}}),A.mount.add(e),s?de({disabled:p(t.disabled)?t.disabled:r.disabled,name:e}):j(e,!0,t.value),{...i?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:_e(t.min),max:_e(t.max),minLength:_e(t.minLength),maxLength:_e(t.maxLength),pattern:_e(t.pattern)}:{},name:e,onChange:$,onBlur:$,ref:i=>{if(i){ce(e,t),s=_(o,e);const r=y(i.value)&&i.querySelectorAll&&i.querySelectorAll("input,select,textarea")[0]||i,n=(e=>re(e)||a(e))(r),l=s._f.refs||[];if(n?l.find(e=>e===r):r===s._f.ref)return;h(o,e,{_f:{...s._f,...n?{refs:[...l.filter(se),r,...Array.isArray(_(m,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),j(e,!1,void 0,r)}else s=_(o,e,{}),s._f&&(s._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&(!d(A.array,e)||!F.action)&&A.unMount.add(e)}}},me=()=>r.shouldFocusError&&Fe(o,H,A.mount),ye=(e,t)=>async a=>{let i;a&&(a.preventDefault&&a.preventDefault(),a.persist&&a.persist());let n=f(b);if(D.state.next({isSubmitting:!0}),r.resolver){const{errors:e,values:t}=await U();s.errors=e,n=f(t)}else await N(o);if(A.disabled.size)for(const e of A.disabled)ae(n,e);if(ae(s.errors,"root"),X(s.errors)){D.state.next({errors:{}});try{await e(n,a)}catch(e){i=e}}else t&&await t({...s.errors},a),me(),setTimeout(me);if(D.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:X(s.errors)&&!i,submitCount:s.submitCount+1,errors:s.errors}),i)throw i},be=(e,t={})=>{const a=e?f(e):m,i=f(a),n=X(e),l=n?m:i;if(t.keepDefaultValues||(m=a),!t.keepValues){if(t.keepDirtyValues){const e=new Set([...A.mount,...Object.keys(le(m,b))]);for(const t of Array.from(e))_(s.dirtyFields,t)?h(l,t,_(b,t)):q(t,_(l,t))}else{if(c&&y(e))for(const e of A.mount){const t=_(o,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(ee(e)){const t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(const e of A.mount)q(e,_(l,e));else o={}}b=r.shouldUnregister?t.keepDefaultValues?f(m):{}:f(l),D.array.next({values:{...l}}),D.state.next({values:{...l}})}A={mount:t.keepDirtyValues?A.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},F.mount=!S.isValid||!!t.keepIsValid||!!t.keepDirtyValues,F.watch=!!r.shouldUnregister,D.state.next({submitCount:t.keepSubmitCount?s.submitCount:0,isDirty:!n&&(t.keepDirty?s.isDirty:!(!t.keepDefaultValues||Q(e,m))),isSubmitted:!!t.keepIsSubmitted&&s.isSubmitted,dirtyFields:n?{}:t.keepDirtyValues?t.keepDefaultValues&&b?le(m,b):s.dirtyFields:t.keepDefaultValues&&e?le(m,e):t.keepDirty?s.dirtyFields:{},touchedFields:t.keepTouched?s.touchedFields:{},errors:t.keepErrors?s.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&s.isSubmitSuccessful,isSubmitting:!1})},he=(e,t)=>be(Z(e)?e(b):e,t),Se=e=>{s={...s,...e}},ke={control:{register:ce,unregister:ue,getFieldState:ie,handleSubmit:ye,setError:ne,_subscribe:oe,_runSchema:U,_focusError:me,_getWatch:B,_getDirty:R,_setValid:E,_setFieldArray:(e,t=[],a,i,n=!0,l=!0)=>{if(i&&a&&!r.disabled){if(F.action=!0,l&&Array.isArray(_(o,e))){const t=a(_(o,e),i.argA,i.argB);n&&h(o,e,t)}if(l&&Array.isArray(_(s.errors,e))){const t=a(_(s.errors,e),i.argA,i.argB);n&&h(s.errors,e,t),((e,t)=>{!g(_(e,t)).length&&ae(e,t)})(s.errors,e)}if((S.touchedFields||k.touchedFields)&&l&&Array.isArray(_(s.touchedFields,e))){const t=a(_(s.touchedFields,e),i.argA,i.argB);n&&h(s.touchedFields,e,t)}(S.dirtyFields||k.dirtyFields)&&(s.dirtyFields=le(m,b)),D.state.next({name:e,isDirty:R(e,t),dirtyFields:s.dirtyFields,errors:s.errors,isValid:s.isValid})}else h(b,e,t)},_setDisabledField:de,_setErrors:e=>{s.errors=e,D.state.next({errors:s.errors,isValid:!1})},_getFieldArray:e=>g(_(F.mount?b:m,e,r.shouldUnregister?_(m,e,[]):[])),_reset:be,_resetDefaultValues:()=>Z(r.defaultValues)&&r.defaultValues().then(e=>{he(e,r.resetOptions),D.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(const e of A.unMount){const t=_(o,e);t&&(t._f.refs?t._f.refs.every(e=>!se(e)):!se(t._f.ref))&&ue(e)}A.unMount=new Set},_disableForm:e=>{p(e)&&(D.state.next({disabled:e}),Fe(o,(t,r)=>{const s=_(o,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach(t=>{t.disabled=s._f.disabled||e}))},0,!1))},_subjects:D,_proxyFormState:S,get _fields(){return o},get _formValues(){return b},get _state(){return F},set _state(e){F=e},get _defaultValues(){return m},get _names(){return A},set _names(e){A=e},get _formState(){return s},get _options(){return r},set _options(e){r={...r,...e}}},subscribe:e=>(F.mount=!0,k={...k,...e.formState},oe({...e,formState:k})),trigger:z,register:ce,handleSubmit:ye,watch:(e,t)=>Z(e)?D.state.subscribe({next:r=>e(B(void 0,t),r)}):B(e,t,!0),setValue:q,getValues:K,reset:he,resetField:(e,t={})=>{_(o,e)&&(y(t.defaultValue)?q(e,f(_(m,e))):(q(e,t.defaultValue),h(m,e,f(t.defaultValue))),t.keepTouched||ae(s.touchedFields,e),t.keepDirty||(ae(s.dirtyFields,e),s.isDirty=t.defaultValue?R(e,f(_(m,e))):R()),t.keepError||(ae(s.errors,e),S.isValid&&E()),D.state.next({...s}))},clearErrors:e=>{e&&J(e).forEach(e=>ae(s.errors,e)),D.state.next({errors:e?s.errors:{}})},unregister:ue,setError:ne,setFocus:(e,t={})=>{const r=_(o,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&Z(e.select)&&e.select())}},getFieldState:ie};return{...ke,formControl:ke}}var Oe=()=>{if("undefined"!=typeof crypto&&crypto.randomUUID)return crypto.randomUUID();const e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)})},je=(e,t,r={})=>r.shouldFocus||y(r.shouldFocus)?r.focusName||`${e}.${y(r.focusIndex)?t:r.focusIndex}.`:"",Me=(e,t)=>[...e,...J(t)],Te=e=>Array.isArray(e)?e.map(()=>{}):void 0;function Ue(e,t,r){return[...e.slice(0,t),...J(r),...e.slice(t)]}var Ne=(e,t,r)=>Array.isArray(e)?(y(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],Re=(e,t)=>[...J(t),...J(e)];var Be=(e,t)=>y(t)?[]:function(e,t){let r=0;const s=[...e];for(const e of t)s.splice(e-r,1),r++;return g(s).length?s:[]}(e,J(t).sort((e,t)=>e-t)),Le=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]},Pe=(e,t,r)=>(e[t]=r,e);e.Controller=e=>e.render(q(e)),e.Form=function(e){const r=N(),[s,a]=t.useState(!1),{control:i=r.control,onSubmit:n,children:o,action:l,method:u=H,headers:d,encType:c,onError:f,render:m,onSuccess:y,validateStatus:g,...b}=e,_=async t=>{let r=!1,s="";await i.handleSubmit(async e=>{const a=new FormData;let o="";try{o=JSON.stringify(e)}catch(e){}const m=$(i._formValues);for(const e in m)a.append(e,m[e]);if(n&&await n({data:e,event:t,method:u,formData:a,formDataJson:o}),l)try{const e=[d&&d["Content-Type"],c].some(e=>e&&e.includes("json")),t=await fetch(String(l),{method:u,headers:{...d,...c?{"Content-Type":c}:{}},body:e?o:a});t&&(g?!g(t.status):t.status<200||t.status>=300)?(r=!0,f&&f({response:t}),s=String(t.status)):y&&y({response:t})}catch(e){r=!0,f&&f({error:e})}})(t),r&&e.control&&(e.control._subjects.state.next({isSubmitSuccessful:!1}),e.control.setError("root.server",{type:s}))};return t.useEffect(()=>{a(!0)},[]),m?t.createElement(t.Fragment,null,m({submit:_})):t.createElement("form",{noValidate:s,action:l,method:u,encType:c,onSubmit:_,...b},o)},e.FormProvider=e=>{const{children:r,...s}=e;return t.createElement(U.Provider,{value:s},r)},e.appendErrors=z,e.createFormControl=Ee,e.get=_,e.set=h,e.useController=q,e.useFieldArray=function(e){const r=N(),{control:s=r.control,name:a,keyName:i="id",shouldUnregister:n,rules:o}=e,[l,u]=t.useState(s._getFieldArray(a)),d=t.useRef(s._getFieldArray(a).map(Oe)),c=t.useRef(l),m=t.useRef(a),y=t.useRef(!1);m.current=a,c.current=l,s._names.array.add(a),o&&s.register(a,o),B(()=>s._subjects.array.subscribe({next:({values:e,name:t})=>{if(t===m.current||!t){const t=_(e,m.current);Array.isArray(t)&&(u(t),d.current=t.map(Oe))}}}).unsubscribe,[s]);const g=t.useCallback(e=>{y.current=!0,s._setFieldArray(a,e)},[s,a]);return t.useEffect(()=>{if(s._state.action=!1,Ve(a,s._names)&&s._subjects.state.next({...s._formState}),y.current&&(!pe(s._options.mode).isOnSubmit||s._formState.isSubmitted)&&!pe(s._options.reValidateMode).isOnSubmit)if(s._options.resolver)s._runSchema([a]).then(e=>{const t=_(e.errors,a),r=_(s._formState.errors,a);(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type)&&(t?h(s._formState.errors,a,t):ae(s._formState.errors,a),s._subjects.state.next({errors:s._formState.errors}))});else{const e=_(s._fields,a);!e||!e._f||pe(s._options.reValidateMode).isOnSubmit&&pe(s._options.mode).isOnSubmit||De(e,s._names.disabled,s._formValues,s._options.criteriaMode===w,s._options.shouldUseNativeValidation,!0).then(e=>!X(e)&&s._subjects.state.next({errors:xe(s._formState.errors,e,a)}))}s._subjects.state.next({name:a,values:f(s._formValues)}),s._names.focus&&Fe(s._fields,(e,t)=>{if(s._names.focus&&t.startsWith(s._names.focus)&&e.focus)return e.focus(),1}),s._names.focus="",s._setValid(),y.current=!1},[l,a,s]),t.useEffect(()=>(!_(s._formValues,a)&&s._setFieldArray(a),()=>{s._options.shouldUnregister||n?s.unregister(a):((e,t)=>{const r=_(s._fields,e);r&&r._f&&(r._f.mount=t)})(a,!1)}),[a,s,i,n]),{swap:t.useCallback((e,t)=>{const r=s._getFieldArray(a);Le(r,e,t),Le(d.current,e,t),g(r),u(r),s._setFieldArray(a,r,Le,{argA:e,argB:t},!1)},[g,a,s]),move:t.useCallback((e,t)=>{const r=s._getFieldArray(a);Ne(r,e,t),Ne(d.current,e,t),g(r),u(r),s._setFieldArray(a,r,Ne,{argA:e,argB:t},!1)},[g,a,s]),prepend:t.useCallback((e,t)=>{const r=J(f(e)),i=Re(s._getFieldArray(a),r);s._names.focus=je(a,0,t),d.current=Re(d.current,r.map(Oe)),g(i),u(i),s._setFieldArray(a,i,Re,{argA:Te(e)})},[g,a,s]),append:t.useCallback((e,t)=>{const r=J(f(e)),i=Me(s._getFieldArray(a),r);s._names.focus=je(a,i.length-1,t),d.current=Me(d.current,r.map(Oe)),g(i),u(i),s._setFieldArray(a,i,Me,{argA:Te(e)})},[g,a,s]),remove:t.useCallback(e=>{const t=Be(s._getFieldArray(a),e);d.current=Be(d.current,e),g(t),u(t),!Array.isArray(_(s._fields,a))&&h(s._fields,a,void 0),s._setFieldArray(a,t,Be,{argA:e})},[g,a,s]),insert:t.useCallback((e,t,r)=>{const i=J(f(t)),n=Ue(s._getFieldArray(a),e,i);s._names.focus=je(a,e,r),d.current=Ue(d.current,e,i.map(Oe)),g(n),u(n),s._setFieldArray(a,n,Ue,{argA:e,argB:Te(t)})},[g,a,s]),update:t.useCallback((e,t)=>{const r=f(t),i=Pe(s._getFieldArray(a),e,r);d.current=[...i].map((t,r)=>t&&r!==e?d.current[r]:Oe()),g(i),u([...i]),s._setFieldArray(a,i,Pe,{argA:e,argB:r},!0,!1)},[g,a,s]),replace:t.useCallback(e=>{const t=J(f(e));d.current=t.map(Oe),g([...t]),u([...t]),s._setFieldArray(a,[...t],e=>e,{},!0,!1)},[g,a,s]),fields:t.useMemo(()=>l.map((e,t)=>({...e,[i]:d.current[t]||Oe()})),[l,i])}},e.useForm=function(e={}){const r=t.useRef(void 0),s=t.useRef(void 0),[a,i]=t.useState({isDirty:!1,isValidating:!1,isLoading:Z(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:Z(e.defaultValues)?void 0:e.defaultValues});if(!r.current)if(e.formControl)r.current={...e.formControl,formState:a},e.defaultValues&&!Z(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:t,...s}=Ee(e);r.current={...s,formState:a}}const n=r.current.control;return n._options=e,B(()=>{const e=n._subscribe({formState:n._proxyFormState,callback:()=>i({...n._formState}),reRenderRoot:!0});return i(e=>({...e,isReady:!0})),n._formState.isReady=!0,e},[n]),t.useEffect(()=>n._disableForm(e.disabled),[n,e.disabled]),t.useEffect(()=>{e.mode&&(n._options.mode=e.mode),e.reValidateMode&&(n._options.reValidateMode=e.reValidateMode)},[n,e.mode,e.reValidateMode]),t.useEffect(()=>{e.errors&&(n._setErrors(e.errors),n._focusError())},[n,e.errors]),t.useEffect(()=>{e.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})},[n,e.shouldUnregister]),t.useEffect(()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}},[n,a.isDirty]),t.useEffect(()=>{e.values&&!Q(e.values,s.current)?(n._reset(e.values,{keepFieldsRef:!0,...n._options.resetOptions}),s.current=e.values,i(e=>({...e}))):n._resetDefaultValues()},[n,e.values]),t.useEffect(()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()}),r.current.formState=R(a,n),r.current},e.useFormContext=N,e.useFormState=L,e.useWatch=W});
//# sourceMappingURL=index.umd.js.map
