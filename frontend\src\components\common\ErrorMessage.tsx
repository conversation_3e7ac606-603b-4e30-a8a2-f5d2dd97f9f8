import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, X, RefreshCw, Info, CheckCircle, AlertCircle } from 'lucide-react';

interface ErrorMessageProps {
  error?: string | Error | null;
  type?: 'error' | 'warning' | 'info' | 'success';
  title?: string;
  description?: string;
  dismissible?: boolean;
  onDismiss?: () => void;
  onRetry?: () => void;
  className?: string;
  variant?: 'inline' | 'toast' | 'banner';
  showIcon?: boolean;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  error,
  type = 'error',
  title,
  description,
  dismissible = true,
  onDismiss,
  onRetry,
  className = '',
  variant = 'inline',
  showIcon = true,
}) => {
  if (!error && !title && !description) return null;

  const getIcon = () => {
    switch (type) {
      case 'success':
        return CheckCircle;
      case 'warning':
        return AlertTriangle;
      case 'info':
        return Info;
      default:
        return AlertCircle;
    }
  };

  const getColors = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-success/10',
          border: 'border-success/20',
          text: 'text-success',
          icon: 'text-success',
        };
      case 'warning':
        return {
          bg: 'bg-warning/10',
          border: 'border-warning/20',
          text: 'text-warning',
          icon: 'text-warning',
        };
      case 'info':
        return {
          bg: 'bg-info/10',
          border: 'border-info/20',
          text: 'text-info',
          icon: 'text-info',
        };
      default:
        return {
          bg: 'bg-error/10',
          border: 'border-error/20',
          text: 'text-error',
          icon: 'text-error',
        };
    }
  };

  const colors = getColors();
  const Icon = getIcon();

  const errorMessage = error instanceof Error ? error.message : error;
  const displayTitle = title || (type === 'error' ? 'Error' : type.charAt(0).toUpperCase() + type.slice(1));
  const displayDescription = description || errorMessage;

  const baseClasses = `rounded-lg border ${colors.bg} ${colors.border} ${className}`;

  const variantClasses = {
    inline: 'p-4',
    toast: 'p-4 shadow-lg backdrop-blur-sm',
    banner: 'p-3 border-l-4 border-r-0 border-t-0 border-b-0',
  };

  return (
    <AnimatePresence>
      <motion.div
        className={`${baseClasses} ${variantClasses[variant]}`}
        initial={{ opacity: 0, y: variant === 'toast' ? -20 : 0, scale: variant === 'toast' ? 0.95 : 1 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: variant === 'toast' ? -20 : 0, scale: variant === 'toast' ? 0.95 : 1 }}
        transition={{ duration: 0.2 }}
      >
        <div className="flex items-start gap-3">
          {/* Icon */}
          {showIcon && (
            <motion.div
              className={`flex-shrink-0 ${colors.icon}`}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.1, type: "spring", stiffness: 500, damping: 30 }}
            >
              <Icon className="w-5 h-5" />
            </motion.div>
          )}

          {/* Content */}
          <div className="flex-1 min-w-0">
            {displayTitle && (
              <h4 className={`font-medium ${colors.text} mb-1`}>
                {displayTitle}
              </h4>
            )}
            
            {displayDescription && (
              <p className="text-sm text-primary-300">
                {displayDescription}
              </p>
            )}

            {/* Actions */}
            {onRetry && (
              <motion.button
                className={`mt-3 inline-flex items-center gap-2 text-sm ${colors.text} hover:opacity-80 transition-opacity`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={onRetry}
              >
                <RefreshCw className="w-4 h-4" />
                Try Again
              </motion.button>
            )}
          </div>

          {/* Dismiss Button */}
          {dismissible && onDismiss && (
            <motion.button
              className="flex-shrink-0 text-primary-400 hover:text-primary-300 transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onDismiss}
            >
              <X className="w-4 h-4" />
            </motion.button>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

// Specialized error components
export const NetworkError: React.FC<{ onRetry?: () => void; onDismiss?: () => void }> = ({ 
  onRetry, 
  onDismiss 
}) => (
  <ErrorMessage
    title="Connection Error"
    description="Unable to connect to the server. Please check your internet connection and try again."
    onRetry={onRetry}
    onDismiss={onDismiss}
  />
);

export const ValidationError: React.FC<{ 
  errors: string[]; 
  onDismiss?: () => void;
}> = ({ errors, onDismiss }) => (
  <ErrorMessage
    title="Validation Error"
    description={errors.length === 1 ? errors[0] : `${errors.length} validation errors occurred`}
    onDismiss={onDismiss}
    variant="banner"
  />
);

export const SuccessMessage: React.FC<{ 
  message: string; 
  onDismiss?: () => void;
}> = ({ message, onDismiss }) => (
  <ErrorMessage
    type="success"
    title="Success"
    description={message}
    onDismiss={onDismiss}
    variant="toast"
  />
);

export const WarningMessage: React.FC<{ 
  message: string; 
  onDismiss?: () => void;
}> = ({ message, onDismiss }) => (
  <ErrorMessage
    type="warning"
    title="Warning"
    description={message}
    onDismiss={onDismiss}
  />
);

export const InfoMessage: React.FC<{ 
  message: string; 
  onDismiss?: () => void;
}> = ({ message, onDismiss }) => (
  <ErrorMessage
    type="info"
    title="Information"
    description={message}
    onDismiss={onDismiss}
    variant="banner"
  />
);

export default ErrorMessage;
