import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@/test/utils';
import ErrorMessage, {
  NetworkError,
  ValidationError,
  SuccessMessage,
  WarningMessage,
  InfoMessage
} from '../ErrorMessage';

describe('ErrorMessage', () => {
  it('renders nothing when no error provided', () => {
    const { container } = render(<ErrorMessage />);
    expect(container.firstChild).toBeNull();
  });

  it('renders error message', () => {
    render(<ErrorMessage error="Test error message" />);
    expect(screen.getByText('Test error message')).toBeInTheDocument();
  });

  it('renders error object', () => {
    const error = new Error('Test error object');
    render(<ErrorMessage error={error} />);
    expect(screen.getByText('Test error object')).toBeInTheDocument();
  });

  it('renders with custom title', () => {
    render(<ErrorMessage error="Test error" title="Custom Title" />);
    expect(screen.getByText('Custom Title')).toBeInTheDocument();
  });

  it('renders with description', () => {
    render(<ErrorMessage description="Test description" />);
    expect(screen.getByText('Test description')).toBeInTheDocument();
  });

  it('renders different types with correct styling', () => {
    const { rerender } = render(<ErrorMessage error="Test" type="success" />);
    expect(screen.getByTestId('mock-icon')).toBeInTheDocument();

    rerender(<ErrorMessage error="Test" type="warning" />);
    expect(screen.getByTestId('mock-icon')).toBeInTheDocument();

    rerender(<ErrorMessage error="Test" type="info" />);
    expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
  });

  it('calls onDismiss when dismiss button clicked', () => {
    const onDismiss = vi.fn();
    render(<ErrorMessage error="Test error" onDismiss={onDismiss} />);
    
    const dismissButton = screen.getByTestId('mock-icon');
    fireEvent.click(dismissButton);
    
    expect(onDismiss).toHaveBeenCalledOnce();
  });

  it('calls onRetry when retry button clicked', () => {
    const onRetry = vi.fn();
    render(<ErrorMessage error="Test error" onRetry={onRetry} />);
    
    const retryButton = screen.getByText('Try Again');
    fireEvent.click(retryButton);
    
    expect(onRetry).toHaveBeenCalledOnce();
  });

  it('hides dismiss button when dismissible is false', () => {
    render(<ErrorMessage error="Test error" dismissible={false} />);
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('hides icon when showIcon is false', () => {
    render(<ErrorMessage error="Test error" showIcon={false} />);
    expect(screen.queryByTestId('mock-icon')).not.toBeInTheDocument();
  });

  it('renders different variants', () => {
    const { rerender } = render(<ErrorMessage error="Test" variant="inline" />);
    expect(screen.getByText('Test')).toBeInTheDocument();

    rerender(<ErrorMessage error="Test" variant="toast" />);
    expect(screen.getByText('Test')).toBeInTheDocument();

    rerender(<ErrorMessage error="Test" variant="banner" />);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });
});

describe('Specialized Error Components', () => {
  it('renders NetworkError', () => {
    const onRetry = vi.fn();
    const onDismiss = vi.fn();
    
    render(<NetworkError onRetry={onRetry} onDismiss={onDismiss} />);
    
    expect(screen.getByText('Connection Error')).toBeInTheDocument();
    expect(screen.getByText(/Unable to connect to the server/)).toBeInTheDocument();
    
    fireEvent.click(screen.getByText('Try Again'));
    expect(onRetry).toHaveBeenCalledOnce();
  });

  it('renders ValidationError with single error', () => {
    const onDismiss = vi.fn();
    
    render(<ValidationError errors={['Field is required']} onDismiss={onDismiss} />);
    
    expect(screen.getByText('Validation Error')).toBeInTheDocument();
    expect(screen.getByText('Field is required')).toBeInTheDocument();
  });

  it('renders ValidationError with multiple errors', () => {
    const errors = ['Field 1 is required', 'Field 2 is invalid'];
    
    render(<ValidationError errors={errors} />);
    
    expect(screen.getByText('Validation Error')).toBeInTheDocument();
    expect(screen.getByText('2 validation errors occurred')).toBeInTheDocument();
  });

  it('renders SuccessMessage', () => {
    const onDismiss = vi.fn();
    
    render(<SuccessMessage message="Operation successful" onDismiss={onDismiss} />);
    
    expect(screen.getByText('Success')).toBeInTheDocument();
    expect(screen.getByText('Operation successful')).toBeInTheDocument();
  });

  it('renders WarningMessage', () => {
    const onDismiss = vi.fn();
    
    render(<WarningMessage message="This is a warning" onDismiss={onDismiss} />);
    
    expect(screen.getByText('Warning')).toBeInTheDocument();
    expect(screen.getByText('This is a warning')).toBeInTheDocument();
  });

  it('renders InfoMessage', () => {
    const onDismiss = vi.fn();
    
    render(<InfoMessage message="This is information" onDismiss={onDismiss} />);
    
    expect(screen.getByText('Information')).toBeInTheDocument();
    expect(screen.getByText('This is information')).toBeInTheDocument();
  });
});
