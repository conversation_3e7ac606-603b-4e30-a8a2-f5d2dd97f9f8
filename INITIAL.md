## FEATURE (Create app from scratch):
<FEATURE>
# Create me a very modern, personal (no auth needed for now) (black, grey and white only) dashboard, with inCREDIBLY smooth animations and effects everywhere, to make the dashboard feel alive. The dashboard should have a sidebar (sub-pages).

LLM AI Powered Dashboard components:
## Main thing - INPUT BAR (main feature of this app) on top of page (hero).
-> User should be able to input anything in this bar and it will get categorized by AI (ABSOLUTELY NO KEYWORDS HARDCODED) into the preset 3 categories.
-> Categories include: 
1. Tasks (like "Do Biology Homework"); 
2. Reminder/Event (like "Math Homework Due at 2PM tomorrow - 20 July"); 
3. Question to AI (like "What does precipitation mean?" - categorize question into sub-categories (***talk more about it below) and then present the answer in the dashboard). All 3 of these categories should give the respective visual feedback to the user. 

*VITAL: The user ABSOLUTELY needs to SEE what the agent/program is doing in the background - Eg. "Add task ***" -> Visual feedback: *Visual animations, "Working" -> "Absolutely! I added your task".

*** "Question to AI" Sub-categories:
LLM decides which tools to call on its own (NO KEYWORD HARDCODING AT ALL)
- Structured, Semantic Database search with LLM - definable properties (LLM-powered smart, intelligent database search utilizing intelligent tool calling)
- Web search via Langsearch (or better alternative) search method which searches multiple sites (LLM-powered web search utilizing intelligent tool calling) -> Afterwhich answer is presented and structured (with links used)
- Simple question - to which the model might know the answer to in its training data.

## Calendar: 
- Should be integrated with the TASKS (tasks with no date are just added to the present day at the time of writing / input), and REMINDER/EVENTS (added to specified TIME and DATE - if no time / date is supplied, ask user when they want it saved). 
- "Calendar" tool should be available to AI LLM - It should be able to read the calendar month, read which events are scheduled, and obviously be able to add events from the INPUT BAR.

## Smart Task list:
- The list to which tasks from the INPUT BAR are added - also displayed on dashboard.
- This list should be automatically categorized into different sub-categories (DEFINED BY AI but ALSO DEFINABLE/EDITABLE BY USER). For eg. BIO - 490 (section which contains all events / tasks for BIO 490 for eg.).
- Filter function.
- "Tasks" tool should be available to AI LLM - it should be able to read the task list when prompted, and obviously be able to add tasks from the INPUT BAR.

## Settings page:
- LLM AI API settings - like the api key, which model the user wants to use.
- Other relevant settings.
- Embeddings & Index status and settings

## TECHNICAL SPECIFICATIONS:
- ABSOLUTELY ABSOLUTELY INCREDIBLY VITAL: Follow the workflow, especially the input in hero bar workflow in \examples\ai_orchestrator_flow.mermaid (also displayed below)
- React, Tailwind, Framer Motion (for animations), React Hook Form (for handling the hero input bar efficiently)
- Relevant database - with persistence (when page gets refreshed, current response of AI data stays - this includes text already inserted in INPUT BAR but not yet sent). When the user asks a new question, the old question disappears and new answer replaces it (No sessions implemented).
- Embeddings via Ollama (with relevant free local model which the AI can then use to more effectively do semantic searches with the indexed data).
- AI Agents with Mirascope - Documentation provided below.
- AI LLM Provider: OpenRouter (Although Free models will be used - Adapt tool calling to work because the free models do not natively support tool calling VIA openrouter).
</FEATURE>

## DOCUMENTATION:

# Your Bible you need to follow by heart:
\examples\ai_orchestrator_flow.mermaid
- Follow the animation guide in this diagram like your life depends on it!! And implement the FLOW and EVERYTHING in this diagram EXACTLY!!

# Links to scrape:
- React Documentation: https://react.dev/learn - For building the modern dashboard UI
- Framer Motion: https://www.framer.com/motion/ - For the smooth animations you emphasized
- Tailwind CSS: https://tailwindcss.com/docs - For the modern black/grey/white styling
- React Hook Form: https://react-hook-form.com/ - For handling the hero input bar efficiently
- Mirascope: https://mirascope.com/docs/mirascope
- Absolutely VITAL resource: The Mirascope LLMS.txt file. Scrape this and use it to do subsequent research: https://mirascope.com/docs/mirascope/llms-full.txt
- Custom Provider: https://mirascope.com/docs/mirascope/learn/extensions/custom_provider (Not sure if this would be helpful for OpenRouter - because it is openai compatible i think)
- https://mirascope.com/docs/mirascope/learn
- https://mirascope.com/docs/mirascope/learn/prompts
- https://mirascope.com/docs/mirascope/learn/calls
- Streams: Important for visual feedback to user: https://mirascope.com/docs/mirascope/learn/streams
- https://mirascope.com/docs/mirascope/learn/chaining
- Probably useful for models that dont support native tool calling, right: https://mirascope.com/docs/mirascope/learn/response_models , https://mirascope.com/docs/mirascope/learn/json_mode , https://mirascope.com/docs/mirascope/learn/output_parsers
- https://mirascope.com/docs/mirascope/learn/tools
- Important: https://mirascope.com/docs/mirascope/learn/agents
- https://mirascope.com/docs/mirascope/learn/evals
- https://mirascope.com/docs/mirascope/learn/async
- https://mirascope.com/docs/mirascope/learn/retries
- Openai compatible: https://mirascope.com/docs/mirascope/learn/local_models   
- Langsearch for web search tool: https://docs.langsearch.com/api/web-search-api
- Database search tool (I dont know if this will be useful, but please take a look): https://docs.langsearch.com/product/langsearch-database , https://docs.langsearch.com/product/langsearch-reranker
- Database - SQlite: https://www.sqlite.org/docs.html
- 

# Other info:
- How to do a Jina curl:
If a page 404s or does not scrape properly, scrape it again

Do not use Jina to scrape CSS of the design site.

All SEPARATE pages must be stored in /research/[technology]/page1/2/3/4/5/6/7/8 etc. directories with individual .md files.

Use POWERSHELL format:
Invoke-WebRequest -Uri "https://r.jina.ai/https://flask.palletsprojects.com/en/stable/templating/" -Headers @{"Authorization" = "Bearer jina_0422e2a5df094b8aa898f263607e8273OOzeKWm4Y3u-y6YdGj_WQb7cIUwj"}


## OTHER CONSIDERATIONS:

- Include the project structure in the README.
- Virtual environment needs to be set up if needed
- Use Docker (docker desktop is installed and ready) for setting up application - easy development and allows you to test everything (logs)
- Use playwright MCP with vision mode activated, ensure to use it to see browser logs, and also to see screenshots to improve UI/UX
- If running commands in the terminal, use powershell syntax (VITAL)
- Mermaid Diagram you need to ABSOLUTELY ALWAYS follow:

graph TD
    A[User Input in Hero Bar] --> B[🎭 'Analyzing input...'<br/>⚡ TYPING INDICATOR]
    B --> C[🤖 'Categorizing...'<br/>⚡ BRAIN ANIMATION]
    
    C --> D{🎯 Category Decision<br/>⚡ SELECTION HIGHLIGHT}
    
    %% Task Flow with Visual Transparency
    D -->|TASK| E[📝 'Task identified!'<br/>⚡ GREEN PULSE]
    E --> F[⚙️ 'Extracting details...'<br/>⚡ SPINNER ANIMATION]
    F --> G[🏷️ 'Auto-categorizing...'<br/>⚡ TAG ANIMATIONS]
    G --> H[📅 'Checking for dates...'<br/>⚡ CALENDAR SCAN]
    H --> I{⏰ Has Date?<br/>⚡ DATE HIGHLIGHT}
    I -->|No| J[📌 'Adding to today...'<br/>⚡ DROP ANIMATION]
    I -->|Yes| K[🗓️ 'Scheduling...'<br/>⚡ SLIDE TO DATE]
    J --> L[📋 'Updating task list...'<br/>⚡ LIST GROW ANIMATION]
    K --> L
    L --> M[📅 'Syncing calendar...'<br/>⚡ SYNC SPINNER]
    M --> N[✅ 'Task added successfully!'<br/>⚡ SUCCESS CONFETTI]
    
    %% Event/Reminder Flow with Visual Transparency
    D -->|EVENT/REMINDER| O[📋 'Event identified!'<br/>⚡ BLUE PULSE]
    O --> P[🕐 'Extracting date/time...'<br/>⚡ CLOCK ANIMATION]
    P --> Q{📆 Complete DateTime?<br/>⚡ INFO CHECK}
    Q -->|No| R[❓ 'Need more info...'<br/>⚡ INPUT PROMPT SLIDE]
    Q -->|Yes| S[📅 'Adding to calendar...'<br/>⚡ CALENDAR DROP]
    R --> T[⏳ 'Waiting for response...'<br/>⚡ BREATHING ANIMATION]
    T --> S
    S --> U[🔄 'Syncing calendar...'<br/>⚡ SYNC ANIMATION]
    U --> V[✅ 'Event scheduled!'<br/>⚡ SUCCESS SPARKLE]
    
    %% AI Question Flow with Visual Transparency
    D -->|AI QUESTION| W[❓ 'Question identified!'<br/>⚡ PURPLE PULSE]
    W --> X[🧠 'Analyzing query type...'<br/>⚡ THINKING DOTS]
    X --> Y{🎯 Query Type Decision<br/>⚡ TOOL HIGHLIGHT}
    
    %% Simple Knowledge Query
    Y -->|Simple Knowledge| Z[🤖 'Accessing knowledge...'<br/>⚡ BRAIN GLOW]
    Z --> AA[📝 'Formatting answer...'<br/>⚡ TEXT TYPEWRITER]
    AA --> BB[✅ 'Answer ready!'<br/>⚡ REVEAL ANIMATION]
    
    %% Database Search
    Y -->|Database Search| CC[🔍 'Searching database...'<br/>⚡ RADAR SCAN]
    CC --> DD[🧮 'Generating embeddings...'<br/>⚡ MATRIX ANIMATION]
    DD --> EE[📊 'Vector searching...'<br/>⚡ PROGRESS DOTS]
    EE --> FF[📄 'Found relevant docs...'<br/>⚡ DOCUMENT FLY-IN]
    FF --> GG[🤖 'Processing context...'<br/>⚡ LOADING SPINNER]
    GG --> HH[📝 'Generating answer...'<br/>⚡ TYPEWRITER EFFECT]
    HH --> II[✅ 'Database search complete!'<br/>⚡ SUCCESS FADE-IN]
    
    %% Web Search
    Y -->|Web Search| JJ[🌐 'Starting web search...'<br/>⚡ GLOBE SPIN]
    JJ --> KK[🔗 'Calling LangSearch API...'<br/>⚡ API PULSE]
    KK --> LL[⚙️ 'Processing results...'<br/>⚡ GEAR ANIMATION]
    LL --> MM[🤖 'Summarizing findings...'<br/>⚡ TEXT PROCESSING]
    MM --> NN[🔗 'Adding sources...'<br/>⚡ LINK ANIMATIONS]
    NN --> OO[✅ 'Web search complete!'<br/>⚡ SUCCESS BURST]
    
    %% Final Display with Smooth Transitions
    N --> PP[🎭 Dashboard Update<br/>⚡ SMOOTH TRANSITIONS]
    V --> PP
    BB --> PP
    II --> PP
    OO --> PP
    PP --> QQ[🎬 Real-time UI Animation<br/>⚡ FRAMER MOTION]
    QQ --> RR[👀 User Sees Result<br/>⚡ FINAL REVEAL]
    
    %% Tool Availability with Visual States
    subgraph Tools[🛠️ AVAILABLE LLM TOOLS + VISUAL STATES]
        TT["📅 Calendar Tool<br/>⚡ CALENDAR ANIMATIONS<br/>- Read events → 📖 Scanning...<br/>- Add events → ➕ Dropping in...<br/>- Check availability → 🔍 Checking..."]
        UU["📋 Tasks Tool<br/>⚡ LIST ANIMATIONS<br/>- Read task list → 📄 Loading...<br/>- Add tasks → ✨ Adding...<br/>- Update categories → 🏷️ Categorizing..."]
        VV["🔍 Database Search Tool<br/>⚡ SEARCH ANIMATIONS<br/>- Semantic search → 🎯 Searching...<br/>- Vector similarity → 🧮 Calculating...<br/>- Context retrieval → 📄 Retrieving..."]
        WW["🌐 Web Search Tool<br/>⚡ WEB ANIMATIONS<br/>- LangSearch API → 🌍 Searching web...<br/>- Multi-site search → 🔗 Crawling sites...<br/>- Source attribution → 📝 Citing sources..."]
    end
    
    %% Visual Feedback States with Transparency
    subgraph Feedback[🎬 VISUAL TRANSPARENCY SYSTEM]
        XX["🔄 Processing Animation<br/>⚡ SMOOTH FADE-INS"]
        YY["🤖 'Categorizing...'<br/>⚡ TYPING ANIMATION"]
        ZZ["🧠 'Task identified...'<br/>⚡ CATEGORY HIGHLIGHT"]
        AAA["⚙️ 'Extracting details...'<br/>⚡ PULSE ANIMATION"]
        BBB["✅ 'Task list updated!'<br/>⚡ SUCCESS CONFETTI"]
        CCC["❌ Error with Retry<br/>⚡ SHAKE ANIMATION"]
        
        DDD["🔍 'Searching database...'<br/>⚡ LOADING DOTS"]
        EEE["🌐 'Searching web...'<br/>⚡ PROGRESS BAR"]
        FFF["📅 'Adding to calendar...'<br/>⚡ SLIDE ANIMATION"]
        GGG["📝 'Processing reminder...'<br/>⚡ GLOW EFFECT"]
    end
    
    %% Database Components
    subgraph Database[Local Data Layer]
        DDD[(SQLite<br/>Tasks & Events)]
        EEE[(Vector DB<br/>Embeddings)]
        FFF[Ollama<br/>Local Embeddings]
    end
    
    %% Real-time Updates with Animation Details
    subgraph Realtime[⚡ REAL-TIME ANIMATION SYSTEM]
        GGG["🔌 WebSocket Connection<br/>⚡ PULSE INDICATOR"]
        HHH["⚛️ React State Updates<br/>⚡ STATE TRANSITIONS"]
        III["🎬 Framer Motion Animations<br/>⚡ SMOOTH 60FPS ANIMATIONS<br/>- Spring physics<br/>- Stagger effects<br/>- Page transitions<br/>- Micro-interactions"]
        JJJ["🎭 Animation Orchestration<br/>⚡ SEQUENCE TIMING<br/>- Enter animations<br/>- Exit animations<br/>- Loading states<br/>- Success celebrations"]
    end
    
    %% Styling
    classDef task fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#e2e8f0
    classDef event fill:#2b341f,stroke:#38a169,stroke-width:2px,color:#e2e8f0
    classDef question fill:#1a202c,stroke:#63b3ed,stroke-width:2px,color:#e2e8f0
    classDef tool fill:#2d2d2d,stroke:#a0aec0,stroke-width:2px,color:#f7fafc
    classDef feedback fill:#1a1a1a,stroke:#f6ad55,stroke-width:2px,color:#fffaf0
    classDef data fill:#2c1810,stroke:#d69e2e,stroke-width:2px,color:#faf089
    
    class E,F,G,H,I,J,K,L,M,N task
    class O,P,Q,R,S,T,U,V event
    class W,X,Y,Z,AA,BB,CC,DD,EE,FF,GG,HH,II,JJ,KK,LL,MM,NN,OO question
    class TT,UU,VV,WW tool
    class XX,YY,ZZ,AAA,BBB,CCC feedback
    class DDD,EEE,FFF data
