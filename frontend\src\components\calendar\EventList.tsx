import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, Clock, MapPin } from 'lucide-react';
import { format, isToday, isTomorrow, isYesterday } from 'date-fns';

import { Event } from '@/types';

interface EventListProps {
  events: Event[];
  selectedDate: Date;
  loading: boolean;
}

const EventList: React.FC<EventListProps> = ({ events, selectedDate, loading }) => {
  const getDateLabel = (date: Date) => {
    if (isToday(date)) return 'Today';
    if (isTomorrow(date)) return 'Tomorrow';
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'MMM d');
  };

  const getEventsForSelectedDate = () => {
    return events.filter(event => {
      const eventDate = new Date(event.start_time);
      return eventDate.toDateString() === selectedDate.toDateString();
    });
  };

  const getUpcomingEvents = () => {
    const now = new Date();
    return events
      .filter(event => new Date(event.start_time) > now)
      .sort((a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime())
      .slice(0, 5);
  };

  const selectedDateEvents = getEventsForSelectedDate();
  const upcomingEvents = getUpcomingEvents();

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="card">
          <div className="h-6 bg-primary-600 rounded mb-4 animate-pulse"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="mb-3 last:mb-0">
              <div className="h-4 bg-primary-700 rounded mb-2 animate-pulse"></div>
              <div className="h-3 bg-primary-800 rounded w-3/4 animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Selected Date Events */}
      <motion.div
        className="card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center gap-2 mb-4">
          <Calendar className="w-5 h-5 text-info" />
          <h3 className="font-semibold text-primary-200">
            {getDateLabel(selectedDate)}
          </h3>
        </div>

        <AnimatePresence>
          {selectedDateEvents.length === 0 ? (
            <motion.div
              className="text-center py-6 text-primary-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <Calendar className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No events scheduled</p>
            </motion.div>
          ) : (
            <div className="space-y-3">
              {selectedDateEvents.map((event, index) => (
                <motion.div
                  key={event.id}
                  className="p-3 bg-info/10 border border-info/20 rounded-lg hover:bg-info/15 transition-colors"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                >
                  <h4 className="font-medium text-primary-200 mb-1">
                    {event.title}
                  </h4>
                  
                  <div className="flex items-center gap-2 text-xs text-primary-400 mb-1">
                    <Clock className="w-3 h-3" />
                    <span>
                      {format(new Date(event.start_time), 'HH:mm')} - 
                      {format(new Date(event.end_time), 'HH:mm')}
                    </span>
                  </div>

                  {event.location && (
                    <div className="flex items-center gap-2 text-xs text-primary-400 mb-1">
                      <MapPin className="w-3 h-3" />
                      <span>{event.location}</span>
                    </div>
                  )}

                  {event.description && (
                    <p className="text-xs text-primary-300 mt-2 line-clamp-2">
                      {event.description}
                    </p>
                  )}

                  {event.ai_generated_category && (
                    <div className="mt-2">
                      <span className="inline-block px-2 py-1 bg-primary-600/20 text-primary-300 rounded-full text-xs">
                        🏷️ {event.ai_generated_category}
                      </span>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Upcoming Events */}
      <motion.div
        className="card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="flex items-center gap-2 mb-4">
          <Clock className="w-5 h-5 text-warning" />
          <h3 className="font-semibold text-primary-200">Upcoming</h3>
        </div>

        <AnimatePresence>
          {upcomingEvents.length === 0 ? (
            <motion.div
              className="text-center py-6 text-primary-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No upcoming events</p>
            </motion.div>
          ) : (
            <div className="space-y-2">
              {upcomingEvents.map((event, index) => (
                <motion.div
                  key={event.id}
                  className="p-2 hover:bg-primary-800/50 rounded-lg transition-colors"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  whileHover={{ scale: 1.01 }}
                >
                  <h5 className="font-medium text-primary-200 text-sm mb-1">
                    {event.title}
                  </h5>
                  
                  <div className="text-xs text-primary-400">
                    {getDateLabel(new Date(event.start_time))} at{' '}
                    {format(new Date(event.start_time), 'HH:mm')}
                  </div>

                  {event.location && (
                    <div className="text-xs text-primary-500 mt-1">
                      📍 {event.location}
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Quick Stats */}
      <motion.div
        className="card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <h3 className="font-semibold text-primary-200 mb-3">Quick Stats</h3>
        
        <div className="grid grid-cols-2 gap-3">
          <div className="text-center p-2 bg-primary-800/50 rounded-lg">
            <div className="text-lg font-bold text-info">
              {events.length}
            </div>
            <div className="text-xs text-primary-400">Total Events</div>
          </div>
          
          <div className="text-center p-2 bg-primary-800/50 rounded-lg">
            <div className="text-lg font-bold text-warning">
              {upcomingEvents.length}
            </div>
            <div className="text-xs text-primary-400">Upcoming</div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default EventList;
