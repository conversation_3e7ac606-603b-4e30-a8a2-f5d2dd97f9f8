"""Integration tests for the AI-Powered Dashboard."""

import pytest
import asyncio
from httpx import Async<PERSON>lient
from fastapi.testclient import TestClient

from app.main import app
from app.database.connection import init_database, AsyncSessionLocal
from app.models.database import TaskModel, EventModel


@pytest.fixture
async def async_client():
    """Create async test client."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(autouse=True)
async def setup_test_database():
    """Setup test database before each test."""
    await init_database()
    yield
    # Cleanup after test
    async with AsyncSessionLocal() as session:
        await session.execute("DELETE FROM tasks")
        await session.execute("DELETE FROM events")
        await session.commit()


class TestHealthCheck:
    """Test health check endpoints."""
    
    async def test_health_endpoint(self, async_client):
        """Test basic health check."""
        response = await async_client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] in ["healthy", "degraded"]
        assert data["service"] == "ai-dashboard-backend"
        assert "database" in data
        assert "stats" in data


class TestTaskAPI:
    """Test task management API."""
    
    async def test_create_task(self, async_client):
        """Test task creation."""
        task_data = {
            "title": "Test Task",
            "description": "Test Description",
            "priority": "medium",
            "category": "work"
        }
        
        response = await async_client.post("/api/tasks", json=task_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data["title"] == task_data["title"]
        assert data["description"] == task_data["description"]
        assert data["priority"] == task_data["priority"]
        assert "id" in data
        assert "created_at" in data
    
    async def test_get_tasks(self, async_client):
        """Test getting tasks."""
        # Create a test task first
        async with AsyncSessionLocal() as session:
            task = TaskModel(
                title="Test Task",
                description="Test Description",
                priority="high",
                category="work",
                completed=False
            )
            session.add(task)
            await session.commit()
        
        response = await async_client.get("/api/tasks")
        assert response.status_code == 200
        
        data = response.json()
        assert "tasks" in data
        assert len(data["tasks"]) >= 1
        assert data["tasks"][0]["title"] == "Test Task"
    
    async def test_update_task(self, async_client):
        """Test task update."""
        # Create a test task first
        async with AsyncSessionLocal() as session:
            task = TaskModel(
                title="Original Task",
                description="Original Description",
                priority="low",
                category="personal",
                completed=False
            )
            session.add(task)
            await session.commit()
            await session.refresh(task)
            task_id = task.id
        
        # Update the task
        update_data = {
            "title": "Updated Task",
            "completed": True
        }
        
        response = await async_client.put(f"/api/tasks/{task_id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["title"] == "Updated Task"
        assert data["completed"] is True
    
    async def test_delete_task(self, async_client):
        """Test task deletion."""
        # Create a test task first
        async with AsyncSessionLocal() as session:
            task = TaskModel(
                title="Task to Delete",
                description="Will be deleted",
                priority="medium",
                category="work",
                completed=False
            )
            session.add(task)
            await session.commit()
            await session.refresh(task)
            task_id = task.id
        
        # Delete the task
        response = await async_client.delete(f"/api/tasks/{task_id}")
        assert response.status_code == 204
        
        # Verify it's deleted
        response = await async_client.get(f"/api/tasks/{task_id}")
        assert response.status_code == 404


class TestEventAPI:
    """Test event management API."""
    
    async def test_create_event(self, async_client):
        """Test event creation."""
        event_data = {
            "title": "Test Event",
            "description": "Test Description",
            "start_time": "2024-12-01T10:00:00Z",
            "end_time": "2024-12-01T11:00:00Z",
            "location": "Test Location"
        }
        
        response = await async_client.post("/api/events", json=event_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data["title"] == event_data["title"]
        assert data["location"] == event_data["location"]
        assert "id" in data
    
    async def test_get_events(self, async_client):
        """Test getting events."""
        # Create a test event first
        async with AsyncSessionLocal() as session:
            event = EventModel(
                title="Test Event",
                description="Test Description",
                start_time="2024-12-01T10:00:00Z",
                end_time="2024-12-01T11:00:00Z",
                location="Test Location"
            )
            session.add(event)
            await session.commit()
        
        response = await async_client.get("/api/events")
        assert response.status_code == 200
        
        data = response.json()
        assert "events" in data
        assert len(data["events"]) >= 1


class TestInputProcessing:
    """Test input processing workflow."""
    
    async def test_process_task_input(self, async_client):
        """Test processing task input."""
        input_data = {
            "text": "Write documentation for the project by Friday",
            "timestamp": "2024-01-01T12:00:00Z"
        }
        
        # Note: This test might need to be mocked if external APIs are not available
        response = await async_client.post("/api/process-input", json=input_data)
        
        # The response might be a stream, so we check for successful start
        assert response.status_code in [200, 202]  # Accept both sync and async responses
    
    async def test_process_event_input(self, async_client):
        """Test processing event input."""
        input_data = {
            "text": "Meeting with team tomorrow at 2pm in conference room",
            "timestamp": "2024-01-01T12:00:00Z"
        }
        
        response = await async_client.post("/api/process-input", json=input_data)
        assert response.status_code in [200, 202]
    
    async def test_process_question_input(self, async_client):
        """Test processing question input."""
        input_data = {
            "text": "What is the weather like today?",
            "timestamp": "2024-01-01T12:00:00Z"
        }
        
        response = await async_client.post("/api/process-input", json=input_data)
        assert response.status_code in [200, 202]


class TestWebSocketConnection:
    """Test WebSocket functionality."""
    
    def test_websocket_connection(self):
        """Test WebSocket connection."""
        client = TestClient(app)
        
        with client.websocket_connect("/ws") as websocket:
            # Test ping/pong
            websocket.send_json({"type": "ping"})
            data = websocket.receive_json()
            assert data["type"] == "pong"


class TestDatabaseOperations:
    """Test database operations."""
    
    async def test_database_stats(self, async_client):
        """Test database statistics endpoint."""
        response = await async_client.get("/api/stats")
        
        if response.status_code == 200:
            data = response.json()
            assert "tasks_count" in data
            assert "events_count" in data
    
    async def test_task_categories(self, async_client):
        """Test task categories endpoint."""
        response = await async_client.get("/api/task-categories")
        assert response.status_code == 200
        
        data = response.json()
        assert "all_categories" in data
        assert isinstance(data["all_categories"], list)


class TestErrorHandling:
    """Test error handling."""
    
    async def test_invalid_task_creation(self, async_client):
        """Test invalid task creation."""
        invalid_data = {
            "title": "",  # Empty title should fail
            "priority": "invalid_priority"
        }
        
        response = await async_client.post("/api/tasks", json=invalid_data)
        assert response.status_code == 422  # Validation error
    
    async def test_nonexistent_task(self, async_client):
        """Test accessing nonexistent task."""
        response = await async_client.get("/api/tasks/99999")
        assert response.status_code == 404
    
    async def test_invalid_event_creation(self, async_client):
        """Test invalid event creation."""
        invalid_data = {
            "title": "Test Event",
            "start_time": "invalid_datetime",
            "end_time": "2024-12-01T11:00:00Z"
        }
        
        response = await async_client.post("/api/events", json=invalid_data)
        assert response.status_code == 422  # Validation error


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
