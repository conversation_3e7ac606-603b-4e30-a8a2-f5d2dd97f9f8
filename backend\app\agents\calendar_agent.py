"""Calendar/Event processing agent for scheduling and reminders."""

import logging
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime, timedelta
import re

from mirascope import llm
from pydantic import BaseModel, Field

from app.models.pydantic_models import UserInput, Event
from app.config import settings

logger = logging.getLogger(__name__)


class EventExtraction(BaseModel):
    """Structured response for event extraction."""
    title: str = Field(..., description="Clear event title")
    description: Optional[str] = Field(None, description="Event description")
    start_time_text: Optional[str] = Field(None, description="Start time mentioned in text")
    end_time_text: Optional[str] = Field(None, description="End time mentioned in text")
    location: Optional[str] = Field(None, description="Event location if mentioned")
    has_complete_datetime: bool = Field(default=False, description="Whether complete date/time info is available")
    missing_info: list = Field(default=[], description="List of missing information needed")


class CalendarAgent:
    """Agent for event/reminder processing."""
    
    def __init__(self):
        self.history = []
    
    @llm.call(
        provider="openai",
        model=settings.default_model,
        response_model=EventExtraction,
        base_url="https://openrouter.ai/api/v1",
        api_key=settings.openrouter_api_key
    )
    async def extract_event_details(self, user_input: UserInput) -> str:
        """
        Extract event details with intelligent parsing.
        """
        return f"""
        You are an event extraction specialist. Analyze the user's input for scheduling information.

        User input: "{user_input.text}"

        Extract the following information:
        1. **Title**: Create a clear event title
        2. **Description**: Add helpful details about the event
        3. **Start Time**: Extract any time references (2PM, tomorrow at 3, next Friday, etc.)
        4. **End Time**: Extract end time if mentioned (duration or specific end time)
        5. **Location**: Extract location information if provided
        6. **Complete DateTime**: Assess if you have enough info to schedule the event
        7. **Missing Info**: List what additional information is needed

        Time parsing examples:
        - "Math exam at 2PM tomorrow" → start_time_text: "2PM tomorrow"
        - "Meeting next Friday from 3-4PM" → start: "next Friday 3PM", end: "next Friday 4PM"
        - "Dentist appointment" → missing_info: ["date", "time"]

        Be thorough in extracting time information and identifying what's missing.
        """
    
    async def process_event_with_feedback(
        self, 
        user_input: UserInput
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process event with visual feedback following mermaid diagram.
        """
        try:
            # Step 1: Event identified (already done by orchestrator)
            
            # Step 2: Extracting date/time animation
            yield {
                "step": "extracting_datetime",
                "message": "🕐 Extracting date/time...",
                "animation": "clock_animation",
                "timestamp": datetime.now().isoformat()
            }
            
            # Extract event details using AI
            event_data = await self.extract_event_details(user_input)
            
            # Step 3: Check if we have complete datetime info
            yield {
                "step": "checking_datetime",
                "message": "📆 Checking datetime completeness...",
                "animation": "info_check",
                "timestamp": datetime.now().isoformat()
            }
            
            if not event_data.has_complete_datetime:
                # Step 4a: Need more info
                yield {
                    "step": "need_more_info",
                    "message": "❓ Need more info...",
                    "animation": "input_prompt_slide",
                    "missing_info": event_data.missing_info,
                    "timestamp": datetime.now().isoformat()
                }
                
                # For now, we'll wait for user response
                # TODO: Implement interactive flow for missing information
                yield {
                    "step": "waiting_for_response",
                    "message": "⏳ Waiting for response...",
                    "animation": "breathing_animation",
                    "prompt": f"Please provide: {', '.join(event_data.missing_info)}",
                    "timestamp": datetime.now().isoformat()
                }
                
                # For demo purposes, we'll create a basic event
                start_time = datetime.now() + timedelta(hours=1)  # Default to 1 hour from now
            else:
                # Parse the datetime information
                start_time = await self._parse_datetime(event_data.start_time_text)
                if not start_time:
                    start_time = datetime.now() + timedelta(hours=1)
            
            # Step 4b: Adding to calendar
            yield {
                "step": "adding_to_calendar",
                "message": "📅 Adding to calendar...",
                "animation": "calendar_drop",
                "timestamp": datetime.now().isoformat()
            }
            
            # Parse end time if available
            end_time = None
            if event_data.end_time_text:
                end_time = await self._parse_datetime(event_data.end_time_text)
            
            # Create event object
            event = Event(
                title=event_data.title,
                description=event_data.description,
                start_time=start_time,
                end_time=end_time,
                location=event_data.location,
                created_at=datetime.now()
            )
            
            # Step 5: Syncing calendar animation
            yield {
                "step": "syncing_calendar",
                "message": "🔄 Syncing calendar...",
                "animation": "sync_animation",
                "timestamp": datetime.now().isoformat()
            }
            
            # TODO: Save to database using calendar tool
            
            # Step 6: Success animation
            yield {
                "step": "event_scheduled",
                "message": "✅ Event scheduled!",
                "animation": "success_sparkle",
                "event": event.dict(),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in event processing: {e}")
            yield {
                "step": "error",
                "message": f"❌ Error processing event: {str(e)}",
                "animation": "shake_animation",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _parse_datetime(self, datetime_text: str) -> Optional[datetime]:
        """Parse natural language datetime references."""
        if not datetime_text:
            return None
            
        datetime_text = datetime_text.lower().strip()
        now = datetime.now()
        
        # Simple datetime parsing - can be enhanced with more sophisticated NLP
        if "tomorrow" in datetime_text:
            base_date = now + timedelta(days=1)
        elif "next week" in datetime_text:
            base_date = now + timedelta(weeks=1)
        elif "monday" in datetime_text:
            days_ahead = 0 - now.weekday()
            if days_ahead <= 0:
                days_ahead += 7
            base_date = now + timedelta(days=days_ahead)
        elif "friday" in datetime_text:
            days_ahead = 4 - now.weekday()
            if days_ahead <= 0:
                days_ahead += 7
            base_date = now + timedelta(days=days_ahead)
        else:
            base_date = now
        
        # Extract time if mentioned
        time_match = re.search(r'(\d{1,2})\s*(am|pm|AM|PM)', datetime_text)
        if time_match:
            hour = int(time_match.group(1))
            period = time_match.group(2).lower()
            
            if period == 'pm' and hour != 12:
                hour += 12
            elif period == 'am' and hour == 12:
                hour = 0
            
            return base_date.replace(hour=hour, minute=0, second=0, microsecond=0)
        
        # Default to current time if no specific time mentioned
        return base_date
