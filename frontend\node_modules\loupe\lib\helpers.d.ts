import type { Inspect, Options } from './types.js';
export declare const truncator = "\u2026";
export declare function normaliseOptions({ showHidden, depth, colors, customInspect, showProxy, maxArrayLength, breakLength, seen, truncate, stylize, }: Partial<Options> | undefined, inspect: Inspect): Options;
export declare function truncate(string: string | number, length: number, tail?: typeof truncator): string;
export declare function inspectList(list: ArrayLike<unknown>, options: Options, inspectItem?: Inspect, separator?: string): string;
export declare function inspectProperty([key, value]: [unknown, unknown], options: Options): string;
//# sourceMappingURL=helpers.d.ts.map