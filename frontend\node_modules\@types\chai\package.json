{"name": "@types/chai", "version": "5.2.2", "description": "TypeScript definitions for chai", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/chai", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "Bartvds", "url": "https://github.com/Bartvds"}, {"name": "<PERSON>", "githubUsername": "AGBrown", "url": "https://github.com/AGBrown"}, {"name": "<PERSON>", "githubUsername": "olivr70", "url": "https://github.com/olivr70"}, {"name": "<PERSON>", "githubUsername": "mwistrand", "url": "https://github.com/mwistrand"}, {"name": "<PERSON>", "githubUsername": "s<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/shaunluttin"}, {"name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>", "url": "https://github.com/micksatana"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Erik<PERSON>boom"}, {"name": "<PERSON>gdan <PERSON>", "githubUsername": "b<PERSON>an", "url": "https://github.com/bparan"}, {"name": "CXuesong", "githubUsername": "CXuesong", "url": "https://github.com/CXuesong"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/joe<PERSON><PERSON>patrick"}], "type": "module", "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/chai"}, "scripts": {}, "dependencies": {"@types/deep-eql": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "174d4205d8ad5de1f59dc1f4c1ca29f9ec7d12d905cd5eb02cd537b1e90e8a74", "typeScriptVersion": "5.1"}