"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @internal
 */
const inverted = Symbol('inverted');
/**
 * @internal
 */
const expectNull = Symbol('expectNull');
/**
 * @internal
 */
const expectUndefined = Symbol('expectUndefined');
/**
 * @internal
 */
const expectNumber = Symbol('expectNumber');
/**
 * @internal
 */
const expectString = Symbol('expectString');
/**
 * @internal
 */
const expectBoolean = Symbol('expectBoolean');
/**
 * @internal
 */
const expectVoid = Symbol('expectVoid');
/**
 * @internal
 */
const expectFunction = Symbol('expectFunction');
/**
 * @internal
 */
const expectObject = Symbol('expectObject');
/**
 * @internal
 */
const expectArray = Symbol('expectArray');
/**
 * @internal
 */
const expectSymbol = Symbol('expectSymbol');
/**
 * @internal
 */
const expectAny = Symbol('expectAny');
/**
 * @internal
 */
const expectUnknown = Symbol('expectUnknown');
/**
 * @internal
 */
const expectNever = Symbol('expectNever');
/**
 * @internal
 */
const expectNullable = Symbol('expectNullable');
/**
 * @internal
 */
const expectBigInt = Symbol('expectBigInt');
