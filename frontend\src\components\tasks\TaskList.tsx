import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckSquare, Clock, Calendar, Flag } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

import { Task, TaskPriority } from '@/types';
import { useTasksStore } from '@/stores/tasksStore';

interface TaskListProps {
  tasks: Task[];
  loading: boolean;
}

const TaskList: React.FC<TaskListProps> = ({ tasks, loading }) => {
  const { updateTaskAsync } = useTasksStore();

  const getPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-400 bg-red-400/20';
      case 'high':
        return 'text-orange-400 bg-orange-400/20';
      case 'medium':
        return 'text-yellow-400 bg-yellow-400/20';
      case 'low':
        return 'text-green-400 bg-green-400/20';
      default:
        return 'text-primary-400 bg-primary-400/20';
    }
  };

  const getPriorityIcon = (priority: TaskPriority) => {
    switch (priority) {
      case 'urgent':
        return '🔥';
      case 'high':
        return '⚡';
      case 'medium':
        return '📋';
      case 'low':
        return '🌱';
      default:
        return '📋';
    }
  };

  const handleToggleComplete = async (task: Task) => {
    try {
      await updateTaskAsync(task.id!, { completed: !task.completed });
    } catch (error) {
      console.error('Failed to toggle task completion:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="card animate-pulse">
            <div className="flex items-center gap-4">
              <div className="w-5 h-5 bg-primary-600 rounded"></div>
              <div className="flex-1">
                <div className="h-4 bg-primary-600 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-primary-700 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <motion.div
        className="card text-center py-16"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <CheckSquare className="w-16 h-16 text-primary-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-primary-200 mb-2">
          No tasks found
        </h3>
        <p className="text-primary-400">
          Create your first task or adjust your filters
        </p>
      </motion.div>
    );
  }

  return (
    <div className="space-y-4">
      <AnimatePresence>
        {tasks.map((task, index) => (
          <motion.div
            key={task.id}
            className={`card-hover ${task.completed ? 'opacity-60' : ''}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ delay: index * 0.05 }}
            layout
          >
            <div className="flex items-start gap-4">
              {/* Completion Checkbox */}
              <motion.button
                className={`mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                  task.completed
                    ? 'bg-success border-success text-white'
                    : 'border-primary-500 hover:border-success'
                }`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => handleToggleComplete(task)}
              >
                {task.completed && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  >
                    <CheckSquare className="w-3 h-3" />
                  </motion.div>
                )}
              </motion.button>

              {/* Task Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1">
                    <h3 className={`font-medium ${
                      task.completed 
                        ? 'text-primary-400 line-through' 
                        : 'text-primary-100'
                    }`}>
                      {task.title}
                    </h3>
                    
                    {task.description && (
                      <p className="text-sm text-primary-400 mt-1">
                        {task.description}
                      </p>
                    )}

                    {/* Metadata */}
                    <div className="flex items-center gap-4 mt-3">
                      {/* Priority */}
                      <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${getPriorityColor(task.priority)}`}>
                        <span>{getPriorityIcon(task.priority)}</span>
                        <span className="capitalize">{task.priority}</span>
                      </div>

                      {/* Category */}
                      {task.ai_generated_category && (
                        <div className="flex items-center gap-1 px-2 py-1 bg-primary-600/20 text-primary-300 rounded-full text-xs">
                          <span>🏷️</span>
                          <span>{task.ai_generated_category}</span>
                        </div>
                      )}

                      {/* Due Date */}
                      {task.due_date && (
                        <div className="flex items-center gap-1 text-xs text-primary-400">
                          <Calendar className="w-3 h-3" />
                          <span>
                            Due {formatDistanceToNow(new Date(task.due_date), { addSuffix: true })}
                          </span>
                        </div>
                      )}

                      {/* Created Date */}
                      <div className="flex items-center gap-1 text-xs text-primary-500">
                        <Clock className="w-3 h-3" />
                        <span>
                          {formatDistanceToNow(new Date(task.created_at), { addSuffix: true })}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Priority Flag */}
                  {(task.priority === 'urgent' || task.priority === 'high') && (
                    <motion.div
                      className={`${getPriorityColor(task.priority)} p-1 rounded`}
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <Flag className="w-4 h-4" />
                    </motion.div>
                  )}
                </div>
              </div>
            </div>

            {/* Hover Effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-primary-600/0 to-primary-500/5 rounded-xl opacity-0 pointer-events-none"
              whileHover={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default TaskList;
