import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, Plus, ChevronLeft, ChevronRight } from 'lucide-react';

import { useEventsStore } from '@/stores/eventsStore';
import EventList from './EventList';
import EventForm from './EventForm';
import CalendarView from './CalendarView';

const CalendarPage: React.FC = () => {
  const [showEventForm, setShowEventForm] = useState(false);
  const [currentDate, setCurrentDate] = useState(new Date());

  const {
    events,
    viewMode,
    loading,
    error,
    setViewMode,
    fetchEvents,
  } = useEventsStore();

  useEffect(() => {
    // Fetch events for current month
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    fetchEvents(startOfMonth, endOfMonth);
  }, [currentDate, fetchEvents]);

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Calendar className="w-8 h-8 text-info" />
          <div>
            <h1 className="text-3xl font-bold text-white">Calendar</h1>
            <p className="text-primary-300">AI-powered event scheduling</p>
          </div>
        </div>

        <motion.button
          className="btn-primary flex items-center gap-2"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setShowEventForm(true)}
        >
          <Plus className="w-5 h-5" />
          Add Event
        </motion.button>
      </div>

      {/* Calendar Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* Month Navigation */}
          <div className="flex items-center gap-2">
            <motion.button
              className="p-2 hover:bg-primary-800 rounded-lg transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => navigateMonth('prev')}
            >
              <ChevronLeft className="w-5 h-5 text-primary-300" />
            </motion.button>

            <h2 className="text-xl font-semibold text-primary-200 min-w-[200px] text-center">
              {currentDate.toLocaleDateString('en-US', {
                month: 'long',
                year: 'numeric'
              })}
            </h2>

            <motion.button
              className="p-2 hover:bg-primary-800 rounded-lg transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => navigateMonth('next')}
            >
              <ChevronRight className="w-5 h-5 text-primary-300" />
            </motion.button>
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex bg-primary-800 rounded-lg p-1">
          {(['month', 'week', 'day'] as const).map((mode) => (
            <motion.button
              key={mode}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                viewMode === mode
                  ? 'bg-primary-600 text-white'
                  : 'text-primary-300 hover:text-white'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setViewMode(mode)}
            >
              {mode.charAt(0).toUpperCase() + mode.slice(1)}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Error Display */}
      <AnimatePresence>
        {error && (
          <motion.div
            className="bg-error/10 border border-error/20 rounded-lg p-4"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <p className="text-error">{error}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Calendar Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Calendar View */}
        <div className="lg:col-span-3">
          <CalendarView
            currentDate={currentDate}
            events={events}
            viewMode={viewMode}
            loading={loading}
          />
        </div>

        {/* Events Sidebar */}
        <div className="lg:col-span-1">
          <EventList
            events={events}
            selectedDate={currentDate}
            loading={loading}
          />
        </div>
      </div>

      {/* Event Form Modal */}
      <AnimatePresence>
        {showEventForm && (
          <EventForm
            onClose={() => setShowEventForm(false)}
            onSubmit={() => {
              setShowEventForm(false);
              // Refresh events
              const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
              const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
              fetchEvents(startOfMonth, endOfMonth);
            }}
          />
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default CalendarPage;
