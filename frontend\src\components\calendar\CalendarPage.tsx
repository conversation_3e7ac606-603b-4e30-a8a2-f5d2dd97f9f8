import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, Plus } from 'lucide-react';

const CalendarPage: React.FC = () => {
  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Calendar className="w-8 h-8 text-info" />
          <div>
            <h1 className="text-3xl font-bold text-white">Calendar</h1>
            <p className="text-primary-300">AI-powered event scheduling</p>
          </div>
        </div>
        
        <motion.button
          className="btn-primary flex items-center gap-2"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Plus className="w-5 h-5" />
          Add Event
        </motion.button>
      </div>

      {/* Coming Soon */}
      <div className="card text-center py-16">
        <Calendar className="w-16 h-16 text-primary-400 mx-auto mb-4" />
        <h2 className="text-2xl font-semibold text-primary-200 mb-2">
          Intelligent Calendar
        </h2>
        <p className="text-primary-300 max-w-md mx-auto">
          Natural language event creation with automatic conflict detection, 
          smart scheduling suggestions, and seamless integration.
        </p>
        <div className="mt-6">
          <span className="px-4 py-2 bg-primary-600/20 text-primary-300 rounded-full text-sm">
            Coming Soon
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default CalendarPage;
