{"name": "ai-powered-dashboard", "version": "1.0.0", "description": "AI-powered dashboard with visual transparency and smooth animations", "private": true, "scripts": {"dev": "docker-compose up --build", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", "build": "docker-compose build", "start": "docker-compose up", "stop": "docker-compose down", "clean": "docker-compose down -v --remove-orphans", "logs": "docker-compose logs -f", "logs:frontend": "docker-compose logs -f frontend", "logs:backend": "docker-compose logs -f backend", "logs:ollama": "docker-compose logs -f ollama", "setup": "cp .env.example .env && echo 'Please edit .env with your API keys'", "test": "cd frontend && npm test && cd ../backend && pytest", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && pytest", "lint": "cd frontend && npm run lint && cd ../backend && ruff check app/", "lint:fix": "cd frontend && npm run lint:fix && cd ../backend && ruff check --fix app/ && black app/", "type-check": "cd frontend && npm run type-check && cd ../backend && mypy app/", "format": "cd frontend && npm run format && cd ../backend && black app/ && isort app/", "health": "curl -f http://localhost:8000/health && curl -f http://localhost:3000", "ollama:pull": "docker-compose exec ollama ollama pull nomic-embed-text"}, "keywords": ["ai", "dashboard", "react", "<PERSON><PERSON><PERSON>", "mirascope", "framer-motion", "tailwindcss"], "author": "AI Dashboard Team", "license": "MIT"}