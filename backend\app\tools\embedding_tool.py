"""Ollama embeddings tool for semantic search."""

import logging
from typing import List, Dict, Any, Optional
import httpx
import json
import numpy as np
from datetime import datetime

from mirascope import BaseTool
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.models.database import EmbeddingModel
from app.database.connection import get_async_session
from app.config import settings

logger = logging.getLogger(__name__)


class EmbeddingTool(BaseTool):
    """Mirascope tool for Ollama embeddings and semantic search."""
    
    def __init__(self):
        self.ollama_url = settings.ollama_url
        self.model_name = settings.ollama_model
        self.timeout = 30
    
    async def generate_embedding(self, text: str) -> Dict[str, Any]:
        """
        Generate embedding for text using Ollama.
        
        Args:
            text: Text to generate embedding for
            
        Returns:
            Dictionary with embedding vector and metadata
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                payload = {
                    "model": self.model_name,
                    "prompt": text
                }
                
                response = await client.post(
                    f"{self.ollama_url}/api/embeddings",
                    json=payload
                )
                
                if response.status_code == 200:
                    data = response.json()
                    embedding = data.get("embedding", [])
                    
                    return {
                        "success": True,
                        "embedding": embedding,
                        "model": self.model_name,
                        "text_length": len(text),
                        "embedding_dimension": len(embedding)
                    }
                else:
                    logger.error(f"Ollama embedding error: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "error": f"Ollama API error: {response.status_code}"
                    }
                    
        except httpx.TimeoutException:
            logger.error(f"Timeout generating embedding for text: {text[:100]}...")
            return {
                "success": False,
                "error": "Embedding generation timeout"
            }
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def store_embedding(
        self,
        content: str,
        content_type: str,
        content_id: int,
        embedding: Optional[List[float]] = None
    ) -> Dict[str, Any]:
        """
        Store embedding in database.
        
        Args:
            content: Original content text
            content_type: Type of content ('task', 'event', 'input')
            content_id: ID of the content in its respective table
            embedding: Pre-computed embedding (optional)
            
        Returns:
            Dictionary with storage result
        """
        try:
            # Generate embedding if not provided
            if embedding is None:
                embed_result = await self.generate_embedding(content)
                if not embed_result.get("success"):
                    return embed_result
                embedding = embed_result["embedding"]
            
            async for session in get_async_session():
                # Check if embedding already exists
                existing = await session.execute(
                    select(EmbeddingModel).where(
                        and_(
                            EmbeddingModel.content_type == content_type,
                            EmbeddingModel.content_id == content_id
                        )
                    )
                )
                existing_embedding = existing.scalar_one_or_none()
                
                if existing_embedding:
                    # Update existing embedding
                    existing_embedding.content = content
                    existing_embedding.embedding = embedding
                    existing_embedding.model_name = self.model_name
                    await session.commit()
                    
                    return {
                        "success": True,
                        "action": "updated",
                        "embedding_id": existing_embedding.id
                    }
                else:
                    # Create new embedding
                    new_embedding = EmbeddingModel(
                        content=content,
                        content_type=content_type,
                        content_id=content_id,
                        embedding=embedding,
                        model_name=self.model_name
                    )
                    
                    session.add(new_embedding)
                    await session.commit()
                    await session.refresh(new_embedding)
                    
                    return {
                        "success": True,
                        "action": "created",
                        "embedding_id": new_embedding.id
                    }
                    
        except Exception as e:
            logger.error(f"Error storing embedding: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def semantic_search(
        self,
        query: str,
        content_types: Optional[List[str]] = None,
        limit: int = 10,
        similarity_threshold: float = 0.5
    ) -> Dict[str, Any]:
        """
        Perform semantic search using embeddings.
        
        Args:
            query: Search query
            content_types: Optional filter by content types
            limit: Maximum number of results
            similarity_threshold: Minimum similarity score
            
        Returns:
            Dictionary with search results
        """
        try:
            # Generate embedding for query
            query_result = await self.generate_embedding(query)
            if not query_result.get("success"):
                return query_result
            
            query_embedding = np.array(query_result["embedding"])
            
            async for session in get_async_session():
                # Get all embeddings (with optional content type filter)
                query_db = select(EmbeddingModel)
                if content_types:
                    query_db = query_db.where(EmbeddingModel.content_type.in_(content_types))
                
                result = await session.execute(query_db)
                embeddings = result.scalars().all()
                
                # Calculate similarities
                similarities = []
                for embedding_record in embeddings:
                    stored_embedding = np.array(embedding_record.embedding)
                    
                    # Calculate cosine similarity
                    similarity = np.dot(query_embedding, stored_embedding) / (
                        np.linalg.norm(query_embedding) * np.linalg.norm(stored_embedding)
                    )
                    
                    if similarity >= similarity_threshold:
                        similarities.append({
                            "embedding_id": embedding_record.id,
                            "content": embedding_record.content,
                            "content_type": embedding_record.content_type,
                            "content_id": embedding_record.content_id,
                            "similarity": float(similarity),
                            "created_at": embedding_record.created_at.isoformat()
                        })
                
                # Sort by similarity and limit results
                similarities.sort(key=lambda x: x["similarity"], reverse=True)
                results = similarities[:limit]
                
                return {
                    "success": True,
                    "query": query,
                    "results": results,
                    "total_results": len(results),
                    "searched_embeddings": len(embeddings),
                    "similarity_threshold": similarity_threshold
                }
                
        except Exception as e:
            logger.error(f"Error in semantic search: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    async def batch_generate_embeddings(
        self,
        texts: List[str],
        batch_size: int = 10
    ) -> Dict[str, Any]:
        """
        Generate embeddings for multiple texts in batches.
        
        Args:
            texts: List of texts to generate embeddings for
            batch_size: Number of texts to process at once
            
        Returns:
            Dictionary with batch results
        """
        try:
            results = []
            errors = []
            
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                
                for text in batch:
                    result = await self.generate_embedding(text)
                    if result.get("success"):
                        results.append({
                            "text": text,
                            "embedding": result["embedding"],
                            "index": i + batch.index(text)
                        })
                    else:
                        errors.append({
                            "text": text,
                            "error": result.get("error"),
                            "index": i + batch.index(text)
                        })
            
            return {
                "success": len(results) > 0,
                "results": results,
                "errors": errors,
                "total_processed": len(results) + len(errors),
                "success_rate": len(results) / len(texts) if texts else 0
            }
            
        except Exception as e:
            logger.error(f"Error in batch embedding generation: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": [],
                "errors": []
            }
    
    async def check_model_status(self) -> Dict[str, Any]:
        """
        Check if Ollama model is available and ready.
        
        Returns:
            Dictionary with model status
        """
        try:
            async with httpx.AsyncClient(timeout=10) as client:
                # Check if Ollama is running
                response = await client.get(f"{self.ollama_url}/api/tags")
                
                if response.status_code == 200:
                    data = response.json()
                    models = data.get("models", [])
                    
                    # Check if our model is available
                    model_available = any(
                        model.get("name", "").startswith(self.model_name)
                        for model in models
                    )
                    
                    return {
                        "success": True,
                        "ollama_running": True,
                        "model_available": model_available,
                        "model_name": self.model_name,
                        "available_models": [m.get("name") for m in models]
                    }
                else:
                    return {
                        "success": False,
                        "ollama_running": False,
                        "error": f"Ollama API error: {response.status_code}"
                    }
                    
        except Exception as e:
            logger.error(f"Error checking model status: {e}")
            return {
                "success": False,
                "ollama_running": False,
                "error": str(e)
            }
