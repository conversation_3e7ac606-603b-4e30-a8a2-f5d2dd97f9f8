import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { DashboardState, ProcessingUpdate } from '@/types';

interface DashboardStore extends DashboardState {
  // Actions
  setProcessing: (isProcessing: boolean) => void;
  setCurrentStep: (step: string) => void;
  addProcessingStep: (step: ProcessingUpdate) => void;
  setLastResult: (result: any) => void;
  setError: (error: string | null) => void;
  clearProcessingSteps: () => void;
  reset: () => void;
}

const initialState: DashboardState = {
  isProcessing: false,
  currentStep: '',
  processingSteps: [],
  lastResult: undefined,
  error: undefined,
};

export const useDashboardStore = create<DashboardStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    // Actions
    setProcessing: (isProcessing: boolean) => 
      set({ isProcessing }),

    setCurrentStep: (step: string) => 
      set({ currentStep: step }),

    addProcessingStep: (step: ProcessingUpdate) => 
      set((state) => ({
        processingSteps: [...state.processingSteps, step],
        currentStep: step.step,
      })),

    setLastResult: (result: any) => 
      set({ lastResult: result, isProcessing: false }),

    setError: (error: string | null) => 
      set({ error, isProcessing: false }),

    clearProcessingSteps: () => 
      set({ processingSteps: [], currentStep: '' }),

    reset: () => 
      set(initialState),
  }))
);

// Selectors
export const selectIsProcessing = (state: DashboardStore) => state.isProcessing;
export const selectCurrentStep = (state: DashboardStore) => state.currentStep;
export const selectProcessingSteps = (state: DashboardStore) => state.processingSteps;
export const selectLastResult = (state: DashboardStore) => state.lastResult;
export const selectError = (state: DashboardStore) => state.error;
