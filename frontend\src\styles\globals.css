@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply bg-primary-900 text-primary-50 antialiased;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-primary-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-primary-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-500;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-primary-900;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-primary-700 text-primary-100 hover:bg-primary-600 focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn bg-transparent text-primary-300 hover:bg-primary-800 hover:text-primary-100 focus:ring-primary-500;
  }
  
  .btn-danger {
    @apply btn bg-error text-white hover:bg-red-600 focus:ring-error;
  }
  
  /* Input styles */
  .input {
    @apply w-full px-4 py-3 bg-primary-800 border border-primary-700 rounded-lg text-primary-100 placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }
  
  .input:focus {
    @apply shadow-lg shadow-primary-500/20;
  }
  
  /* Card styles */
  .card {
    @apply bg-primary-800/50 backdrop-blur-sm border border-primary-700/50 rounded-xl p-6 shadow-lg;
  }
  
  .card-hover {
    @apply card hover:bg-primary-800/70 hover:border-primary-600/50 transition-all duration-300 cursor-pointer;
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-bounce-gentle {
    animation: bounceGentle 0.6s ease-in-out;
  }
  
  /* Glass morphism effect */
  .glass {
    @apply bg-white/5 backdrop-blur-md border border-white/10;
  }
  
  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-200 to-primary-400 bg-clip-text text-transparent;
  }
  
  /* Loading states */
  .loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
  }
  
  /* Focus visible for accessibility */
  .focus-visible {
    @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-primary-900;
  }
}

/* Utility styles */
@layer utilities {
  /* Animation delays */
  .delay-100 { animation-delay: 100ms; }
  .delay-200 { animation-delay: 200ms; }
  .delay-300 { animation-delay: 300ms; }
  .delay-400 { animation-delay: 400ms; }
  .delay-500 { animation-delay: 500ms; }
  
  /* Transform GPU acceleration */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in,
    .animate-slide-up,
    .animate-bounce-gentle,
    .animate-pulse-gentle,
    .animate-typing,
    .animate-brain,
    .animate-confetti,
    .animate-shake,
    .animate-glow {
      animation: none;
    }
    
    * {
      transition-duration: 0.01ms !important;
      animation-duration: 0.01ms !important;
    }
  }
}

/* Custom keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes bounceGentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes loadingDots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
