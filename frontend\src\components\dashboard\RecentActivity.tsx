import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Clock, CheckSquare, Calendar, Brain, ArrowRight } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface Activity {
  id: number;
  input: string;
  timestamp: string;
  type: 'input' | 'task' | 'event' | 'question';
  category?: string;
  result?: any;
}

interface RecentActivityProps {
  activities: Activity[];
}

const RecentActivity: React.FC<RecentActivityProps> = ({ activities }) => {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'task':
        return CheckSquare;
      case 'event':
        return Calendar;
      case 'question':
        return Brain;
      default:
        return Clock;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'task':
        return 'text-success';
      case 'event':
        return 'text-info';
      case 'question':
        return 'text-purple-400';
      default:
        return 'text-primary-400';
    }
  };

  const getActivityBgColor = (type: string) => {
    switch (type) {
      case 'task':
        return 'bg-success/20';
      case 'event':
        return 'bg-info/20';
      case 'question':
        return 'bg-purple-600/20';
      default:
        return 'bg-primary-600/20';
    }
  };

  if (activities.length === 0) {
    return (
      <div className="card">
        <h2 className="text-xl font-semibold text-primary-200 mb-6">Recent Activity</h2>
        <motion.div
          className="text-center py-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <Clock className="w-12 h-12 text-primary-400 mx-auto mb-4" />
          <p className="text-primary-300 mb-2">No recent activity</p>
          <p className="text-sm text-primary-400">
            Start by entering something in the input bar above
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-primary-200">Recent Activity</h2>
        <motion.button
          className="text-sm text-primary-400 hover:text-primary-300 flex items-center gap-1"
          whileHover={{ x: 5 }}
        >
          View All
          <ArrowRight className="w-4 h-4" />
        </motion.button>
      </div>

      <div className="space-y-4">
        <AnimatePresence>
          {activities.slice(0, 8).map((activity, index) => {
            const Icon = getActivityIcon(activity.type);
            const iconColor = getActivityColor(activity.type);
            const bgColor = getActivityBgColor(activity.type);

            return (
              <motion.div
                key={activity.id}
                className="flex items-start gap-4 p-4 bg-primary-800/30 rounded-lg hover:bg-primary-800/50 transition-colors cursor-pointer"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ 
                  delay: index * 0.05,
                  type: "spring",
                  stiffness: 300,
                  damping: 30
                }}
                whileHover={{ scale: 1.02 }}
                layout
              >
                {/* Icon */}
                <motion.div
                  className={`p-2 rounded-lg ${bgColor} flex-shrink-0`}
                  whileHover={{ scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <Icon className={`w-4 h-4 ${iconColor}`} />
                </motion.div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1">
                      <p className="text-primary-200 font-medium truncate">
                        {activity.input}
                      </p>
                      
                      {activity.category && (
                        <motion.span
                          className="inline-block mt-1 px-2 py-1 text-xs bg-primary-700/50 text-primary-300 rounded-full"
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.05 + 0.1 }}
                        >
                          {activity.category}
                        </motion.span>
                      )}
                    </div>

                    <div className="text-xs text-primary-400 flex-shrink-0">
                      {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                    </div>
                  </div>

                  {/* Result preview */}
                  {activity.result && (
                    <motion.div
                      className="mt-2 text-sm text-primary-400"
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      transition={{ delay: index * 0.05 + 0.2 }}
                    >
                      <div className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-success rounded-full" />
                        <span>Processed successfully</span>
                      </div>
                    </motion.div>
                  )}
                </div>

                {/* Hover indicator */}
                <motion.div
                  className="w-2 h-2 bg-primary-500 rounded-full opacity-0"
                  whileHover={{ opacity: 1, scale: 1.5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                />
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Load more button */}
      {activities.length > 8 && (
        <motion.div
          className="mt-6 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <motion.button
            className="text-sm text-primary-400 hover:text-primary-300 px-4 py-2 rounded-lg hover:bg-primary-800/30 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Load More ({activities.length - 8} remaining)
          </motion.button>
        </motion.div>
      )}
    </div>
  );
};

export default RecentActivity;
