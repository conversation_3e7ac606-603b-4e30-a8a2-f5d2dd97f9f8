import React from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { X, Save } from 'lucide-react';

import { TaskFormData, TaskPriority } from '@/types';
import { useTasksStore } from '@/stores/tasksStore';

interface TaskFormProps {
  onClose: () => void;
  onSubmit: () => void;
}

const TaskForm: React.FC<TaskFormProps> = ({ onClose, onSubmit }) => {
  const { createTask } = useTasksStore();
  
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<TaskFormData>({
    defaultValues: {
      title: '',
      description: '',
      priority: 'medium',
    }
  });

  const onFormSubmit = async (data: TaskFormData) => {
    try {
      await createTask({
        title: data.title,
        description: data.description || undefined,
        priority: data.priority,
        due_date: data.due_date ? new Date(data.due_date).toISOString() : undefined,
        completed: false,
      });
      onSubmit();
    } catch (error) {
      console.error('Failed to create task:', error);
    }
  };

  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="bg-primary-800 rounded-2xl p-6 w-full max-w-md"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Create New Task</h2>
          <motion.button
            className="p-2 hover:bg-primary-700 rounded-lg transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onClose}
          >
            <X className="w-5 h-5 text-primary-300" />
          </motion.button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-primary-300 mb-2">
              Title *
            </label>
            <input
              {...register('title', { 
                required: 'Title is required',
                minLength: { value: 1, message: 'Title cannot be empty' }
              })}
              className="input"
              placeholder="Enter task title..."
              autoFocus
            />
            {errors.title && (
              <p className="text-error text-sm mt-1">{errors.title.message}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-primary-300 mb-2">
              Description
            </label>
            <textarea
              {...register('description')}
              className="input resize-none"
              rows={3}
              placeholder="Optional description..."
            />
          </div>

          {/* Priority */}
          <div>
            <label className="block text-sm font-medium text-primary-300 mb-2">
              Priority
            </label>
            <select {...register('priority')} className="input">
              <option value="low">🌱 Low</option>
              <option value="medium">📋 Medium</option>
              <option value="high">⚡ High</option>
              <option value="urgent">🔥 Urgent</option>
            </select>
          </div>

          {/* Due Date */}
          <div>
            <label className="block text-sm font-medium text-primary-300 mb-2">
              Due Date
            </label>
            <input
              {...register('due_date')}
              type="datetime-local"
              className="input"
            />
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <motion.button
              type="button"
              className="btn-ghost flex-1"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </motion.button>
            
            <motion.button
              type="submit"
              className="btn-primary flex-1 flex items-center justify-center gap-2"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Create Task
                </>
              )}
            </motion.button>
          </div>
        </form>
      </motion.div>
    </motion.div>
  );
};

export default TaskForm;
