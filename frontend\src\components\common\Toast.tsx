import React, { createContext, useContext, useState, useC<PERSON>back, ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';

export interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  persistent?: boolean;
}

interface ToastContextType {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

interface ToastProviderProps {
  children: ReactNode;
  maxToasts?: number;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ 
  children, 
  maxToasts = 5 
}) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const addToast = useCallback((toast: Omit<Toast, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast: Toast = {
      ...toast,
      id,
      duration: toast.duration ?? 5000,
    };

    setToasts(prev => {
      const updated = [newToast, ...prev];
      return updated.slice(0, maxToasts);
    });

    // Auto-remove toast after duration (unless persistent)
    if (!newToast.persistent && newToast.duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, newToast.duration);
    }
  }, [maxToasts]);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const clearToasts = useCallback(() => {
    setToasts([]);
  }, []);

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast, clearToasts }}>
      {children}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </ToastContext.Provider>
  );
};

interface ToastContainerProps {
  toasts: Toast[];
  onRemove: (id: string) => void;
}

const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onRemove }) => {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      <AnimatePresence>
        {toasts.map((toast) => (
          <ToastItem key={toast.id} toast={toast} onRemove={onRemove} />
        ))}
      </AnimatePresence>
    </div>
  );
};

interface ToastItemProps {
  toast: Toast;
  onRemove: (id: string) => void;
}

const ToastItem: React.FC<ToastItemProps> = ({ toast, onRemove }) => {
  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return CheckCircle;
      case 'error':
        return AlertCircle;
      case 'warning':
        return AlertTriangle;
      case 'info':
        return Info;
    }
  };

  const getColors = () => {
    switch (toast.type) {
      case 'success':
        return {
          bg: 'bg-success/90',
          border: 'border-success',
          text: 'text-white',
          icon: 'text-white',
        };
      case 'error':
        return {
          bg: 'bg-error/90',
          border: 'border-error',
          text: 'text-white',
          icon: 'text-white',
        };
      case 'warning':
        return {
          bg: 'bg-warning/90',
          border: 'border-warning',
          text: 'text-black',
          icon: 'text-black',
        };
      case 'info':
        return {
          bg: 'bg-info/90',
          border: 'border-info',
          text: 'text-white',
          icon: 'text-white',
        };
    }
  };

  const Icon = getIcon();
  const colors = getColors();

  return (
    <motion.div
      className={`${colors.bg} ${colors.border} border backdrop-blur-sm rounded-lg shadow-lg p-4 ${colors.text}`}
      initial={{ opacity: 0, x: 300, scale: 0.9 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 300, scale: 0.9 }}
      transition={{ type: "spring", stiffness: 500, damping: 30 }}
      layout
    >
      <div className="flex items-start gap-3">
        {/* Icon */}
        <motion.div
          className={`flex-shrink-0 ${colors.icon}`}
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1, type: "spring", stiffness: 500, damping: 30 }}
        >
          <Icon className="w-5 h-5" />
        </motion.div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {toast.title && (
            <h4 className="font-medium mb-1">
              {toast.title}
            </h4>
          )}
          <p className="text-sm opacity-90">
            {toast.message}
          </p>
        </div>

        {/* Close Button */}
        <motion.button
          className={`flex-shrink-0 ${colors.text} opacity-70 hover:opacity-100 transition-opacity`}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => onRemove(toast.id)}
        >
          <X className="w-4 h-4" />
        </motion.button>
      </div>

      {/* Progress Bar (for non-persistent toasts) */}
      {!toast.persistent && toast.duration && toast.duration > 0 && (
        <motion.div
          className="mt-3 h-1 bg-black/20 rounded-full overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          <motion.div
            className="h-full bg-white/30"
            initial={{ width: '100%' }}
            animate={{ width: '0%' }}
            transition={{ duration: toast.duration / 1000, ease: 'linear' }}
          />
        </motion.div>
      )}
    </motion.div>
  );
};

// Convenience hooks for different toast types
export const useToastHelpers = () => {
  const { addToast } = useToast();

  return {
    success: (message: string, title?: string) => 
      addToast({ type: 'success', message, title }),
    
    error: (message: string, title?: string) => 
      addToast({ type: 'error', message, title, duration: 7000 }),
    
    warning: (message: string, title?: string) => 
      addToast({ type: 'warning', message, title }),
    
    info: (message: string, title?: string) => 
      addToast({ type: 'info', message, title }),
    
    persistent: (type: Toast['type'], message: string, title?: string) => 
      addToast({ type, message, title, persistent: true }),
  };
};

export default ToastProvider;
