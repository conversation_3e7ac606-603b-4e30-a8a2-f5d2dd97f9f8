import formatDistance from "./_lib/formatDistance/index.js";
import formatLong from "./_lib/formatLong/index.js";
import formatRelative from "./_lib/formatRelative/index.js";
import localize from "./_lib/localize/index.js";
import match from "./_lib/match/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary Danish locale.
 * @language Danish
 * @iso-639-2 dan
 * <AUTHOR> [@MathiasK<PERSON>elborg]{@link https://github.com/Mathias<PERSON>andelborg}
 * <AUTHOR> [@Andersbiha]{@link https://github.com/Anders<PERSON>ha}
 * <AUTHOR> https://github.com/kgram}
 * <AUTHOR> https://github.com/stefanbugge}
 */
var locale = {
  code: 'da',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4
  }
};
export default locale;