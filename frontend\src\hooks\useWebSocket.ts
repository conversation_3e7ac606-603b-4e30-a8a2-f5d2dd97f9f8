import { useState, useEffect, useCallback } from 'react';
import webSocketService, { WebSocketServiceOptions } from '@/services/websocket';

interface UseWebSocketOptions extends WebSocketServiceOptions {}

export const useWebSocket = (options: UseWebSocketOptions = {}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');

  // Configure the WebSocket service with options
  useEffect(() => {
    const serviceOptions = {
      ...options,
      onConnect: () => {
        setIsConnected(true);
        setConnectionState('connected');
        options.onConnect?.();
      },
      onDisconnect: () => {
        setIsConnected(false);
        setConnectionState('disconnected');
        options.onDisconnect?.();
      },
      onError: (error: Event) => {
        setConnectionState('error');
        options.onError?.(error);
      },
    };

    // Update the service options
    Object.assign(webSocketService, { options: serviceOptions });
  }, [options]);

  const connect = useCallback(async () => {
    setConnectionState('connecting');
    try {
      await webSocketService.connect();
    } catch (error) {
      setConnectionState('error');
      console.error('Failed to connect WebSocket:', error);
    }
  }, []);

  const disconnect = useCallback(() => {
    webSocketService.disconnect();
    setIsConnected(false);
    setConnectionState('disconnected');
  }, []);

  const sendMessage = useCallback((message: any) => {
    webSocketService.send(message);
  }, []);

  const sendPing = useCallback(() => {
    webSocketService.sendPing();
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    connect();

    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Ping interval to keep connection alive
  useEffect(() => {
    if (!isConnected) return;

    const pingInterval = setInterval(() => {
      sendPing();
    }, 30000); // Ping every 30 seconds

    return () => clearInterval(pingInterval);
  }, [isConnected, sendPing]);

  // Update connection state based on service state
  useEffect(() => {
    const checkConnectionState = () => {
      const state = webSocketService.getConnectionState();
      setConnectionState(state as any);
      setIsConnected(webSocketService.isWebSocketConnected());
    };

    const interval = setInterval(checkConnectionState, 1000);
    return () => clearInterval(interval);
  }, []);

  return {
    isConnected,
    connectionState,
    sendMessage,
    connect,
    disconnect,
    sendPing
  };
};
