import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ToastProvider } from '@/components/common/Toast';

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <BrowserRouter>
      <ToastProvider>
        {children}
      </ToastProvider>
    </BrowserRouter>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };

// Mock data generators
export const mockTask = (overrides = {}) => ({
  id: '1',
  title: 'Test Task',
  description: 'Test Description',
  completed: false,
  priority: 'medium' as const,
  category: 'work',
  ai_generated_category: 'productivity',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  due_date: null,
  ...overrides,
});

export const mockEvent = (overrides = {}) => ({
  id: '1',
  title: 'Test Event',
  description: 'Test Description',
  start_time: '2024-01-01T10:00:00Z',
  end_time: '2024-01-01T11:00:00Z',
  location: 'Test Location',
  ai_generated_category: 'meeting',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const mockUserInput = (overrides = {}) => ({
  text: 'Test input',
  timestamp: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const mockAIResponse = (overrides = {}) => ({
  original_input: 'Test input',
  category: 'task' as const,
  processing_steps: ['Step 1', 'Step 2'],
  result: mockTask(),
  search_results: [],
  sources: [],
  confidence: 0.9,
  content: 'Test response content',
  timestamp: '2024-01-01T00:00:00Z',
  metadata: {},
  ...overrides,
});

export const mockSettings = (overrides = {}) => ({
  default_model: 'test-model',
  backup_model: 'test-backup-model',
  openrouter_api_key: 'test-key',
  langsearch_api_key: 'test-key',
  ollama_url: 'http://localhost:11434',
  ollama_model: 'test-model',
  enable_animations: true,
  reduced_motion: false,
  max_search_results: 10,
  auto_save_interval: 30,
  save_input_history: true,
  enable_analytics: false,
  animation_duration: 300,
  models_available: ['test-model'],
  debug: false,
  ...overrides,
});

// Test utilities
export const waitForLoadingToFinish = () => 
  new Promise(resolve => setTimeout(resolve, 0));

export const createMockApiResponse = <T>(data: T, delay = 0) => 
  new Promise<T>(resolve => setTimeout(() => resolve(data), delay));

export const createMockApiError = (message = 'Test error', delay = 0) => 
  new Promise((_, reject) => 
    setTimeout(() => reject(new Error(message)), delay)
  );

// Mock API service
export const mockApiService = {
  processInput: vi.fn(),
  getTasks: vi.fn(),
  createTask: vi.fn(),
  updateTask: vi.fn(),
  deleteTask: vi.fn(),
  getTaskCategories: vi.fn(),
  getEvents: vi.fn(),
  createEvent: vi.fn(),
  updateEvent: vi.fn(),
  deleteEvent: vi.fn(),
  searchContent: vi.fn(),
  getSettings: vi.fn(),
  updateSettings: vi.fn(),
  healthCheck: vi.fn(),
};

// Mock WebSocket service
export const mockWebSocketService = {
  connect: vi.fn(),
  disconnect: vi.fn(),
  send: vi.fn(),
  sendPing: vi.fn(),
  processInput: vi.fn(),
  getConnectionState: vi.fn(() => 'connected'),
  isWebSocketConnected: vi.fn(() => true),
};

// Mock stores
export const mockTasksStore = {
  tasks: [],
  categories: [],
  filters: {},
  loading: false,
  error: undefined,
  setTasks: vi.fn(),
  addTask: vi.fn(),
  updateTask: vi.fn(),
  removeTask: vi.fn(),
  setCategories: vi.fn(),
  setFilters: vi.fn(),
  setLoading: vi.fn(),
  setError: vi.fn(),
  fetchTasks: vi.fn(),
  createTask: vi.fn(),
  updateTaskAsync: vi.fn(),
  deleteTask: vi.fn(),
  fetchCategories: vi.fn(),
};

export const mockEventsStore = {
  events: [],
  selectedDate: undefined,
  viewMode: 'month' as const,
  loading: false,
  error: undefined,
  setEvents: vi.fn(),
  addEvent: vi.fn(),
  updateEvent: vi.fn(),
  removeEvent: vi.fn(),
  setSelectedDate: vi.fn(),
  setViewMode: vi.fn(),
  setLoading: vi.fn(),
  setError: vi.fn(),
  fetchEvents: vi.fn(),
  createEvent: vi.fn(),
  updateEventAsync: vi.fn(),
  deleteEvent: vi.fn(),
};

export const mockDashboardStore = {
  isProcessing: false,
  currentStep: '',
  processingSteps: [],
  lastResult: undefined,
  error: undefined,
  setProcessing: vi.fn(),
  setCurrentStep: vi.fn(),
  addProcessingStep: vi.fn(),
  setLastResult: vi.fn(),
  setError: vi.fn(),
  clearProcessingSteps: vi.fn(),
  reset: vi.fn(),
};
