{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,MAAM,MAAM,iBAAiB,CAAA;AACpC,OAAO,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAA;AAC9D,OAAO,EAAE,GAAG,EAAe,MAAM,UAAU,CAAA;AAC3C,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AACpC,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAsCxC,MAAM,CAAC,MAAM,SAAS,GAAG,CACvB,CAAS,EACT,OAAe,EACf,UAA4B,EAAE,EAC9B,EAAE;IACF,kBAAkB,CAAC,OAAO,CAAC,CAAA;IAE3B,oCAAoC;IACpC,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACnD,OAAO,KAAK,CAAA;KACb;IAED,OAAO,IAAI,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;AACjD,CAAC,CAAA;AAED,wDAAwD;AACxD,MAAM,YAAY,GAAG,uBAAuB,CAAA;AAC5C,MAAM,cAAc,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,CAAC,CAAS,EAAE,EAAE,CACpD,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AACvC,MAAM,iBAAiB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AACzE,MAAM,oBAAoB,GAAG,CAAC,GAAW,EAAE,EAAE;IAC3C,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAA;IACvB,OAAO,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AAC3E,CAAC,CAAA;AACD,MAAM,uBAAuB,GAAG,CAAC,GAAW,EAAE,EAAE;IAC9C,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAA;IACvB,OAAO,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AACrD,CAAC,CAAA;AACD,MAAM,aAAa,GAAG,YAAY,CAAA;AAClC,MAAM,eAAe,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AAC5E,MAAM,kBAAkB,GAAG,CAAC,CAAS,EAAE,EAAE,CACvC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AAC5C,MAAM,SAAS,GAAG,SAAS,CAAA;AAC3B,MAAM,WAAW,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;AAC/E,MAAM,MAAM,GAAG,OAAO,CAAA;AACtB,MAAM,QAAQ,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;AACpE,MAAM,WAAW,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAA;AAC5E,MAAM,QAAQ,GAAG,wBAAwB,CAAA;AACzC,MAAM,gBAAgB,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,CAAmB,EAAE,EAAE;IAC5D,MAAM,KAAK,GAAG,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACnC,IAAI,CAAC,GAAG;QAAE,OAAO,KAAK,CAAA;IACtB,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAA;IACvB,OAAO,CAAC,CAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AACjE,CAAC,CAAA;AACD,MAAM,mBAAmB,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,CAAmB,EAAE,EAAE;IAC/D,MAAM,KAAK,GAAG,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACtC,IAAI,CAAC,GAAG;QAAE,OAAO,KAAK,CAAA;IACtB,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAA;IACvB,OAAO,CAAC,CAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AACjE,CAAC,CAAA;AACD,MAAM,aAAa,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,CAAmB,EAAE,EAAE;IACzD,MAAM,KAAK,GAAG,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACtC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AAClE,CAAC,CAAA;AACD,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,CAAmB,EAAE,EAAE;IACtD,MAAM,KAAK,GAAG,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACnC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AAClE,CAAC,CAAA;AACD,MAAM,eAAe,GAAG,CAAC,CAAC,EAAE,CAAmB,EAAE,EAAE;IACjD,MAAM,GAAG,GAAG,EAAE,CAAC,MAAM,CAAA;IACrB,OAAO,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;AAC9D,CAAC,CAAA;AACD,MAAM,kBAAkB,GAAG,CAAC,CAAC,EAAE,CAAmB,EAAE,EAAE;IACpD,MAAM,GAAG,GAAG,EAAE,CAAC,MAAM,CAAA;IACrB,OAAO,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAA;AACnE,CAAC,CAAA;AAED,qBAAqB;AACrB,MAAM,eAAe,GAAa,CAChC,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO;IACpC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ;QAC9B,OAAO,CAAC,GAAG;QACX,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;QAC7C,OAAO,CAAC,QAAQ;IAClB,CAAC,CAAC,OAAO,CACA,CAAA;AAEb,MAAM,IAAI,GAAkC;IAC1C,KAAK,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE;IACpB,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;CACpB,CAAA;AACD,oBAAoB;AAEpB,MAAM,CAAC,MAAM,GAAG,GAAG,eAAe,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAA;AAChF,SAAS,CAAC,GAAG,GAAG,GAAG,CAAA;AAEnB,MAAM,CAAC,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,CAAA;AAC7C,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAA;AAE7B,gCAAgC;AAChC,iDAAiD;AACjD,MAAM,KAAK,GAAG,MAAM,CAAA;AAEpB,gCAAgC;AAChC,MAAM,IAAI,GAAG,KAAK,GAAG,IAAI,CAAA;AAEzB,4DAA4D;AAC5D,+DAA+D;AAC/D,6CAA6C;AAC7C,MAAM,UAAU,GAAG,yCAAyC,CAAA;AAE5D,kCAAkC;AAClC,6CAA6C;AAC7C,MAAM,YAAY,GAAG,yBAAyB,CAAA;AAE9C,MAAM,CAAC,MAAM,MAAM,GACjB,CAAC,OAAe,EAAE,UAA4B,EAAE,EAAE,EAAE,CACpD,CAAC,CAAS,EAAE,EAAE,CACZ,SAAS,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;AAClC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAA;AAEzB,MAAM,GAAG,GAAG,CAAC,CAAmB,EAAE,IAAsB,EAAE,EAAE,EAAE,CAC5D,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;AAEzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,GAAqB,EAAoB,EAAE;IAClE,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE;QAC/D,OAAO,SAAS,CAAA;KACjB;IAED,MAAM,IAAI,GAAG,SAAS,CAAA;IAEtB,MAAM,CAAC,GAAG,CAAC,CAAS,EAAE,OAAe,EAAE,UAA4B,EAAE,EAAE,EAAE,CACvE,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAA;IAErC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QACtB,SAAS,EAAE,MAAM,SAAU,SAAQ,IAAI,CAAC,SAAS;YAC/C,YAAY,OAAe,EAAE,UAA4B,EAAE;gBACzD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAA;YACnC,CAAC;YACD,MAAM,CAAC,QAAQ,CAAC,OAAyB;gBACvC,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAA;YACnD,CAAC;SACF;QAED,GAAG,EAAE,MAAM,GAAI,SAAQ,IAAI,CAAC,GAAG;YAC7B,qBAAqB;YACrB,YACE,IAAwB,EACxB,MAAY,EACZ,UAA4B,EAAE;gBAE9B,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAA;YACxC,CAAC;YACD,oBAAoB;YAEpB,MAAM,CAAC,QAAQ,CAAC,OAAe,EAAE,UAA4B,EAAE;gBAC7D,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAA;YACtD,CAAC;SACF;QAED,QAAQ,EAAE,CACR,CAAS,EACT,UAA0D,EAAE,EAC5D,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAExC,MAAM,EAAE,CACN,CAAS,EACT,UAA0D,EAAE,EAC5D,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAEtC,MAAM,EAAE,CAAC,OAAe,EAAE,UAA4B,EAAE,EAAE,EAAE,CAC1D,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAEzC,QAAQ,EAAE,CAAC,OAAyB,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAEzE,MAAM,EAAE,CAAC,OAAe,EAAE,UAA4B,EAAE,EAAE,EAAE,CAC1D,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAEzC,WAAW,EAAE,CAAC,OAAe,EAAE,UAA4B,EAAE,EAAE,EAAE,CAC/D,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAE9C,KAAK,EAAE,CAAC,IAAc,EAAE,OAAe,EAAE,UAA4B,EAAE,EAAE,EAAE,CACzE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAE9C,GAAG,EAAE,IAAI,CAAC,GAAG;QACb,QAAQ,EAAE,QAA2B;KACtC,CAAC,CAAA;AACJ,CAAC,CAAA;AACD,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAA;AAE7B,mBAAmB;AACnB,qBAAqB;AACrB,mBAAmB;AACnB,8BAA8B;AAC9B,mCAAmC;AACnC,2CAA2C;AAC3C,EAAE;AACF,iCAAiC;AACjC,qBAAqB;AACrB,iBAAiB;AACjB,MAAM,CAAC,MAAM,WAAW,GAAG,CACzB,OAAe,EACf,UAA4B,EAAE,EAC9B,EAAE;IACF,kBAAkB,CAAC,OAAO,CAAC,CAAA;IAE3B,wDAAwD;IACxD,wDAAwD;IACxD,IAAI,OAAO,CAAC,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QACxD,+BAA+B;QAC/B,OAAO,CAAC,OAAO,CAAC,CAAA;KACjB;IAED,OAAO,MAAM,CAAC,OAAO,CAAC,CAAA;AACxB,CAAC,CAAA;AACD,SAAS,CAAC,WAAW,GAAG,WAAW,CAAA;AAEnC,yCAAyC;AACzC,kDAAkD;AAClD,oEAAoE;AACpE,oEAAoE;AACpE,6DAA6D;AAC7D,kEAAkE;AAClE,EAAE;AACF,0EAA0E;AAC1E,wEAAwE;AACxE,qEAAqE;AACrE,8DAA8D;AAE9D,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,OAAe,EAAE,UAA4B,EAAE,EAAE,EAAE,CACxE,IAAI,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,CAAA;AAC1C,SAAS,CAAC,MAAM,GAAG,MAAM,CAAA;AAEzB,MAAM,CAAC,MAAM,KAAK,GAAG,CACnB,IAAc,EACd,OAAe,EACf,UAA4B,EAAE,EAC9B,EAAE;IACF,MAAM,EAAE,GAAG,IAAI,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAC1C,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IACpC,IAAI,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QACrC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;KACnB;IACD,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AACD,SAAS,CAAC,KAAK,GAAG,KAAK,CAAA;AAEvB,+BAA+B;AAC/B,MAAM,SAAS,GAAG,yBAAyB,CAAA;AAC3C,MAAM,YAAY,GAAG,CAAC,CAAS,EAAE,EAAE,CACjC,CAAC,CAAC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAA;AAU/C,MAAM,OAAO,SAAS;IACpB,OAAO,CAAkB;IACzB,GAAG,CAAyB;IAC5B,OAAO,CAAQ;IAEf,oBAAoB,CAAS;IAC7B,QAAQ,CAAS;IACjB,MAAM,CAAS;IACf,OAAO,CAAS;IAChB,KAAK,CAAS;IACd,uBAAuB,CAAS;IAChC,OAAO,CAAS;IAChB,OAAO,CAAU;IACjB,SAAS,CAAY;IACrB,MAAM,CAAS;IAEf,SAAS,CAAS;IAClB,QAAQ,CAAU;IAClB,kBAAkB,CAAS;IAE3B,MAAM,CAAyB;IAC/B,YAAY,OAAe,EAAE,UAA4B,EAAE;QACzD,kBAAkB,CAAC,OAAO,CAAC,CAAA;QAE3B,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,eAAe,CAAA;QACnD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,KAAK,OAAO,CAAA;QAC1C,IAAI,CAAC,oBAAoB;YACvB,CAAC,CAAC,OAAO,CAAC,oBAAoB,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAA;QACxE,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;SAChD;QACD,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAA;QAChE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAA;QAClC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAClB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAA;QAChC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QACnC,IAAI,CAAC,kBAAkB;YACrB,OAAO,CAAC,kBAAkB,KAAK,SAAS;gBACtC,CAAC,CAAC,OAAO,CAAC,kBAAkB;gBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,CAAA;QAEvC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;QACjB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;QACnB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAA;QAEb,+BAA+B;QAC/B,IAAI,CAAC,IAAI,EAAE,CAAA;IACb,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,OAAO,IAAI,CAAA;SACZ;QACD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE;YAC9B,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;gBAC1B,IAAI,OAAO,IAAI,KAAK,QAAQ;oBAAE,OAAO,IAAI,CAAA;aAC1C;SACF;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,KAAK,CAAC,GAAG,CAAQ,IAAG,CAAC;IAErB,IAAI;QACF,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,6CAA6C;QAC7C,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;YACnB,OAAM;SACP;QAED,IAAI,CAAC,OAAO,EAAE;YACZ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;YACjB,OAAM;SACP;QAED,oCAAoC;QACpC,IAAI,CAAC,WAAW,EAAE,CAAA;QAElB,wBAAwB;QACxB,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAA;QAE/C,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAA;SACxD;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QAEtC,+DAA+D;QAC/D,kCAAkC;QAClC,8DAA8D;QAC9D,oDAAoD;QACpD,wCAAwC;QACxC,EAAE;QACF,mEAAmE;QACnE,oEAAoE;QACpE,kEAAkE;QAClE,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;QAC9D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA;QAC9C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;QAExC,mBAAmB;QACnB,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;YACxC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC7C,qCAAqC;gBACrC,MAAM,KAAK,GACT,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;oBACX,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACvB,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACrC,IAAI,KAAK,EAAE;oBACT,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;iBACnE;qBAAM,IAAI,OAAO,EAAE;oBAClB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;iBACvD;aACF;YACD,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;QACpC,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;QAE7B,sDAAsD;QACtD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,CACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CACF,CAAA;QAE5B,2CAA2C;QAC3C,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;gBACrB,IACE,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;oBACX,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;oBACX,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;oBAC5B,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ;oBACxB,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACtB;oBACA,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;iBACX;aACF;SACF;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;IACpC,CAAC;IAED,yDAAyD;IACzD,0DAA0D;IAC1D,yDAAyD;IACzD,4DAA4D;IAC5D,uCAAuC;IACvC,UAAU,CAAC,SAAqB;QAC9B,yDAAyD;QACzD,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC5C,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;wBAC5B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;qBACtB;iBACF;aACF;SACF;QAED,MAAM,EAAE,iBAAiB,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;QAE9C,IAAI,iBAAiB,IAAI,CAAC,EAAE;YAC1B,wDAAwD;YACxD,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAA;YAChD,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAA;SAClD;aAAM,IAAI,iBAAiB,IAAI,CAAC,EAAE;YACjC,mDAAmD;YACnD,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;SAC7C;aAAM;YACL,8CAA8C;YAC9C,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAA;SACtD;QAED,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,wCAAwC;IACxC,yBAAyB,CAAC,SAAqB;QAC7C,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC3B,IAAI,EAAE,GAAW,CAAC,CAAC,CAAA;YACnB,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE;gBAChD,IAAI,CAAC,GAAG,EAAE,CAAA;gBACV,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;oBAC5B,CAAC,EAAE,CAAA;iBACJ;gBACD,IAAI,CAAC,KAAK,EAAE,EAAE;oBACZ,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAA;iBACzB;aACF;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,kDAAkD;IAClD,gBAAgB,CAAC,SAAqB;QACpC,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC3B,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAa,EAAE,IAAI,EAAE,EAAE;gBAC3C,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;gBAChC,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;oBAClC,OAAO,GAAG,CAAA;iBACX;gBACD,IAAI,IAAI,KAAK,IAAI,EAAE;oBACjB,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,EAAE;wBAC1D,GAAG,CAAC,GAAG,EAAE,CAAA;wBACT,OAAO,GAAG,CAAA;qBACX;iBACF;gBACD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACd,OAAO,GAAG,CAAA;YACZ,CAAC,EAAE,EAAE,CAAC,CAAA;YACN,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QAC1C,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,oBAAoB,CAAC,KAAwB;QAC3C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACzB,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;SAC/B;QACD,IAAI,YAAY,GAAY,KAAK,CAAA;QACjC,GAAG;YACD,YAAY,GAAG,KAAK,CAAA;YACpB,mCAAmC;YACnC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBACzC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;oBAClB,iCAAiC;oBACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE;wBAAE,SAAQ;oBACpD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,EAAE;wBACzB,YAAY,GAAG,IAAI,CAAA;wBACnB,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;wBAClB,CAAC,EAAE,CAAA;qBACJ;iBACF;gBACD,IACE,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG;oBAChB,KAAK,CAAC,MAAM,KAAK,CAAC;oBAClB,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,EACrC;oBACA,YAAY,GAAG,IAAI,CAAA;oBACnB,KAAK,CAAC,GAAG,EAAE,CAAA;iBACZ;aACF;YAED,sCAAsC;YACtC,IAAI,EAAE,GAAW,CAAC,CAAA;YAClB,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE;gBAChD,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;gBACvB,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;oBAC9C,YAAY,GAAG,IAAI,CAAA;oBACnB,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;oBACvB,EAAE,IAAI,CAAC,CAAA;iBACR;aACF;SACF,QAAQ,YAAY,EAAC;QACtB,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;IAC1C,CAAC;IAED,yCAAyC;IACzC,8BAA8B;IAC9B,+BAA+B;IAC/B,iDAAiD;IACjD,iBAAiB;IACjB,EAAE;IACF,gEAAgE;IAChE,gEAAgE;IAChE,kEAAkE;IAClE,qDAAqD;IACrD,EAAE;IACF,kFAAkF;IAClF,mCAAmC;IACnC,sCAAsC;IACtC,4BAA4B;IAC5B,EAAE;IACF,qEAAqE;IACrE,+DAA+D;IAC/D,oBAAoB,CAAC,SAAqB;QACxC,IAAI,YAAY,GAAG,KAAK,CAAA;QACxB,GAAG;YACD,YAAY,GAAG,KAAK,CAAA;YACpB,kFAAkF;YAClF,KAAK,IAAI,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,EAAE,GAAW,CAAC,CAAC,CAAA;gBACnB,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE;oBAChD,IAAI,GAAG,GAAW,EAAE,CAAA;oBACpB,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;wBAC9B,wCAAwC;wBACxC,GAAG,EAAE,CAAA;qBACN;oBACD,uDAAuD;oBACvD,mCAAmC;oBACnC,IAAI,GAAG,GAAG,EAAE,EAAE;wBACZ,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,CAAA;qBAC/B;oBAED,IAAI,IAAI,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;oBACxB,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;oBACvB,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;oBACxB,IAAI,IAAI,KAAK,IAAI;wBAAE,SAAQ;oBAC3B,IACE,CAAC,CAAC;wBACF,CAAC,KAAK,GAAG;wBACT,CAAC,KAAK,IAAI;wBACV,CAAC,EAAE;wBACH,EAAE,KAAK,GAAG;wBACV,EAAE,KAAK,IAAI,EACX;wBACA,SAAQ;qBACT;oBACD,YAAY,GAAG,IAAI,CAAA;oBACnB,4CAA4C;oBAC5C,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;oBACnB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;oBAC5B,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;oBAChB,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;oBACrB,EAAE,EAAE,CAAA;iBACL;gBAED,mCAAmC;gBACnC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;oBACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBACzC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;wBAClB,iCAAiC;wBACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE;4BAAE,SAAQ;wBACpD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,EAAE;4BACzB,YAAY,GAAG,IAAI,CAAA;4BACnB,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;4BAClB,CAAC,EAAE,CAAA;yBACJ;qBACF;oBACD,IACE,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG;wBAChB,KAAK,CAAC,MAAM,KAAK,CAAC;wBAClB,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,EACrC;wBACA,YAAY,GAAG,IAAI,CAAA;wBACnB,KAAK,CAAC,GAAG,EAAE,CAAA;qBACZ;iBACF;gBAED,sCAAsC;gBACtC,IAAI,EAAE,GAAW,CAAC,CAAA;gBAClB,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE;oBAChD,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;oBACvB,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;wBAC9C,YAAY,GAAG,IAAI,CAAA;wBACnB,MAAM,OAAO,GAAG,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,IAAI,CAAA;wBAClD,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;wBAClC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAA;wBACjC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;4BAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;wBACtC,EAAE,IAAI,CAAC,CAAA;qBACR;iBACF;aACF;SACF,QAAQ,YAAY,EAAC;QAEtB,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,sCAAsC;IACtC,sDAAsD;IACtD,8CAA8C;IAC9C,oDAAoD;IACpD,EAAE;IACF,2DAA2D;IAC3D,mDAAmD;IACnD,qBAAqB,CAAC,SAAqB;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAC7B,SAAS,CAAC,CAAC,CAAC,EACZ,SAAS,CAAC,CAAC,CAAC,EACZ,CAAC,IAAI,CAAC,uBAAuB,CAC9B,CAAA;gBACD,IAAI,OAAO,EAAE;oBACX,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;oBACjB,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAA;oBACtB,MAAK;iBACN;aACF;SACF;QACD,OAAO,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAA;IAC1C,CAAC;IAED,UAAU,CACR,CAAW,EACX,CAAW,EACX,eAAwB,KAAK;QAE7B,IAAI,EAAE,GAAG,CAAC,CAAA;QACV,IAAI,EAAE,GAAG,CAAC,CAAA;QACV,IAAI,MAAM,GAAa,EAAE,CAAA;QACzB,IAAI,KAAK,GAAW,EAAE,CAAA;QACtB,OAAO,EAAE,GAAG,CAAC,CAAC,MAAM,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE;YACrC,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;gBACnB,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBAC1C,EAAE,EAAE,CAAA;gBACJ,EAAE,EAAE,CAAA;aACL;iBAAM,IAAI,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;gBAChE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBAClB,EAAE,EAAE,CAAA;aACL;iBAAM,IAAI,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;gBAChE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBAClB,EAAE,EAAE,CAAA;aACL;iBAAM,IACL,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG;gBACb,CAAC,CAAC,EAAE,CAAC;gBACL,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC5C,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,EACd;gBACA,IAAI,KAAK,KAAK,GAAG;oBAAE,OAAO,KAAK,CAAA;gBAC/B,KAAK,GAAG,GAAG,CAAA;gBACX,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBAClB,EAAE,EAAE,CAAA;gBACJ,EAAE,EAAE,CAAA;aACL;iBAAM,IACL,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG;gBACb,CAAC,CAAC,EAAE,CAAC;gBACL,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC5C,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,EACd;gBACA,IAAI,KAAK,KAAK,GAAG;oBAAE,OAAO,KAAK,CAAA;gBAC/B,KAAK,GAAG,GAAG,CAAA;gBACX,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBAClB,EAAE,EAAE,CAAA;gBACJ,EAAE,EAAE,CAAA;aACL;iBAAM;gBACL,OAAO,KAAK,CAAA;aACb;SACF;QACD,8DAA8D;QAC9D,iCAAiC;QACjC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IAAI,MAAM,CAAA;IACxC,CAAC;IAED,WAAW;QACT,IAAI,IAAI,CAAC,QAAQ;YAAE,OAAM;QAEzB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,IAAI,MAAM,GAAG,KAAK,CAAA;QAClB,IAAI,YAAY,GAAG,CAAC,CAAA;QAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,EAAE,EAAE;YACpE,MAAM,GAAG,CAAC,MAAM,CAAA;YAChB,YAAY,EAAE,CAAA;SACf;QAED,IAAI,YAAY;YAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;QAC5D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACtB,CAAC;IAED,+CAA+C;IAC/C,yCAAyC;IACzC,uDAAuD;IACvD,mDAAmD;IACnD,mBAAmB;IACnB,QAAQ,CAAC,IAAc,EAAE,OAAsB,EAAE,UAAmB,KAAK;QACvE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,4DAA4D;QAC5D,mEAAmE;QACnE,sBAAsB;QACtB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,SAAS,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1E,MAAM,OAAO,GACX,CAAC,SAAS;gBACV,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE;gBACd,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE;gBACd,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;gBACf,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;YAE3B,MAAM,YAAY,GAChB,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;YAChE,MAAM,UAAU,GACd,CAAC,YAAY;gBACb,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE;gBACjB,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE;gBACjB,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;gBAClB,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ;gBAC9B,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;YAE9B,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;YACnD,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;YACzD,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;gBACtD,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,CAAW,CAAC,CAAA;gBACtE,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,WAAW,EAAE,EAAE;oBACzC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA;oBACjB,IAAI,GAAG,GAAG,GAAG,EAAE;wBACb,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;qBAC7B;yBAAM,IAAI,GAAG,GAAG,GAAG,EAAE;wBACpB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;qBACvB;iBACF;aACF;SACF;QAED,4DAA4D;QAC5D,oEAAoE;QACpE,MAAM,EAAE,iBAAiB,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;QAC9C,IAAI,iBAAiB,IAAI,CAAC,EAAE;YAC1B,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;SACvC;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAA;QAC/C,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;QAEnD,KACE,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,OAAO,CAAC,MAAM,EACzD,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,EAClB,EAAE,EAAE,EAAE,EAAE,EAAE,EACV;YACA,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;YAC3B,IAAI,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAA;YACnB,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAA;YAEhB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;YAEzB,wBAAwB;YACxB,wCAAwC;YACxC,qBAAqB;YACrB,IAAI,CAAC,KAAK,KAAK,EAAE;gBACf,OAAO,KAAK,CAAA;aACb;YACD,oBAAoB;YAEpB,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAClB,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;gBAEvC,OAAO;gBACP,yCAAyC;gBACzC,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd,QAAQ;gBACR,iDAAiD;gBACjD,wDAAwD;gBACxD,yBAAyB;gBACzB,sDAAsD;gBACtD,6BAA6B;gBAC7B,EAAE;gBACF,mCAAmC;gBACnC,gBAAgB;gBAChB,eAAe;gBACf,kCAAkC;gBAClC,oBAAoB;gBACpB,mBAAmB;gBACnB,qCAAqC;gBACrC,mCAAmC;gBACnC,iCAAiC;gBACjC,kCAAkC;gBAClC,IAAI,EAAE,GAAG,EAAE,CAAA;gBACX,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;gBACf,IAAI,EAAE,KAAK,EAAE,EAAE;oBACb,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;oBAC3B,8CAA8C;oBAC9C,yBAAyB;oBACzB,2CAA2C;oBAC3C,sBAAsB;oBACtB,sDAAsD;oBACtD,uBAAuB;oBACvB,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;wBACpB,IACE,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG;4BAChB,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI;4BACjB,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;4BAE5C,OAAO,KAAK,CAAA;qBACf;oBACD,OAAO,IAAI,CAAA;iBACZ;gBAED,mDAAmD;gBACnD,OAAO,EAAE,GAAG,EAAE,EAAE;oBACd,IAAI,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAA;oBAExB,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,CAAC,CAAA;oBAEhE,qDAAqD;oBACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE;wBAC7D,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,CAAA;wBACtD,iBAAiB;wBACjB,OAAO,IAAI,CAAA;qBACZ;yBAAM;wBACL,kCAAkC;wBAClC,iDAAiD;wBACjD,IACE,SAAS,KAAK,GAAG;4BACjB,SAAS,KAAK,IAAI;4BAClB,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAC7C;4BACA,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,CAAA;4BAClD,MAAK;yBACN;wBAED,uCAAuC;wBACvC,IAAI,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAA;wBACtD,EAAE,EAAE,CAAA;qBACL;iBACF;gBAED,sBAAsB;gBACtB,mEAAmE;gBACnE,qBAAqB;gBACrB,IAAI,OAAO,EAAE;oBACX,kBAAkB;oBAClB,IAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,CAAA;oBAC7D,IAAI,EAAE,KAAK,EAAE,EAAE;wBACb,OAAO,IAAI,CAAA;qBACZ;iBACF;gBACD,oBAAoB;gBACpB,OAAO,KAAK,CAAA;aACb;YAED,0BAA0B;YAC1B,gDAAgD;YAChD,qDAAqD;YACrD,IAAI,GAAY,CAAA;YAChB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;gBACzB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAA;gBACb,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA;aACtC;iBAAM;gBACL,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBACf,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA;aACvC;YAED,IAAI,CAAC,GAAG;gBAAE,OAAO,KAAK,CAAA;SACvB;QAED,oDAAoD;QACpD,oDAAoD;QACpD,2CAA2C;QAC3C,kDAAkD;QAClD,oDAAoD;QACpD,uDAAuD;QACvD,oDAAoD;QACpD,yDAAyD;QACzD,6BAA6B;QAC7B,yCAAyC;QAEzC,gEAAgE;QAChE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YAC1B,oDAAoD;YACpD,gBAAgB;YAChB,OAAO,IAAI,CAAA;SACZ;aAAM,IAAI,EAAE,KAAK,EAAE,EAAE;YACpB,+CAA+C;YAC/C,iDAAiD;YACjD,uBAAuB;YACvB,OAAO,OAAO,CAAA;SACf;aAAM,IAAI,EAAE,KAAK,EAAE,EAAE;YACpB,4CAA4C;YAC5C,oDAAoD;YACpD,iDAAiD;YACjD,wBAAwB;YACxB,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YAEvC,qBAAqB;SACtB;aAAM;YACL,yBAAyB;YACzB,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAA;SACxB;QACD,oBAAoB;IACtB,CAAC;IAED,WAAW;QACT,OAAO,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IAChD,CAAC;IAED,KAAK,CAAC,OAAe;QACnB,kBAAkB,CAAC,OAAO,CAAC,CAAA;QAE3B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,YAAY;QACZ,IAAI,OAAO,KAAK,IAAI;YAAE,OAAO,QAAQ,CAAA;QACrC,IAAI,OAAO,KAAK,EAAE;YAAE,OAAO,EAAE,CAAA;QAE7B,uDAAuD;QACvD,0DAA0D;QAC1D,IAAI,CAA0B,CAAA;QAC9B,IAAI,QAAQ,GAAoC,IAAI,CAAA;QACpD,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE;YAC/B,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAA;SAChD;aAAM,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE;YAC5C,QAAQ,GAAG,CACT,OAAO,CAAC,MAAM;gBACZ,CAAC,CAAC,OAAO,CAAC,GAAG;oBACX,CAAC,CAAC,uBAAuB;oBACzB,CAAC,CAAC,oBAAoB;gBACxB,CAAC,CAAC,OAAO,CAAC,GAAG;oBACb,CAAC,CAAC,iBAAiB;oBACnB,CAAC,CAAC,cAAc,CACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACR;aAAM,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE;YACxC,QAAQ,GAAG,CACT,OAAO,CAAC,MAAM;gBACZ,CAAC,CAAC,OAAO,CAAC,GAAG;oBACX,CAAC,CAAC,mBAAmB;oBACrB,CAAC,CAAC,gBAAgB;gBACpB,CAAC,CAAC,OAAO,CAAC,GAAG;oBACb,CAAC,CAAC,aAAa;oBACf,CAAC,CAAC,UAAU,CACf,CAAC,CAAC,CAAC,CAAA;SACL;aAAM,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE;YAC7C,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,eAAe,CAAA;SAC9D;aAAM,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;YACzC,QAAQ,GAAG,WAAW,CAAA;SACvB;QAED,MAAM,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAA;QAC5D,IAAI,QAAQ,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;YACtC,2CAA2C;YAC3C,OAAO,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAA;SACxD;QACD,OAAO,EAAE,CAAA;IACX,CAAC;IAED,MAAM;QACJ,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK;YAAE,OAAO,IAAI,CAAC,MAAM,CAAA;QAE5D,mDAAmD;QACnD,4BAA4B;QAC5B,EAAE;QACF,wDAAwD;QACxD,yDAAyD;QACzD,2CAA2C;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QAEpB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;YACnB,OAAO,IAAI,CAAC,MAAM,CAAA;SACnB;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU;YAChC,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,OAAO,CAAC,GAAG;gBACb,CAAC,CAAC,UAAU;gBACZ,CAAC,CAAC,YAAY,CAAA;QAChB,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QAElD,kCAAkC;QAClC,kDAAkD;QAClD,sEAAsE;QACtE,iDAAiD;QACjD,8DAA8D;QAC9D,mCAAmC;QACnC,IAAI,EAAE,GAAG,GAAG;aACT,GAAG,CAAC,OAAO,CAAC,EAAE;YACb,MAAM,EAAE,GAAiC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACvD,IAAI,CAAC,YAAY,MAAM,EAAE;oBACvB,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;wBAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;iBAChD;gBACD,OAAO,OAAO,CAAC,KAAK,QAAQ;oBAC1B,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;oBACjB,CAAC,CAAC,CAAC,KAAK,QAAQ;wBAChB,CAAC,CAAC,QAAQ;wBACV,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;YACZ,CAAC,CAAiC,CAAA;YAClC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAClB,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;gBACtB,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;gBACtB,IAAI,CAAC,KAAK,QAAQ,IAAI,IAAI,KAAK,QAAQ,EAAE;oBACvC,OAAM;iBACP;gBACD,IAAI,IAAI,KAAK,SAAS,EAAE;oBACtB,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,QAAQ,EAAE;wBAC3C,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO,GAAG,OAAO,GAAG,IAAI,CAAA;qBACjD;yBAAM;wBACL,EAAE,CAAC,CAAC,CAAC,GAAG,OAAO,CAAA;qBAChB;iBACF;qBAAM,IAAI,IAAI,KAAK,SAAS,EAAE;oBAC7B,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO,GAAG,IAAI,CAAA;iBAC9C;qBAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;oBAC5B,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,YAAY,GAAG,OAAO,GAAG,MAAM,GAAG,IAAI,CAAA;oBACzD,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAA;iBACrB;YACH,CAAC,CAAC,CAAA;YACF,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACjD,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;QAEZ,+DAA+D;QAC/D,mEAAmE;QACnE,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QAC9D,4BAA4B;QAC5B,gDAAgD;QAChD,EAAE,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG,GAAG,CAAA;QAElC,gDAAgD;QAChD,IAAI,IAAI,CAAC,MAAM;YAAE,EAAE,GAAG,MAAM,GAAG,EAAE,GAAG,MAAM,CAAA;QAE1C,IAAI;YACF,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;YACjD,qBAAqB;SACtB;QAAC,OAAO,EAAE,EAAE;YACX,uBAAuB;YACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;SACpB;QACD,oBAAoB;QACpB,OAAO,IAAI,CAAC,MAAM,CAAA;IACpB,CAAC;IAED,UAAU,CAAC,CAAS;QAClB,mDAAmD;QACnD,6DAA6D;QAC7D,8CAA8C;QAC9C,0CAA0C;QAC1C,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;SACpB;aAAM,IAAI,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YAClD,sCAAsC;YACtC,OAAO,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;SAC/B;aAAM;YACL,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;SACtB;IACH,CAAC;IAED,KAAK,CAAC,CAAS,EAAE,OAAO,GAAG,IAAI,CAAC,OAAO;QACrC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QACpC,8CAA8C;QAC9C,iBAAiB;QACjB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,KAAK,CAAA;SACb;QACD,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,EAAE,CAAA;SAChB;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,OAAO,EAAE;YACxB,OAAO,IAAI,CAAA;SACZ;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,gCAAgC;QAChC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SAC5B;QAED,6CAA6C;QAC7C,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,CAAA;QAErC,0DAA0D;QAC1D,2DAA2D;QAC3D,mCAAmC;QACnC,uCAAuC;QAEvC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,CAAA;QAEpC,0EAA0E;QAC1E,IAAI,QAAQ,GAAW,EAAE,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QACxC,IAAI,CAAC,QAAQ,EAAE;YACb,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBACpD,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;aACjB;SACF;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;YACtB,IAAI,IAAI,GAAG,EAAE,CAAA;YACb,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7C,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAA;aAClB;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;YACjD,IAAI,GAAG,EAAE;gBACP,IAAI,OAAO,CAAC,UAAU,EAAE;oBACtB,OAAO,IAAI,CAAA;iBACZ;gBACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;aACpB;SACF;QAED,2DAA2D;QAC3D,8BAA8B;QAC9B,IAAI,OAAO,CAAC,UAAU,EAAE;YACtB,OAAO,KAAK,CAAA;SACb;QACD,OAAO,IAAI,CAAC,MAAM,CAAA;IACpB,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,GAAqB;QACnC,OAAO,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,CAAA;IAC1C,CAAC;CACF;AACD,qBAAqB;AACrB,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAA;AAC9B,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AACpC,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AACxC,oBAAoB;AACpB,SAAS,CAAC,GAAG,GAAG,GAAG,CAAA;AACnB,SAAS,CAAC,SAAS,GAAG,SAAS,CAAA;AAC/B,SAAS,CAAC,MAAM,GAAG,MAAM,CAAA;AACzB,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAA", "sourcesContent": ["import expand from 'brace-expansion'\nimport { assertValidPattern } from './assert-valid-pattern.js'\nimport { AST, ExtglobType } from './ast.js'\nimport { escape } from './escape.js'\nimport { unescape } from './unescape.js'\n\ntype Platform =\n  | 'aix'\n  | 'android'\n  | 'darwin'\n  | 'freebsd'\n  | 'haiku'\n  | 'linux'\n  | 'openbsd'\n  | 'sunos'\n  | 'win32'\n  | 'cygwin'\n  | 'netbsd'\n\nexport interface MinimatchOptions {\n  nobrace?: boolean\n  nocomment?: boolean\n  nonegate?: boolean\n  debug?: boolean\n  noglobstar?: boolean\n  noext?: boolean\n  nonull?: boolean\n  windowsPathsNoEscape?: boolean\n  allowWindowsEscape?: boolean\n  partial?: boolean\n  dot?: boolean\n  nocase?: boolean\n  nocaseMagicOnly?: boolean\n  magicalBraces?: boolean\n  matchBase?: boolean\n  flipNegate?: boolean\n  preserveMultipleSlashes?: boolean\n  optimizationLevel?: number\n  platform?: Platform\n  windowsNoMagicRoot?: boolean\n}\n\nexport const minimatch = (\n  p: string,\n  pattern: string,\n  options: MinimatchOptions = {}\n) => {\n  assertValidPattern(pattern)\n\n  // shortcut: comments match nothing.\n  if (!options.nocomment && pattern.charAt(0) === '#') {\n    return false\n  }\n\n  return new Minimatch(pattern, options).match(p)\n}\n\n// Optimized checking for the most common glob patterns.\nconst starDotExtRE = /^\\*+([^+@!?\\*\\[\\(]*)$/\nconst starDotExtTest = (ext: string) => (f: string) =>\n  !f.startsWith('.') && f.endsWith(ext)\nconst starDotExtTestDot = (ext: string) => (f: string) => f.endsWith(ext)\nconst starDotExtTestNocase = (ext: string) => {\n  ext = ext.toLowerCase()\n  return (f: string) => !f.startsWith('.') && f.toLowerCase().endsWith(ext)\n}\nconst starDotExtTestNocaseDot = (ext: string) => {\n  ext = ext.toLowerCase()\n  return (f: string) => f.toLowerCase().endsWith(ext)\n}\nconst starDotStarRE = /^\\*+\\.\\*+$/\nconst starDotStarTest = (f: string) => !f.startsWith('.') && f.includes('.')\nconst starDotStarTestDot = (f: string) =>\n  f !== '.' && f !== '..' && f.includes('.')\nconst dotStarRE = /^\\.\\*+$/\nconst dotStarTest = (f: string) => f !== '.' && f !== '..' && f.startsWith('.')\nconst starRE = /^\\*+$/\nconst starTest = (f: string) => f.length !== 0 && !f.startsWith('.')\nconst starTestDot = (f: string) => f.length !== 0 && f !== '.' && f !== '..'\nconst qmarksRE = /^\\?+([^+@!?\\*\\[\\(]*)?$/\nconst qmarksTestNocase = ([$0, ext = '']: RegExpMatchArray) => {\n  const noext = qmarksTestNoExt([$0])\n  if (!ext) return noext\n  ext = ext.toLowerCase()\n  return (f: string) => noext(f) && f.toLowerCase().endsWith(ext)\n}\nconst qmarksTestNocaseDot = ([$0, ext = '']: RegExpMatchArray) => {\n  const noext = qmarksTestNoExtDot([$0])\n  if (!ext) return noext\n  ext = ext.toLowerCase()\n  return (f: string) => noext(f) && f.toLowerCase().endsWith(ext)\n}\nconst qmarksTestDot = ([$0, ext = '']: RegExpMatchArray) => {\n  const noext = qmarksTestNoExtDot([$0])\n  return !ext ? noext : (f: string) => noext(f) && f.endsWith(ext)\n}\nconst qmarksTest = ([$0, ext = '']: RegExpMatchArray) => {\n  const noext = qmarksTestNoExt([$0])\n  return !ext ? noext : (f: string) => noext(f) && f.endsWith(ext)\n}\nconst qmarksTestNoExt = ([$0]: RegExpMatchArray) => {\n  const len = $0.length\n  return (f: string) => f.length === len && !f.startsWith('.')\n}\nconst qmarksTestNoExtDot = ([$0]: RegExpMatchArray) => {\n  const len = $0.length\n  return (f: string) => f.length === len && f !== '.' && f !== '..'\n}\n\n/* c8 ignore start */\nconst defaultPlatform: Platform = (\n  typeof process === 'object' && process\n    ? (typeof process.env === 'object' &&\n        process.env &&\n        process.env.__MINIMATCH_TESTING_PLATFORM__) ||\n      process.platform\n    : 'posix'\n) as Platform\ntype Sep = '\\\\' | '/'\nconst path: { [k: string]: { sep: Sep } } = {\n  win32: { sep: '\\\\' },\n  posix: { sep: '/' },\n}\n/* c8 ignore stop */\n\nexport const sep = defaultPlatform === 'win32' ? path.win32.sep : path.posix.sep\nminimatch.sep = sep\n\nexport const GLOBSTAR = Symbol('globstar **')\nminimatch.GLOBSTAR = GLOBSTAR\n\n// any single thing other than /\n// don't need to escape / when using new RegExp()\nconst qmark = '[^/]'\n\n// * => any number of characters\nconst star = qmark + '*?'\n\n// ** when dots are allowed.  Anything goes, except .. and .\n// not (^ or / followed by one or two dots followed by $ or /),\n// followed by anything, any number of times.\nconst twoStarDot = '(?:(?!(?:\\\\/|^)(?:\\\\.{1,2})($|\\\\/)).)*?'\n\n// not a ^ or / followed by a dot,\n// followed by anything, any number of times.\nconst twoStarNoDot = '(?:(?!(?:\\\\/|^)\\\\.).)*?'\n\nexport const filter =\n  (pattern: string, options: MinimatchOptions = {}) =>\n  (p: string) =>\n    minimatch(p, pattern, options)\nminimatch.filter = filter\n\nconst ext = (a: MinimatchOptions, b: MinimatchOptions = {}) =>\n  Object.assign({}, a, b)\n\nexport const defaults = (def: MinimatchOptions): typeof minimatch => {\n  if (!def || typeof def !== 'object' || !Object.keys(def).length) {\n    return minimatch\n  }\n\n  const orig = minimatch\n\n  const m = (p: string, pattern: string, options: MinimatchOptions = {}) =>\n    orig(p, pattern, ext(def, options))\n\n  return Object.assign(m, {\n    Minimatch: class Minimatch extends orig.Minimatch {\n      constructor(pattern: string, options: MinimatchOptions = {}) {\n        super(pattern, ext(def, options))\n      }\n      static defaults(options: MinimatchOptions) {\n        return orig.defaults(ext(def, options)).Minimatch\n      }\n    },\n\n    AST: class AST extends orig.AST {\n      /* c8 ignore start */\n      constructor(\n        type: ExtglobType | null,\n        parent?: AST,\n        options: MinimatchOptions = {}\n      ) {\n        super(type, parent, ext(def, options))\n      }\n      /* c8 ignore stop */\n\n      static fromGlob(pattern: string, options: MinimatchOptions = {}) {\n        return orig.AST.fromGlob(pattern, ext(def, options))\n      }\n    },\n\n    unescape: (\n      s: string,\n      options: Pick<MinimatchOptions, 'windowsPathsNoEscape'> = {}\n    ) => orig.unescape(s, ext(def, options)),\n\n    escape: (\n      s: string,\n      options: Pick<MinimatchOptions, 'windowsPathsNoEscape'> = {}\n    ) => orig.escape(s, ext(def, options)),\n\n    filter: (pattern: string, options: MinimatchOptions = {}) =>\n      orig.filter(pattern, ext(def, options)),\n\n    defaults: (options: MinimatchOptions) => orig.defaults(ext(def, options)),\n\n    makeRe: (pattern: string, options: MinimatchOptions = {}) =>\n      orig.makeRe(pattern, ext(def, options)),\n\n    braceExpand: (pattern: string, options: MinimatchOptions = {}) =>\n      orig.braceExpand(pattern, ext(def, options)),\n\n    match: (list: string[], pattern: string, options: MinimatchOptions = {}) =>\n      orig.match(list, pattern, ext(def, options)),\n\n    sep: orig.sep,\n    GLOBSTAR: GLOBSTAR as typeof GLOBSTAR,\n  })\n}\nminimatch.defaults = defaults\n\n// Brace expansion:\n// a{b,c}d -> abd acd\n// a{b,}c -> abc ac\n// a{0..3}d -> a0d a1d a2d a3d\n// a{b,c{d,e}f}g -> abg acdfg acefg\n// a{b,c}d{e,f}g -> abdeg acdeg abdeg abdfg\n//\n// Invalid sets are not expanded.\n// a{2..}b -> a{2..}b\n// a{b}c -> a{b}c\nexport const braceExpand = (\n  pattern: string,\n  options: MinimatchOptions = {}\n) => {\n  assertValidPattern(pattern)\n\n  // Thanks to Yeting Li <https://github.com/yetingli> for\n  // improving this regexp to avoid a ReDOS vulnerability.\n  if (options.nobrace || !/\\{(?:(?!\\{).)*\\}/.test(pattern)) {\n    // shortcut. no need to expand.\n    return [pattern]\n  }\n\n  return expand(pattern)\n}\nminimatch.braceExpand = braceExpand\n\n// parse a component of the expanded set.\n// At this point, no pattern may contain \"/\" in it\n// so we're going to return a 2d array, where each entry is the full\n// pattern, split on '/', and then turned into a regular expression.\n// A regexp is made at the end which joins each array with an\n// escaped /, and another full one which joins each regexp with |.\n//\n// Following the lead of Bash 4.1, note that \"**\" only has special meaning\n// when it is the *only* thing in a path portion.  Otherwise, any series\n// of * is equivalent to a single *.  Globstar behavior is enabled by\n// default, and can be disabled by setting options.noglobstar.\n\nexport const makeRe = (pattern: string, options: MinimatchOptions = {}) =>\n  new Minimatch(pattern, options).makeRe()\nminimatch.makeRe = makeRe\n\nexport const match = (\n  list: string[],\n  pattern: string,\n  options: MinimatchOptions = {}\n) => {\n  const mm = new Minimatch(pattern, options)\n  list = list.filter(f => mm.match(f))\n  if (mm.options.nonull && !list.length) {\n    list.push(pattern)\n  }\n  return list\n}\nminimatch.match = match\n\n// replace stuff like \\* with *\nconst globMagic = /[?*]|[+@!]\\(.*?\\)|\\[|\\]/\nconst regExpEscape = (s: string) =>\n  s.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&')\n\nexport type MMRegExp = RegExp & {\n  _src?: string\n  _glob?: string\n}\n\nexport type ParseReturnFiltered = string | MMRegExp | typeof GLOBSTAR\nexport type ParseReturn = ParseReturnFiltered | false\n\nexport class Minimatch {\n  options: MinimatchOptions\n  set: ParseReturnFiltered[][]\n  pattern: string\n\n  windowsPathsNoEscape: boolean\n  nonegate: boolean\n  negate: boolean\n  comment: boolean\n  empty: boolean\n  preserveMultipleSlashes: boolean\n  partial: boolean\n  globSet: string[]\n  globParts: string[][]\n  nocase: boolean\n\n  isWindows: boolean\n  platform: Platform\n  windowsNoMagicRoot: boolean\n\n  regexp: false | null | MMRegExp\n  constructor(pattern: string, options: MinimatchOptions = {}) {\n    assertValidPattern(pattern)\n\n    options = options || {}\n    this.options = options\n    this.pattern = pattern\n    this.platform = options.platform || defaultPlatform\n    this.isWindows = this.platform === 'win32'\n    this.windowsPathsNoEscape =\n      !!options.windowsPathsNoEscape || options.allowWindowsEscape === false\n    if (this.windowsPathsNoEscape) {\n      this.pattern = this.pattern.replace(/\\\\/g, '/')\n    }\n    this.preserveMultipleSlashes = !!options.preserveMultipleSlashes\n    this.regexp = null\n    this.negate = false\n    this.nonegate = !!options.nonegate\n    this.comment = false\n    this.empty = false\n    this.partial = !!options.partial\n    this.nocase = !!this.options.nocase\n    this.windowsNoMagicRoot =\n      options.windowsNoMagicRoot !== undefined\n        ? options.windowsNoMagicRoot\n        : !!(this.isWindows && this.nocase)\n\n    this.globSet = []\n    this.globParts = []\n    this.set = []\n\n    // make the set of regexps etc.\n    this.make()\n  }\n\n  hasMagic(): boolean {\n    if (this.options.magicalBraces && this.set.length > 1) {\n      return true\n    }\n    for (const pattern of this.set) {\n      for (const part of pattern) {\n        if (typeof part !== 'string') return true\n      }\n    }\n    return false\n  }\n\n  debug(..._: any[]) {}\n\n  make() {\n    const pattern = this.pattern\n    const options = this.options\n\n    // empty patterns and comments match nothing.\n    if (!options.nocomment && pattern.charAt(0) === '#') {\n      this.comment = true\n      return\n    }\n\n    if (!pattern) {\n      this.empty = true\n      return\n    }\n\n    // step 1: figure out negation, etc.\n    this.parseNegate()\n\n    // step 2: expand braces\n    this.globSet = [...new Set(this.braceExpand())]\n\n    if (options.debug) {\n      this.debug = (...args: any[]) => console.error(...args)\n    }\n\n    this.debug(this.pattern, this.globSet)\n\n    // step 3: now we have a set, so turn each one into a series of\n    // path-portion matching patterns.\n    // These will be regexps, except in the case of \"**\", which is\n    // set to the GLOBSTAR object for globstar behavior,\n    // and will not contain any / characters\n    //\n    // First, we preprocess to make the glob pattern sets a bit simpler\n    // and deduped.  There are some perf-killing patterns that can cause\n    // problems with a glob walk, but we can simplify them down a bit.\n    const rawGlobParts = this.globSet.map(s => this.slashSplit(s))\n    this.globParts = this.preprocess(rawGlobParts)\n    this.debug(this.pattern, this.globParts)\n\n    // glob --> regexps\n    let set = this.globParts.map((s, _, __) => {\n      if (this.isWindows && this.windowsNoMagicRoot) {\n        // check if it's a drive or unc path.\n        const isUNC =\n          s[0] === '' &&\n          s[1] === '' &&\n          (s[2] === '?' || !globMagic.test(s[2])) &&\n          !globMagic.test(s[3])\n        const isDrive = /^[a-z]:/i.test(s[0])\n        if (isUNC) {\n          return [...s.slice(0, 4), ...s.slice(4).map(ss => this.parse(ss))]\n        } else if (isDrive) {\n          return [s[0], ...s.slice(1).map(ss => this.parse(ss))]\n        }\n      }\n      return s.map(ss => this.parse(ss))\n    })\n\n    this.debug(this.pattern, set)\n\n    // filter out everything that didn't compile properly.\n    this.set = set.filter(\n      s => s.indexOf(false) === -1\n    ) as ParseReturnFiltered[][]\n\n    // do not treat the ? in UNC paths as magic\n    if (this.isWindows) {\n      for (let i = 0; i < this.set.length; i++) {\n        const p = this.set[i]\n        if (\n          p[0] === '' &&\n          p[1] === '' &&\n          this.globParts[i][2] === '?' &&\n          typeof p[3] === 'string' &&\n          /^[a-z]:$/i.test(p[3])\n        ) {\n          p[2] = '?'\n        }\n      }\n    }\n\n    this.debug(this.pattern, this.set)\n  }\n\n  // various transforms to equivalent pattern sets that are\n  // faster to process in a filesystem walk.  The goal is to\n  // eliminate what we can, and push all ** patterns as far\n  // to the right as possible, even if it increases the number\n  // of patterns that we have to process.\n  preprocess(globParts: string[][]) {\n    // if we're not in globstar mode, then turn all ** into *\n    if (this.options.noglobstar) {\n      for (let i = 0; i < globParts.length; i++) {\n        for (let j = 0; j < globParts[i].length; j++) {\n          if (globParts[i][j] === '**') {\n            globParts[i][j] = '*'\n          }\n        }\n      }\n    }\n\n    const { optimizationLevel = 1 } = this.options\n\n    if (optimizationLevel >= 2) {\n      // aggressive optimization for the purpose of fs walking\n      globParts = this.firstPhasePreProcess(globParts)\n      globParts = this.secondPhasePreProcess(globParts)\n    } else if (optimizationLevel >= 1) {\n      // just basic optimizations to remove some .. parts\n      globParts = this.levelOneOptimize(globParts)\n    } else {\n      // just collapse multiple ** portions into one\n      globParts = this.adjascentGlobstarOptimize(globParts)\n    }\n\n    return globParts\n  }\n\n  // just get rid of adjascent ** portions\n  adjascentGlobstarOptimize(globParts: string[][]) {\n    return globParts.map(parts => {\n      let gs: number = -1\n      while (-1 !== (gs = parts.indexOf('**', gs + 1))) {\n        let i = gs\n        while (parts[i + 1] === '**') {\n          i++\n        }\n        if (i !== gs) {\n          parts.splice(gs, i - gs)\n        }\n      }\n      return parts\n    })\n  }\n\n  // get rid of adjascent ** and resolve .. portions\n  levelOneOptimize(globParts: string[][]) {\n    return globParts.map(parts => {\n      parts = parts.reduce((set: string[], part) => {\n        const prev = set[set.length - 1]\n        if (part === '**' && prev === '**') {\n          return set\n        }\n        if (part === '..') {\n          if (prev && prev !== '..' && prev !== '.' && prev !== '**') {\n            set.pop()\n            return set\n          }\n        }\n        set.push(part)\n        return set\n      }, [])\n      return parts.length === 0 ? [''] : parts\n    })\n  }\n\n  levelTwoFileOptimize(parts: string | string[]) {\n    if (!Array.isArray(parts)) {\n      parts = this.slashSplit(parts)\n    }\n    let didSomething: boolean = false\n    do {\n      didSomething = false\n      // <pre>/<e>/<rest> -> <pre>/<rest>\n      if (!this.preserveMultipleSlashes) {\n        for (let i = 1; i < parts.length - 1; i++) {\n          const p = parts[i]\n          // don't squeeze out UNC patterns\n          if (i === 1 && p === '' && parts[0] === '') continue\n          if (p === '.' || p === '') {\n            didSomething = true\n            parts.splice(i, 1)\n            i--\n          }\n        }\n        if (\n          parts[0] === '.' &&\n          parts.length === 2 &&\n          (parts[1] === '.' || parts[1] === '')\n        ) {\n          didSomething = true\n          parts.pop()\n        }\n      }\n\n      // <pre>/<p>/../<rest> -> <pre>/<rest>\n      let dd: number = 0\n      while (-1 !== (dd = parts.indexOf('..', dd + 1))) {\n        const p = parts[dd - 1]\n        if (p && p !== '.' && p !== '..' && p !== '**') {\n          didSomething = true\n          parts.splice(dd - 1, 2)\n          dd -= 2\n        }\n      }\n    } while (didSomething)\n    return parts.length === 0 ? [''] : parts\n  }\n\n  // First phase: single-pattern processing\n  // <pre> is 1 or more portions\n  // <rest> is 1 or more portions\n  // <p> is any portion other than ., .., '', or **\n  // <e> is . or ''\n  //\n  // **/.. is *brutal* for filesystem walking performance, because\n  // it effectively resets the recursive walk each time it occurs,\n  // and ** cannot be reduced out by a .. pattern part like a regexp\n  // or most strings (other than .., ., and '') can be.\n  //\n  // <pre>/**/../<p>/<p>/<rest> -> {<pre>/../<p>/<p>/<rest>,<pre>/**/<p>/<p>/<rest>}\n  // <pre>/<e>/<rest> -> <pre>/<rest>\n  // <pre>/<p>/../<rest> -> <pre>/<rest>\n  // **/**/<rest> -> **/<rest>\n  //\n  // **/*/<rest> -> */**/<rest> <== not valid because ** doesn't follow\n  // this WOULD be allowed if ** did follow symlinks, or * didn't\n  firstPhasePreProcess(globParts: string[][]) {\n    let didSomething = false\n    do {\n      didSomething = false\n      // <pre>/**/../<p>/<p>/<rest> -> {<pre>/../<p>/<p>/<rest>,<pre>/**/<p>/<p>/<rest>}\n      for (let parts of globParts) {\n        let gs: number = -1\n        while (-1 !== (gs = parts.indexOf('**', gs + 1))) {\n          let gss: number = gs\n          while (parts[gss + 1] === '**') {\n            // <pre>/**/**/<rest> -> <pre>/**/<rest>\n            gss++\n          }\n          // eg, if gs is 2 and gss is 4, that means we have 3 **\n          // parts, and can remove 2 of them.\n          if (gss > gs) {\n            parts.splice(gs + 1, gss - gs)\n          }\n\n          let next = parts[gs + 1]\n          const p = parts[gs + 2]\n          const p2 = parts[gs + 3]\n          if (next !== '..') continue\n          if (\n            !p ||\n            p === '.' ||\n            p === '..' ||\n            !p2 ||\n            p2 === '.' ||\n            p2 === '..'\n          ) {\n            continue\n          }\n          didSomething = true\n          // edit parts in place, and push the new one\n          parts.splice(gs, 1)\n          const other = parts.slice(0)\n          other[gs] = '**'\n          globParts.push(other)\n          gs--\n        }\n\n        // <pre>/<e>/<rest> -> <pre>/<rest>\n        if (!this.preserveMultipleSlashes) {\n          for (let i = 1; i < parts.length - 1; i++) {\n            const p = parts[i]\n            // don't squeeze out UNC patterns\n            if (i === 1 && p === '' && parts[0] === '') continue\n            if (p === '.' || p === '') {\n              didSomething = true\n              parts.splice(i, 1)\n              i--\n            }\n          }\n          if (\n            parts[0] === '.' &&\n            parts.length === 2 &&\n            (parts[1] === '.' || parts[1] === '')\n          ) {\n            didSomething = true\n            parts.pop()\n          }\n        }\n\n        // <pre>/<p>/../<rest> -> <pre>/<rest>\n        let dd: number = 0\n        while (-1 !== (dd = parts.indexOf('..', dd + 1))) {\n          const p = parts[dd - 1]\n          if (p && p !== '.' && p !== '..' && p !== '**') {\n            didSomething = true\n            const needDot = dd === 1 && parts[dd + 1] === '**'\n            const splin = needDot ? ['.'] : []\n            parts.splice(dd - 1, 2, ...splin)\n            if (parts.length === 0) parts.push('')\n            dd -= 2\n          }\n        }\n      }\n    } while (didSomething)\n\n    return globParts\n  }\n\n  // second phase: multi-pattern dedupes\n  // {<pre>/*/<rest>,<pre>/<p>/<rest>} -> <pre>/*/<rest>\n  // {<pre>/<rest>,<pre>/<rest>} -> <pre>/<rest>\n  // {<pre>/**/<rest>,<pre>/<rest>} -> <pre>/**/<rest>\n  //\n  // {<pre>/**/<rest>,<pre>/**/<p>/<rest>} -> <pre>/**/<rest>\n  // ^-- not valid because ** doens't follow symlinks\n  secondPhasePreProcess(globParts: string[][]): string[][] {\n    for (let i = 0; i < globParts.length - 1; i++) {\n      for (let j = i + 1; j < globParts.length; j++) {\n        const matched = this.partsMatch(\n          globParts[i],\n          globParts[j],\n          !this.preserveMultipleSlashes\n        )\n        if (matched) {\n          globParts[i] = []\n          globParts[j] = matched\n          break\n        }\n      }\n    }\n    return globParts.filter(gs => gs.length)\n  }\n\n  partsMatch(\n    a: string[],\n    b: string[],\n    emptyGSMatch: boolean = false\n  ): false | string[] {\n    let ai = 0\n    let bi = 0\n    let result: string[] = []\n    let which: string = ''\n    while (ai < a.length && bi < b.length) {\n      if (a[ai] === b[bi]) {\n        result.push(which === 'b' ? b[bi] : a[ai])\n        ai++\n        bi++\n      } else if (emptyGSMatch && a[ai] === '**' && b[bi] === a[ai + 1]) {\n        result.push(a[ai])\n        ai++\n      } else if (emptyGSMatch && b[bi] === '**' && a[ai] === b[bi + 1]) {\n        result.push(b[bi])\n        bi++\n      } else if (\n        a[ai] === '*' &&\n        b[bi] &&\n        (this.options.dot || !b[bi].startsWith('.')) &&\n        b[bi] !== '**'\n      ) {\n        if (which === 'b') return false\n        which = 'a'\n        result.push(a[ai])\n        ai++\n        bi++\n      } else if (\n        b[bi] === '*' &&\n        a[ai] &&\n        (this.options.dot || !a[ai].startsWith('.')) &&\n        a[ai] !== '**'\n      ) {\n        if (which === 'a') return false\n        which = 'b'\n        result.push(b[bi])\n        ai++\n        bi++\n      } else {\n        return false\n      }\n    }\n    // if we fall out of the loop, it means they two are identical\n    // as long as their lengths match\n    return a.length === b.length && result\n  }\n\n  parseNegate() {\n    if (this.nonegate) return\n\n    const pattern = this.pattern\n    let negate = false\n    let negateOffset = 0\n\n    for (let i = 0; i < pattern.length && pattern.charAt(i) === '!'; i++) {\n      negate = !negate\n      negateOffset++\n    }\n\n    if (negateOffset) this.pattern = pattern.slice(negateOffset)\n    this.negate = negate\n  }\n\n  // set partial to true to test if, for example,\n  // \"/a/b\" matches the start of \"/*/b/*/d\"\n  // Partial means, if you run out of file before you run\n  // out of pattern, then that's fine, as long as all\n  // the parts match.\n  matchOne(file: string[], pattern: ParseReturn[], partial: boolean = false) {\n    const options = this.options\n\n    // UNC paths like //?/X:/... can match X:/... and vice versa\n    // Drive letters in absolute drive or unc paths are always compared\n    // case-insensitively.\n    if (this.isWindows) {\n      const fileDrive = typeof file[0] === 'string' && /^[a-z]:$/i.test(file[0])\n      const fileUNC =\n        !fileDrive &&\n        file[0] === '' &&\n        file[1] === '' &&\n        file[2] === '?' &&\n        /^[a-z]:$/i.test(file[3])\n\n      const patternDrive =\n        typeof pattern[0] === 'string' && /^[a-z]:$/i.test(pattern[0])\n      const patternUNC =\n        !patternDrive &&\n        pattern[0] === '' &&\n        pattern[1] === '' &&\n        pattern[2] === '?' &&\n        typeof pattern[3] === 'string' &&\n        /^[a-z]:$/i.test(pattern[3])\n\n      const fdi = fileUNC ? 3 : fileDrive ? 0 : undefined\n      const pdi = patternUNC ? 3 : patternDrive ? 0 : undefined\n      if (typeof fdi === 'number' && typeof pdi === 'number') {\n        const [fd, pd]: [string, string] = [file[fdi], pattern[pdi] as string]\n        if (fd.toLowerCase() === pd.toLowerCase()) {\n          pattern[pdi] = fd\n          if (pdi > fdi) {\n            pattern = pattern.slice(pdi)\n          } else if (fdi > pdi) {\n            file = file.slice(fdi)\n          }\n        }\n      }\n    }\n\n    // resolve and reduce . and .. portions in the file as well.\n    // dont' need to do the second phase, because it's only one string[]\n    const { optimizationLevel = 1 } = this.options\n    if (optimizationLevel >= 2) {\n      file = this.levelTwoFileOptimize(file)\n    }\n\n    this.debug('matchOne', this, { file, pattern })\n    this.debug('matchOne', file.length, pattern.length)\n\n    for (\n      var fi = 0, pi = 0, fl = file.length, pl = pattern.length;\n      fi < fl && pi < pl;\n      fi++, pi++\n    ) {\n      this.debug('matchOne loop')\n      var p = pattern[pi]\n      var f = file[fi]\n\n      this.debug(pattern, p, f)\n\n      // should be impossible.\n      // some invalid regexp stuff in the set.\n      /* c8 ignore start */\n      if (p === false) {\n        return false\n      }\n      /* c8 ignore stop */\n\n      if (p === GLOBSTAR) {\n        this.debug('GLOBSTAR', [pattern, p, f])\n\n        // \"**\"\n        // a/**/b/**/c would match the following:\n        // a/b/x/y/z/c\n        // a/x/y/z/b/c\n        // a/b/x/b/x/c\n        // a/b/c\n        // To do this, take the rest of the pattern after\n        // the **, and see if it would match the file remainder.\n        // If so, return success.\n        // If not, the ** \"swallows\" a segment, and try again.\n        // This is recursively awful.\n        //\n        // a/**/b/**/c matching a/b/x/y/z/c\n        // - a matches a\n        // - doublestar\n        //   - matchOne(b/x/y/z/c, b/**/c)\n        //     - b matches b\n        //     - doublestar\n        //       - matchOne(x/y/z/c, c) -> no\n        //       - matchOne(y/z/c, c) -> no\n        //       - matchOne(z/c, c) -> no\n        //       - matchOne(c, c) yes, hit\n        var fr = fi\n        var pr = pi + 1\n        if (pr === pl) {\n          this.debug('** at the end')\n          // a ** at the end will just swallow the rest.\n          // We have found a match.\n          // however, it will not swallow /.x, unless\n          // options.dot is set.\n          // . and .. are *never* matched by **, for explosively\n          // exponential reasons.\n          for (; fi < fl; fi++) {\n            if (\n              file[fi] === '.' ||\n              file[fi] === '..' ||\n              (!options.dot && file[fi].charAt(0) === '.')\n            )\n              return false\n          }\n          return true\n        }\n\n        // ok, let's see if we can swallow whatever we can.\n        while (fr < fl) {\n          var swallowee = file[fr]\n\n          this.debug('\\nglobstar while', file, fr, pattern, pr, swallowee)\n\n          // XXX remove this slice.  Just pass the start index.\n          if (this.matchOne(file.slice(fr), pattern.slice(pr), partial)) {\n            this.debug('globstar found match!', fr, fl, swallowee)\n            // found a match.\n            return true\n          } else {\n            // can't swallow \".\" or \"..\" ever.\n            // can only swallow \".foo\" when explicitly asked.\n            if (\n              swallowee === '.' ||\n              swallowee === '..' ||\n              (!options.dot && swallowee.charAt(0) === '.')\n            ) {\n              this.debug('dot detected!', file, fr, pattern, pr)\n              break\n            }\n\n            // ** swallows a segment, and continue.\n            this.debug('globstar swallow a segment, and continue')\n            fr++\n          }\n        }\n\n        // no match was found.\n        // However, in partial mode, we can't say this is necessarily over.\n        /* c8 ignore start */\n        if (partial) {\n          // ran out of file\n          this.debug('\\n>>> no match, partial?', file, fr, pattern, pr)\n          if (fr === fl) {\n            return true\n          }\n        }\n        /* c8 ignore stop */\n        return false\n      }\n\n      // something other than **\n      // non-magic patterns just have to match exactly\n      // patterns with magic have been turned into regexps.\n      let hit: boolean\n      if (typeof p === 'string') {\n        hit = f === p\n        this.debug('string match', p, f, hit)\n      } else {\n        hit = p.test(f)\n        this.debug('pattern match', p, f, hit)\n      }\n\n      if (!hit) return false\n    }\n\n    // Note: ending in / means that we'll get a final \"\"\n    // at the end of the pattern.  This can only match a\n    // corresponding \"\" at the end of the file.\n    // If the file ends in /, then it can only match a\n    // a pattern that ends in /, unless the pattern just\n    // doesn't have any more for it. But, a/b/ should *not*\n    // match \"a/b/*\", even though \"\" matches against the\n    // [^/]*? pattern, except in partial mode, where it might\n    // simply not be reached yet.\n    // However, a/b/ should still satisfy a/*\n\n    // now either we fell off the end of the pattern, or we're done.\n    if (fi === fl && pi === pl) {\n      // ran out of pattern and filename at the same time.\n      // an exact hit!\n      return true\n    } else if (fi === fl) {\n      // ran out of file, but still had pattern left.\n      // this is ok if we're doing the match as part of\n      // a glob fs traversal.\n      return partial\n    } else if (pi === pl) {\n      // ran out of pattern, still have file left.\n      // this is only acceptable if we're on the very last\n      // empty segment of a file with a trailing slash.\n      // a/* should match a/b/\n      return fi === fl - 1 && file[fi] === ''\n\n      /* c8 ignore start */\n    } else {\n      // should be unreachable.\n      throw new Error('wtf?')\n    }\n    /* c8 ignore stop */\n  }\n\n  braceExpand() {\n    return braceExpand(this.pattern, this.options)\n  }\n\n  parse(pattern: string): ParseReturn {\n    assertValidPattern(pattern)\n\n    const options = this.options\n\n    // shortcuts\n    if (pattern === '**') return GLOBSTAR\n    if (pattern === '') return ''\n\n    // far and away, the most common glob pattern parts are\n    // *, *.*, and *.<ext>  Add a fast check method for those.\n    let m: RegExpMatchArray | null\n    let fastTest: null | ((f: string) => boolean) = null\n    if ((m = pattern.match(starRE))) {\n      fastTest = options.dot ? starTestDot : starTest\n    } else if ((m = pattern.match(starDotExtRE))) {\n      fastTest = (\n        options.nocase\n          ? options.dot\n            ? starDotExtTestNocaseDot\n            : starDotExtTestNocase\n          : options.dot\n          ? starDotExtTestDot\n          : starDotExtTest\n      )(m[1])\n    } else if ((m = pattern.match(qmarksRE))) {\n      fastTest = (\n        options.nocase\n          ? options.dot\n            ? qmarksTestNocaseDot\n            : qmarksTestNocase\n          : options.dot\n          ? qmarksTestDot\n          : qmarksTest\n      )(m)\n    } else if ((m = pattern.match(starDotStarRE))) {\n      fastTest = options.dot ? starDotStarTestDot : starDotStarTest\n    } else if ((m = pattern.match(dotStarRE))) {\n      fastTest = dotStarTest\n    }\n\n    const re = AST.fromGlob(pattern, this.options).toMMPattern()\n    if (fastTest && typeof re === 'object') {\n      // Avoids overriding in frozen environments\n      Reflect.defineProperty(re, 'test', { value: fastTest })\n    }\n    return re\n  }\n\n  makeRe() {\n    if (this.regexp || this.regexp === false) return this.regexp\n\n    // at this point, this.set is a 2d array of partial\n    // pattern strings, or \"**\".\n    //\n    // It's better to use .match().  This function shouldn't\n    // be used, really, but it's pretty convenient sometimes,\n    // when you just want to work with a regex.\n    const set = this.set\n\n    if (!set.length) {\n      this.regexp = false\n      return this.regexp\n    }\n    const options = this.options\n\n    const twoStar = options.noglobstar\n      ? star\n      : options.dot\n      ? twoStarDot\n      : twoStarNoDot\n    const flags = new Set(options.nocase ? ['i'] : [])\n\n    // regexpify non-globstar patterns\n    // if ** is only item, then we just do one twoStar\n    // if ** is first, and there are more, prepend (\\/|twoStar\\/)? to next\n    // if ** is last, append (\\/twoStar|) to previous\n    // if ** is in the middle, append (\\/|\\/twoStar\\/) to previous\n    // then filter out GLOBSTAR symbols\n    let re = set\n      .map(pattern => {\n        const pp: (string | typeof GLOBSTAR)[] = pattern.map(p => {\n          if (p instanceof RegExp) {\n            for (const f of p.flags.split('')) flags.add(f)\n          }\n          return typeof p === 'string'\n            ? regExpEscape(p)\n            : p === GLOBSTAR\n            ? GLOBSTAR\n            : p._src\n        }) as (string | typeof GLOBSTAR)[]\n        pp.forEach((p, i) => {\n          const next = pp[i + 1]\n          const prev = pp[i - 1]\n          if (p !== GLOBSTAR || prev === GLOBSTAR) {\n            return\n          }\n          if (prev === undefined) {\n            if (next !== undefined && next !== GLOBSTAR) {\n              pp[i + 1] = '(?:\\\\/|' + twoStar + '\\\\/)?' + next\n            } else {\n              pp[i] = twoStar\n            }\n          } else if (next === undefined) {\n            pp[i - 1] = prev + '(?:\\\\/|' + twoStar + ')?'\n          } else if (next !== GLOBSTAR) {\n            pp[i - 1] = prev + '(?:\\\\/|\\\\/' + twoStar + '\\\\/)' + next\n            pp[i + 1] = GLOBSTAR\n          }\n        })\n        return pp.filter(p => p !== GLOBSTAR).join('/')\n      })\n      .join('|')\n\n    // need to wrap in parens if we had more than one thing with |,\n    // otherwise only the first will be anchored to ^ and the last to $\n    const [open, close] = set.length > 1 ? ['(?:', ')'] : ['', '']\n    // must match entire pattern\n    // ending in a * or ** will make it less strict.\n    re = '^' + open + re + close + '$'\n\n    // can match anything, as long as it's not this.\n    if (this.negate) re = '^(?!' + re + ').+$'\n\n    try {\n      this.regexp = new RegExp(re, [...flags].join(''))\n      /* c8 ignore start */\n    } catch (ex) {\n      // should be impossible\n      this.regexp = false\n    }\n    /* c8 ignore stop */\n    return this.regexp\n  }\n\n  slashSplit(p: string) {\n    // if p starts with // on windows, we preserve that\n    // so that UNC paths aren't broken.  Otherwise, any number of\n    // / characters are coalesced into one, unless\n    // preserveMultipleSlashes is set to true.\n    if (this.preserveMultipleSlashes) {\n      return p.split('/')\n    } else if (this.isWindows && /^\\/\\/[^\\/]+/.test(p)) {\n      // add an extra '' for the one we lose\n      return ['', ...p.split(/\\/+/)]\n    } else {\n      return p.split(/\\/+/)\n    }\n  }\n\n  match(f: string, partial = this.partial) {\n    this.debug('match', f, this.pattern)\n    // short-circuit in the case of busted things.\n    // comments, etc.\n    if (this.comment) {\n      return false\n    }\n    if (this.empty) {\n      return f === ''\n    }\n\n    if (f === '/' && partial) {\n      return true\n    }\n\n    const options = this.options\n\n    // windows: need to use /, not \\\n    if (this.isWindows) {\n      f = f.split('\\\\').join('/')\n    }\n\n    // treat the test path as a set of pathparts.\n    const ff = this.slashSplit(f)\n    this.debug(this.pattern, 'split', ff)\n\n    // just ONE of the pattern sets in this.set needs to match\n    // in order for it to be valid.  If negating, then just one\n    // match means that we have failed.\n    // Either way, return on the first hit.\n\n    const set = this.set\n    this.debug(this.pattern, 'set', set)\n\n    // Find the basename of the path by looking for the last non-empty segment\n    let filename: string = ff[ff.length - 1]\n    if (!filename) {\n      for (let i = ff.length - 2; !filename && i >= 0; i--) {\n        filename = ff[i]\n      }\n    }\n\n    for (let i = 0; i < set.length; i++) {\n      const pattern = set[i]\n      let file = ff\n      if (options.matchBase && pattern.length === 1) {\n        file = [filename]\n      }\n      const hit = this.matchOne(file, pattern, partial)\n      if (hit) {\n        if (options.flipNegate) {\n          return true\n        }\n        return !this.negate\n      }\n    }\n\n    // didn't get any hits.  this is success if it's a negative\n    // pattern, failure otherwise.\n    if (options.flipNegate) {\n      return false\n    }\n    return this.negate\n  }\n\n  static defaults(def: MinimatchOptions) {\n    return minimatch.defaults(def).Minimatch\n  }\n}\n/* c8 ignore start */\nexport { AST } from './ast.js'\nexport { escape } from './escape.js'\nexport { unescape } from './unescape.js'\n/* c8 ignore stop */\nminimatch.AST = AST\nminimatch.Minimatch = Minimatch\nminimatch.escape = escape\nminimatch.unescape = unescape\n"]}