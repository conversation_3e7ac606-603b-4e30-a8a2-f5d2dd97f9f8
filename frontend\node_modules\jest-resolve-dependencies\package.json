{"name": "jest-resolve-dependencies", "version": "29.7.0", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-resolve-dependencies"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"jest-regex-util": "^29.6.3", "jest-snapshot": "^29.7.0"}, "devDependencies": {"@jest/test-utils": "^29.7.0", "@jest/types": "^29.6.3", "jest-haste-map": "^29.7.0", "jest-resolve": "^29.7.0", "jest-runtime": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630"}