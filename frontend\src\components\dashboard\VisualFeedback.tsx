import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  Zap, 
  CheckCircle, 
  Calendar, 
  FileText, 
  Search,
  Globe,
  Database,
  Sparkles,
  AlertCircle
} from 'lucide-react';

import { ProcessingUpdate } from '@/types';

interface VisualFeedbackProps {
  currentStep: string;
  processingSteps: ProcessingUpdate[];
  isProcessing: boolean;
}

// Animation configurations for each step (following mermaid diagram exactly)
const stepAnimations = {
  analyzing: {
    icon: Brain,
    message: "🎭 Analyzing input...",
    animation: "typing_indicator",
    color: "text-primary-400",
    bgColor: "bg-primary-600/20"
  },
  categorizing: {
    icon: Brain,
    message: "🤖 Categorizing...",
    animation: "brain_animation", 
    color: "text-primary-400",
    bgColor: "bg-primary-600/20"
  },
  category_decision: {
    icon: Zap,
    message: "🎯 Category Decision",
    animation: "selection_highlight",
    color: "text-info",
    bgColor: "bg-info/20"
  },
  // Task flow
  task_identified: {
    icon: FileText,
    message: "📝 Task identified!",
    animation: "green_pulse",
    color: "text-success",
    bgColor: "bg-success/20"
  },
  extracting_details: {
    icon: Search,
    message: "⚙️ Extracting details...",
    animation: "spinner_animation",
    color: "text-primary-400",
    bgColor: "bg-primary-600/20"
  },
  auto_categorizing: {
    icon: FileText,
    message: "🏷️ Auto-categorizing...",
    animation: "tag_animations",
    color: "text-primary-400",
    bgColor: "bg-primary-600/20"
  },
  checking_dates: {
    icon: Calendar,
    message: "📅 Checking for dates...",
    animation: "calendar_scan",
    color: "text-primary-400",
    bgColor: "bg-primary-600/20"
  },
  scheduling: {
    icon: Calendar,
    message: "🗓️ Scheduling...",
    animation: "slide_to_date",
    color: "text-info",
    bgColor: "bg-info/20"
  },
  adding_to_today: {
    icon: Calendar,
    message: "📌 Adding to today...",
    animation: "drop_animation",
    color: "text-warning",
    bgColor: "bg-warning/20"
  },
  updating_task_list: {
    icon: FileText,
    message: "📋 Updating task list...",
    animation: "list_grow_animation",
    color: "text-primary-400",
    bgColor: "bg-primary-600/20"
  },
  syncing_calendar: {
    icon: Calendar,
    message: "📅 Syncing calendar...",
    animation: "sync_spinner",
    color: "text-primary-400",
    bgColor: "bg-primary-600/20"
  },
  task_added_successfully: {
    icon: CheckCircle,
    message: "✅ Task added successfully!",
    animation: "success_confetti",
    color: "text-success",
    bgColor: "bg-success/20"
  },
  // Event flow
  event_identified: {
    icon: Calendar,
    message: "📋 Event identified!",
    animation: "blue_pulse",
    color: "text-info",
    bgColor: "bg-info/20"
  },
  extracting_datetime: {
    icon: Calendar,
    message: "🕐 Extracting date/time...",
    animation: "clock_animation",
    color: "text-primary-400",
    bgColor: "bg-primary-600/20"
  },
  checking_datetime: {
    icon: Calendar,
    message: "📆 Checking datetime completeness...",
    animation: "info_check",
    color: "text-primary-400",
    bgColor: "bg-primary-600/20"
  },
  need_more_info: {
    icon: AlertCircle,
    message: "❓ Need more info...",
    animation: "input_prompt_slide",
    color: "text-warning",
    bgColor: "bg-warning/20"
  },
  waiting_for_response: {
    icon: AlertCircle,
    message: "⏳ Waiting for response...",
    animation: "breathing_animation",
    color: "text-warning",
    bgColor: "bg-warning/20"
  },
  adding_to_calendar: {
    icon: Calendar,
    message: "📅 Adding to calendar...",
    animation: "calendar_drop",
    color: "text-info",
    bgColor: "bg-info/20"
  },
  event_scheduled: {
    icon: CheckCircle,
    message: "✅ Event scheduled!",
    animation: "success_sparkle",
    color: "text-success",
    bgColor: "bg-success/20"
  },
  // AI Question flow
  question_identified: {
    icon: Brain,
    message: "❓ Question identified!",
    animation: "purple_pulse",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  analyzing_query_type: {
    icon: Brain,
    message: "🧠 Analyzing query type...",
    animation: "thinking_dots",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  query_type_decision: {
    icon: Zap,
    message: "🎯 Query Type Decision",
    animation: "tool_highlight",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  // Simple knowledge
  accessing_knowledge: {
    icon: Brain,
    message: "🤖 Accessing knowledge...",
    animation: "brain_glow",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  formatting_answer: {
    icon: FileText,
    message: "📝 Formatting answer...",
    animation: "text_typewriter",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  answer_ready: {
    icon: CheckCircle,
    message: "✅ Answer ready!",
    animation: "reveal_animation",
    color: "text-success",
    bgColor: "bg-success/20"
  },
  // Database search
  searching_database: {
    icon: Database,
    message: "🔍 Searching database...",
    animation: "radar_scan",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  generating_embeddings: {
    icon: Sparkles,
    message: "🧮 Generating embeddings...",
    animation: "matrix_animation",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  vector_searching: {
    icon: Search,
    message: "📊 Vector searching...",
    animation: "progress_dots",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  found_relevant_docs: {
    icon: FileText,
    message: "📄 Found relevant docs...",
    animation: "document_fly_in",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  processing_context: {
    icon: Brain,
    message: "🤖 Processing context...",
    animation: "loading_spinner",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  generating_answer: {
    icon: FileText,
    message: "📝 Generating answer...",
    animation: "typewriter_effect",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  database_search_complete: {
    icon: CheckCircle,
    message: "✅ Database search complete!",
    animation: "success_fade_in",
    color: "text-success",
    bgColor: "bg-success/20"
  },
  // Web search
  starting_web_search: {
    icon: Globe,
    message: "🌐 Starting web search...",
    animation: "globe_spin",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  calling_langsearch_api: {
    icon: Globe,
    message: "🔗 Calling LangSearch API...",
    animation: "api_pulse",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  processing_results: {
    icon: Search,
    message: "⚙️ Processing results...",
    animation: "gear_animation",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  summarizing_findings: {
    icon: Brain,
    message: "🤖 Summarizing findings...",
    animation: "text_processing",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  adding_sources: {
    icon: Globe,
    message: "🔗 Adding sources...",
    animation: "link_animations",
    color: "text-purple-400",
    bgColor: "bg-purple-600/20"
  },
  web_search_complete: {
    icon: CheckCircle,
    message: "✅ Web search complete!",
    animation: "success_burst",
    color: "text-success",
    bgColor: "bg-success/20"
  },
  // General
  complete: {
    icon: CheckCircle,
    message: "✅ Processing complete!",
    animation: "success_confetti",
    color: "text-success",
    bgColor: "bg-success/20"
  },
  error: {
    icon: AlertCircle,
    message: "❌ Error occurred",
    animation: "shake_animation",
    color: "text-error",
    bgColor: "bg-error/20"
  }
};

const VisualFeedback: React.FC<VisualFeedbackProps> = ({ 
  currentStep, 
  processingSteps, 
  isProcessing 
}) => {
  const currentStepConfig = stepAnimations[currentStep as keyof typeof stepAnimations] || stepAnimations.analyzing;
  const Icon = currentStepConfig.icon;

  return (
    <motion.div
      className="absolute inset-0 bg-primary-900/80 backdrop-blur-sm rounded-2xl flex items-center justify-center z-10"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div
        className={`${currentStepConfig.bgColor} backdrop-blur-md px-8 py-6 rounded-2xl flex items-center space-x-4 border border-white/10 shadow-2xl`}
        initial={{ scale: 0.8, y: 10 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.8, y: -10 }}
        transition={{ type: "spring", stiffness: 400, damping: 25 }}
      >
        {/* Animated Icon */}
        <motion.div
          className={`${currentStepConfig.color}`}
          animate={getIconAnimation(currentStepConfig.animation)}
          transition={{ duration: 1, repeat: Infinity }}
        >
          <Icon className="w-8 h-8" />
        </motion.div>

        {/* Message */}
        <motion.div
          className="flex flex-col"
          key={currentStep}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <span className={`text-lg font-medium ${currentStepConfig.color}`}>
            {currentStepConfig.message}
          </span>
          {processingSteps.length > 0 && (
            <span className="text-sm text-primary-400 mt-1">
              Step {processingSteps.length} of processing...
            </span>
          )}
        </motion.div>

        {/* Progress indicator */}
        <motion.div
          className="w-2 h-2 bg-current rounded-full"
          animate={{ scale: [1, 1.5, 1], opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity }}
        />
      </motion.div>
    </motion.div>
  );
};

// Helper function to get animation variants for icons
function getIconAnimation(animationType: string) {
  switch (animationType) {
    case 'typing_indicator':
      return { opacity: [1, 0.3, 1] };
    case 'brain_animation':
      return { scale: [1, 1.1, 1] };
    case 'green_pulse':
      return { scale: [1, 1.2, 1], color: ['#10b981', '#34d399', '#10b981'] };
    case 'blue_pulse':
      return { scale: [1, 1.2, 1], color: ['#3b82f6', '#60a5fa', '#3b82f6'] };
    case 'purple_pulse':
      return { scale: [1, 1.2, 1], color: ['#a855f7', '#c084fc', '#a855f7'] };
    case 'spinner_animation':
      return { rotate: 360 };
    case 'globe_spin':
      return { rotate: [0, 360] };
    case 'shake_animation':
      return { x: [-2, 2, -2, 2, 0] };
    case 'success_confetti':
      return { 
        scale: [1, 1.3, 1], 
        rotate: [0, 180, 360],
        color: ['#10b981', '#fbbf24', '#10b981']
      };
    default:
      return { scale: [1, 1.05, 1] };
  }
}

export default VisualFeedback;
