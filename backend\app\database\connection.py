"""SQLite database connection with WAL mode and async support."""

import logging
import os
from typing import AsyncGenerator

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import declarative_base, sessionmaker

from app.config import settings

logger = logging.getLogger(__name__)

# Create declarative base
Base = declarative_base()

# Database engines
engine = None
async_engine = None
SessionLocal = None
AsyncSessionLocal = None


async def init_database():
    """Initialize database with WAL mode and create tables."""
    global engine, async_engine, SessionLocal, AsyncSessionLocal
    
    # Ensure data directory exists
    db_path = settings.database_url.replace("sqlite:///", "")
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # Create sync engine for WAL mode setup
    engine = create_engine(
        settings.database_url,
        echo=settings.debug,
        pool_pre_ping=True,
        connect_args={"check_same_thread": False}
    )
    
    # Create async engine
    async_database_url = settings.database_url.replace("sqlite:///", "sqlite+aiosqlite:///")
    async_engine = create_async_engine(
        async_database_url,
        echo=settings.debug,
        pool_pre_ping=True
    )
    
    # Create session makers
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    AsyncSessionLocal = async_sessionmaker(
        async_engine, 
        class_=AsyncSession, 
        expire_on_commit=False
    )
    
    # Enable WAL mode for concurrent access
    with engine.connect() as conn:
        conn.execute(text("PRAGMA journal_mode=WAL"))
        conn.execute(text("PRAGMA synchronous=NORMAL"))
        conn.execute(text("PRAGMA cache_size=1000"))
        conn.execute(text("PRAGMA temp_store=memory"))
        conn.commit()
    
    logger.info("Database WAL mode enabled")
    
    # Import models to register them
    from app.models.database import TaskModel, EventModel, UserInputModel, EmbeddingModel
    
    # Create all tables
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("Database tables created")


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session."""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


def get_sync_session():
    """Get sync database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def close_database():
    """Close database connections."""
    if async_engine:
        await async_engine.dispose()
    if engine:
        engine.dispose()
    logger.info("Database connections closed")
