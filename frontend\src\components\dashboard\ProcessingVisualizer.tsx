import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  Zap, 
  Search, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  ArrowRight,
  Sparkles
} from 'lucide-react';

import { ProcessingUpdate } from '@/types';

interface ProcessingVisualizerProps {
  isProcessing: boolean;
  currentStep: string;
  processingSteps: ProcessingUpdate[];
  onComplete?: () => void;
}

const ProcessingVisualizer: React.FC<ProcessingVisualizerProps> = ({
  isProcessing,
  currentStep,
  processingSteps,
  onComplete,
}) => {
  const [visibleSteps, setVisibleSteps] = useState<ProcessingUpdate[]>([]);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  useEffect(() => {
    if (processingSteps.length > visibleSteps.length) {
      const newSteps = processingSteps.slice(visibleSteps.length);
      
      newSteps.forEach((step, index) => {
        setTimeout(() => {
          setVisibleSteps(prev => [...prev, step]);
          setCurrentStepIndex(prev => prev + 1);
        }, index * 800); // Stagger step animations
      });
    }
  }, [processingSteps, visibleSteps.length]);

  useEffect(() => {
    if (!isProcessing && visibleSteps.length > 0) {
      const timer = setTimeout(() => {
        onComplete?.();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [isProcessing, visibleSteps.length, onComplete]);

  const getStepIcon = (step: string) => {
    if (step.toLowerCase().includes('categoriz')) return Brain;
    if (step.toLowerCase().includes('search')) return Search;
    if (step.toLowerCase().includes('process')) return Zap;
    if (step.toLowerCase().includes('complet')) return CheckCircle;
    if (step.toLowerCase().includes('error')) return AlertCircle;
    return Sparkles;
  };

  const getStepColor = (step: string, isActive: boolean, isCompleted: boolean) => {
    if (step.toLowerCase().includes('error')) return 'text-error';
    if (isCompleted) return 'text-success';
    if (isActive) return 'text-info';
    return 'text-primary-400';
  };

  if (!isProcessing && visibleSteps.length === 0) {
    return null;
  }

  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-primary-800 rounded-2xl p-8 max-w-md w-full mx-4 border border-primary-700"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        {/* Header */}
        <div className="text-center mb-8">
          <motion.div
            className="w-16 h-16 bg-gradient-to-br from-info to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4"
            animate={{ 
              scale: [1, 1.1, 1],
              rotate: [0, 5, -5, 0]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Brain className="w-8 h-8 text-white" />
          </motion.div>
          
          <h2 className="text-xl font-semibold text-white mb-2">
            AI Processing
          </h2>
          
          <p className="text-primary-400 text-sm">
            Analyzing your input with artificial intelligence
          </p>
        </div>

        {/* Processing Steps */}
        <div className="space-y-4">
          <AnimatePresence>
            {visibleSteps.map((step, index) => {
              const Icon = getStepIcon(step.step);
              const isActive = index === currentStepIndex - 1 && isProcessing;
              const isCompleted = index < currentStepIndex - 1 || !isProcessing;
              const stepColor = getStepColor(step.step, isActive, isCompleted);

              return (
                <motion.div
                  key={`${step.step}-${index}`}
                  className="flex items-center gap-4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ 
                    duration: 0.5,
                    type: "spring",
                    stiffness: 300,
                    damping: 30
                  }}
                >
                  {/* Step Icon */}
                  <motion.div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      isCompleted ? 'bg-success/20' :
                      isActive ? 'bg-info/20' : 'bg-primary-700'
                    }`}
                    animate={isActive ? {
                      scale: [1, 1.2, 1],
                      rotate: [0, 360]
                    } : {}}
                    transition={isActive ? {
                      scale: { duration: 1, repeat: Infinity },
                      rotate: { duration: 2, repeat: Infinity, ease: "linear" }
                    } : {}}
                  >
                    {isActive ? (
                      <Loader2 className={`w-4 h-4 ${stepColor} animate-spin`} />
                    ) : (
                      <Icon className={`w-4 h-4 ${stepColor}`} />
                    )}
                  </motion.div>

                  {/* Step Text */}
                  <div className="flex-1">
                    <motion.p
                      className={`text-sm font-medium ${
                        isCompleted ? 'text-primary-200' :
                        isActive ? 'text-white' : 'text-primary-400'
                      }`}
                      animate={isActive ? {
                        opacity: [0.7, 1, 0.7]
                      } : {}}
                      transition={isActive ? {
                        duration: 1.5,
                        repeat: Infinity,
                        ease: "easeInOut"
                      } : {}}
                    >
                      {step.step}
                    </motion.p>
                    
                    {step.details && (
                      <p className="text-xs text-primary-500 mt-1">
                        {step.details}
                      </p>
                    )}
                  </div>

                  {/* Completion Indicator */}
                  <AnimatePresence>
                    {isCompleted && (
                      <motion.div
                        initial={{ scale: 0, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0, opacity: 0 }}
                        transition={{ 
                          type: "spring", 
                          stiffness: 500, 
                          damping: 30 
                        }}
                      >
                        <CheckCircle className="w-5 h-5 text-success" />
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {/* Arrow for active step */}
                  <AnimatePresence>
                    {isActive && (
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ 
                          opacity: 1, 
                          x: 0,
                          scale: [1, 1.1, 1]
                        }}
                        exit={{ opacity: 0, x: 10 }}
                        transition={{ 
                          scale: { duration: 1, repeat: Infinity }
                        }}
                      >
                        <ArrowRight className="w-4 h-4 text-info" />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>

        {/* Progress Bar */}
        <div className="mt-8">
          <div className="flex items-center justify-between text-xs text-primary-400 mb-2">
            <span>Progress</span>
            <span>
              {Math.round((currentStepIndex / Math.max(processingSteps.length, 1)) * 100)}%
            </span>
          </div>
          
          <div className="w-full bg-primary-700 rounded-full h-2 overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-info to-success"
              initial={{ width: 0 }}
              animate={{ 
                width: `${(currentStepIndex / Math.max(processingSteps.length, 1)) * 100}%` 
              }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
        </div>

        {/* Completion Message */}
        <AnimatePresence>
          {!isProcessing && visibleSteps.length > 0 && (
            <motion.div
              className="mt-6 text-center"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
            >
              <motion.div
                className="inline-flex items-center gap-2 text-success text-sm font-medium"
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 0.5 }}
              >
                <CheckCircle className="w-4 h-4" />
                Processing Complete
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
};

export default ProcessingVisualizer;
