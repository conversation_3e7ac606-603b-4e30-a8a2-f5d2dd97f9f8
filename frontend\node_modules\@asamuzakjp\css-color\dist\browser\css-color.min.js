var al=Object.defineProperty,to=t=>{throw TypeError(t)},ol=(t,e,n)=>e in t?al(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,K=(t,e,n)=>ol(t,typeof e!="symbol"?e+"":e,n),Hs=(t,e,n)=>e.has(t)||to("Cannot "+n),u=(t,e,n)=>(Hs(t,e,"read from private field"),n?n.call(t):e.get(t)),z=(t,e,n)=>e.has(t)?to("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),C=(t,e,n,r)=>(Hs(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),I=(t,e,n)=>(Hs(t,e,"access private method"),n),Us=(t,e,n,r)=>({set _(s){C(t,e,s,n)},get _(){return u(t,e,r)}});class dr extends Error{constructor(e,n,r,s){super(e),K(this,"sourceStart"),K(this,"sourceEnd"),K(this,"parserState"),this.name="ParseError",this.sourceStart=n,this.sourceEnd=r,this.parserState=s}}class an extends dr{constructor(e,n,r,s,a){super(e,n,r,s),K(this,"token"),this.token=a}}const qe={UnexpectedNewLineInString:"Unexpected newline while consuming a string token.",UnexpectedEOFInString:"Unexpected EOF while consuming a string token.",UnexpectedEOFInComment:"Unexpected EOF while consuming a comment.",UnexpectedEOFInURL:"Unexpected EOF while consuming a url token.",UnexpectedEOFInEscapedCodePoint:"Unexpected EOF while consuming an escaped code point.",UnexpectedCharacterInURL:"Unexpected character while consuming a url token.",InvalidEscapeSequenceInURL:"Invalid escape sequence while consuming a url token.",InvalidEscapeSequenceAfterBackslash:'Invalid escape sequence after "\\"'};function Ve(...t){let e="";for(let n=0;n<t.length;n++)e+=t[n][1];return e}const Mn=13,St=45,Dn=10,Bn=43,Rn=65533;function il(t){return t.source.codePointAt(t.cursor)===60&&t.source.codePointAt(t.cursor+1)===33&&t.source.codePointAt(t.cursor+2)===St&&t.source.codePointAt(t.cursor+3)===St}function ne(t){return t>=48&&t<=57}function ll(t){return t>=65&&t<=90}function cl(t){return t>=97&&t<=122}function bn(t){return t>=48&&t<=57||t>=97&&t<=102||t>=65&&t<=70}function ul(t){return cl(t)||ll(t)}function On(t){return ul(t)||hl(t)||t===95}function zs(t){return On(t)||ne(t)||t===St}function hl(t){return t===183||t===8204||t===8205||t===8255||t===8256||t===8204||192<=t&&t<=214||216<=t&&t<=246||248<=t&&t<=893||895<=t&&t<=8191||8304<=t&&t<=8591||11264<=t&&t<=12271||12289<=t&&t<=55295||63744<=t&&t<=64975||65008<=t&&t<=65533||t===0||!!Wn(t)||t>=65536}function Xr(t){return t===Dn||t===Mn||t===12}function wn(t){return t===32||t===Dn||t===9||t===Mn||t===12}function Wn(t){return t>=55296&&t<=57343}function Tn(t){return t.source.codePointAt(t.cursor)===92&&!Xr(t.source.codePointAt(t.cursor+1)??-1)}function Yr(t,e){return e.source.codePointAt(e.cursor)===St?e.source.codePointAt(e.cursor+1)===St||!!On(e.source.codePointAt(e.cursor+1)??-1)||e.source.codePointAt(e.cursor+1)===92&&!Xr(e.source.codePointAt(e.cursor+2)??-1):!!On(e.source.codePointAt(e.cursor)??-1)||Tn(e)}function eo(t){return t.source.codePointAt(t.cursor)===Bn||t.source.codePointAt(t.cursor)===St?!!ne(t.source.codePointAt(t.cursor+1)??-1)||t.source.codePointAt(t.cursor+1)===46&&ne(t.source.codePointAt(t.cursor+2)??-1):t.source.codePointAt(t.cursor)===46?ne(t.source.codePointAt(t.cursor+1)??-1):ne(t.source.codePointAt(t.cursor)??-1)}function fl(t){return t.source.codePointAt(t.cursor)===47&&t.source.codePointAt(t.cursor+1)===42}function pl(t){return t.source.codePointAt(t.cursor)===St&&t.source.codePointAt(t.cursor+1)===St&&t.source.codePointAt(t.cursor+2)===62}var v,x,Zr;function dl(t){switch(t){case v.OpenParen:return v.CloseParen;case v.CloseParen:return v.OpenParen;case v.OpenCurly:return v.CloseCurly;case v.CloseCurly:return v.OpenCurly;case v.OpenSquare:return v.CloseSquare;case v.CloseSquare:return v.OpenSquare;default:return null}}function ml(t){switch(t[0]){case v.OpenParen:return[v.CloseParen,")",-1,-1,void 0];case v.CloseParen:return[v.OpenParen,"(",-1,-1,void 0];case v.OpenCurly:return[v.CloseCurly,"}",-1,-1,void 0];case v.CloseCurly:return[v.OpenCurly,"{",-1,-1,void 0];case v.OpenSquare:return[v.CloseSquare,"]",-1,-1,void 0];case v.CloseSquare:return[v.OpenSquare,"[",-1,-1,void 0];default:return null}}function gl(t,e){for(e.advanceCodePoint(2);;){const n=e.readCodePoint();if(n===void 0){const r=[v.Comment,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new an(qe.UnexpectedEOFInComment,e.representationStart,e.representationEnd,["4.3.2. Consume comments","Unexpected EOF"],r)),r}if(n===42&&e.source.codePointAt(e.cursor)!==void 0&&e.source.codePointAt(e.cursor)===47){e.advanceCodePoint();break}}return[v.Comment,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0]}function Jr(t,e){const n=e.readCodePoint();if(n===void 0)return t.onParseError(new dr(qe.UnexpectedEOFInEscapedCodePoint,e.representationStart,e.representationEnd,["4.3.7. Consume an escaped code point","Unexpected EOF"])),Rn;if(bn(n)){const r=[n];let s;for(;(s=e.source.codePointAt(e.cursor))!==void 0&&bn(s)&&r.length<6;)r.push(s),e.advanceCodePoint();wn(e.source.codePointAt(e.cursor)??-1)&&(e.source.codePointAt(e.cursor)===Mn&&e.source.codePointAt(e.cursor+1)===Dn&&e.advanceCodePoint(),e.advanceCodePoint());const a=parseInt(String.fromCodePoint(...r),16);return a===0||Wn(a)||a>1114111?Rn:a}return n===0||Wn(n)?Rn:n}function Qr(t,e){const n=[];for(;;){const r=e.source.codePointAt(e.cursor)??-1;if(r===0||Wn(r))n.push(Rn),e.advanceCodePoint(+(r>65535)+1);else if(zs(r))n.push(r),e.advanceCodePoint(+(r>65535)+1);else{if(!Tn(e))return n;e.advanceCodePoint(),n.push(Jr(t,e))}}}function vl(t,e){e.advanceCodePoint();const n=e.source.codePointAt(e.cursor);if(n!==void 0&&(zs(n)||Tn(e))){let r=Zr.Unrestricted;Yr(0,e)&&(r=Zr.ID);const s=Qr(t,e);return[v.Hash,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...s),type:r}]}return[v.Delim,"#",e.representationStart,e.representationEnd,{value:"#"}]}function bl(t,e){let n=x.Integer;for(e.source.codePointAt(e.cursor)!==Bn&&e.source.codePointAt(e.cursor)!==St||e.advanceCodePoint();ne(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===46&&ne(e.source.codePointAt(e.cursor+1)??-1))for(e.advanceCodePoint(2),n=x.Number;ne(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===101||e.source.codePointAt(e.cursor)===69){if(ne(e.source.codePointAt(e.cursor+1)??-1))e.advanceCodePoint(2);else{if(e.source.codePointAt(e.cursor+1)!==St&&e.source.codePointAt(e.cursor+1)!==Bn||!ne(e.source.codePointAt(e.cursor+2)??-1))return n;e.advanceCodePoint(3)}for(n=x.Number;ne(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint()}return n}function js(t,e){let n;{const a=e.source.codePointAt(e.cursor);a===St?n="-":a===Bn&&(n="+")}const r=bl(0,e),s=parseFloat(e.source.slice(e.representationStart,e.representationEnd+1));if(Yr(0,e)){const a=Qr(t,e);return[v.Dimension,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n,type:r,unit:String.fromCodePoint(...a)}]}return e.source.codePointAt(e.cursor)===37?(e.advanceCodePoint(),[v.Percentage,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n}]):[v.Number,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n,type:r}]}function wl(t){for(;wn(t.source.codePointAt(t.cursor)??-1);)t.advanceCodePoint();return[v.Whitespace,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0]}(function(t){t.Comment="comment",t.AtKeyword="at-keyword-token",t.BadString="bad-string-token",t.BadURL="bad-url-token",t.CDC="CDC-token",t.CDO="CDO-token",t.Colon="colon-token",t.Comma="comma-token",t.Delim="delim-token",t.Dimension="dimension-token",t.EOF="EOF-token",t.Function="function-token",t.Hash="hash-token",t.Ident="ident-token",t.Number="number-token",t.Percentage="percentage-token",t.Semicolon="semicolon-token",t.String="string-token",t.URL="url-token",t.Whitespace="whitespace-token",t.OpenParen="(-token",t.CloseParen=")-token",t.OpenSquare="[-token",t.CloseSquare="]-token",t.OpenCurly="{-token",t.CloseCurly="}-token",t.UnicodeRange="unicode-range-token"})(v||(v={})),function(t){t.Integer="integer",t.Number="number"}(x||(x={})),function(t){t.Unrestricted="unrestricted",t.ID="id"}(Zr||(Zr={}));class $l{constructor(e){K(this,"cursor",0),K(this,"source",""),K(this,"representationStart",0),K(this,"representationEnd",-1),this.source=e}advanceCodePoint(e=1){this.cursor=this.cursor+e,this.representationEnd=this.cursor-1}readCodePoint(){const e=this.source.codePointAt(this.cursor);if(e!==void 0)return this.cursor=this.cursor+1,this.representationEnd=this.cursor-1,e}unreadCodePoint(e=1){this.cursor=this.cursor-e,this.representationEnd=this.cursor-1}resetRepresentation(){this.representationStart=this.cursor,this.representationEnd=-1}}function yl(t,e){let n="";const r=e.readCodePoint();for(;;){const s=e.readCodePoint();if(s===void 0){const a=[v.String,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new an(qe.UnexpectedEOFInString,e.representationStart,e.representationEnd,["4.3.5. Consume a string token","Unexpected EOF"],a)),a}if(Xr(s)){e.unreadCodePoint();const a=[v.BadString,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new an(qe.UnexpectedNewLineInString,e.representationStart,e.source.codePointAt(e.cursor)===Mn&&e.source.codePointAt(e.cursor+1)===Dn?e.representationEnd+2:e.representationEnd+1,["4.3.5. Consume a string token","Unexpected newline"],a)),a}if(s===r)return[v.String,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];if(s!==92)s===0||Wn(s)?n+=String.fromCodePoint(Rn):n+=String.fromCodePoint(s);else{if(e.source.codePointAt(e.cursor)===void 0)continue;if(Xr(e.source.codePointAt(e.cursor)??-1)){e.source.codePointAt(e.cursor)===Mn&&e.source.codePointAt(e.cursor+1)===Dn&&e.advanceCodePoint(),e.advanceCodePoint();continue}n+=String.fromCodePoint(Jr(t,e))}}}function Nl(t){return!(t.length!==3||t[0]!==117&&t[0]!==85||t[1]!==114&&t[1]!==82||t[2]!==108&&t[2]!==76)}function Gs(t,e){for(;;){const n=e.source.codePointAt(e.cursor);if(n===void 0)return;if(n===41)return void e.advanceCodePoint();Tn(e)?(e.advanceCodePoint(),Jr(t,e)):e.advanceCodePoint()}}function El(t,e){for(;wn(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();let n="";for(;;){if(e.source.codePointAt(e.cursor)===void 0){const a=[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new an(qe.UnexpectedEOFInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","Unexpected EOF"],a)),a}if(e.source.codePointAt(e.cursor)===41)return e.advanceCodePoint(),[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];if(wn(e.source.codePointAt(e.cursor)??-1)){for(e.advanceCodePoint();wn(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===void 0){const a=[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new an(qe.UnexpectedEOFInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","Consume as much whitespace as possible","Unexpected EOF"],a)),a}return e.source.codePointAt(e.cursor)===41?(e.advanceCodePoint(),[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}]):(Gs(t,e),[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0])}const s=e.source.codePointAt(e.cursor);if(s===34||s===39||s===40||(r=s??-1)===11||r===127||0<=r&&r<=8||14<=r&&r<=31){Gs(t,e);const a=[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new an(qe.UnexpectedCharacterInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token",`Unexpected U+0022 QUOTATION MARK ("), U+0027 APOSTROPHE ('), U+0028 LEFT PARENTHESIS (() or non-printable code point`],a)),a}if(s===92){if(Tn(e)){e.advanceCodePoint(),n+=String.fromCodePoint(Jr(t,e));continue}Gs(t,e);const a=[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new an(qe.InvalidEscapeSequenceInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],a)),a}e.source.codePointAt(e.cursor)===0||Wn(e.source.codePointAt(e.cursor)??-1)?(n+=String.fromCodePoint(Rn),e.advanceCodePoint()):(n+=e.source[e.cursor],e.advanceCodePoint())}var r}function qs(t,e){const n=Qr(t,e);if(e.source.codePointAt(e.cursor)!==40)return[v.Ident,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}];if(Nl(n)){e.advanceCodePoint();let r=0;for(;;){const s=wn(e.source.codePointAt(e.cursor)??-1),a=wn(e.source.codePointAt(e.cursor+1)??-1);if(s&&a){r+=1,e.advanceCodePoint(1);continue}const o=s?e.source.codePointAt(e.cursor+1):e.source.codePointAt(e.cursor);if(o===34||o===39)return r>0&&e.unreadCodePoint(r),[v.Function,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}];break}return El(t,e)}return e.advanceCodePoint(),[v.Function,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}]}function Cl(t){return!(t.source.codePointAt(t.cursor)!==117&&t.source.codePointAt(t.cursor)!==85||t.source.codePointAt(t.cursor+1)!==Bn||t.source.codePointAt(t.cursor+2)!==63&&!bn(t.source.codePointAt(t.cursor+2)??-1))}function kl(t,e){e.advanceCodePoint(2);const n=[],r=[];let s;for(;(s=e.source.codePointAt(e.cursor))!==void 0&&n.length<6&&bn(s);)n.push(s),e.advanceCodePoint();for(;(s=e.source.codePointAt(e.cursor))!==void 0&&n.length<6&&s===63;)r.length===0&&r.push(...n),n.push(48),r.push(70),e.advanceCodePoint();if(!r.length&&e.source.codePointAt(e.cursor)===St&&bn(e.source.codePointAt(e.cursor+1)??-1))for(e.advanceCodePoint();(s=e.source.codePointAt(e.cursor))!==void 0&&r.length<6&&bn(s);)r.push(s),e.advanceCodePoint();if(!r.length){const i=parseInt(String.fromCodePoint(...n),16);return[v.UnicodeRange,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{startOfRange:i,endOfRange:i}]}const a=parseInt(String.fromCodePoint(...n),16),o=parseInt(String.fromCodePoint(...r),16);return[v.UnicodeRange,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{startOfRange:a,endOfRange:o}]}function Ke(t,e){const n=no(t),r=[];for(;!n.endOfFile();)r.push(n.nextToken());return r.push(n.nextToken()),r}function no(t,e){const n=t.css.valueOf(),r=t.unicodeRangesAllowed??!1,s=new $l(n),a={onParseError:Fl};return{nextToken:function(){s.resetRepresentation();const o=s.source.codePointAt(s.cursor);if(o===void 0)return[v.EOF,"",-1,-1,void 0];if(o===47&&fl(s))return gl(a,s);if(r&&(o===117||o===85)&&Cl(s))return kl(0,s);if(On(o))return qs(a,s);if(ne(o))return js(a,s);switch(o){case 44:return s.advanceCodePoint(),[v.Comma,",",s.representationStart,s.representationEnd,void 0];case 58:return s.advanceCodePoint(),[v.Colon,":",s.representationStart,s.representationEnd,void 0];case 59:return s.advanceCodePoint(),[v.Semicolon,";",s.representationStart,s.representationEnd,void 0];case 40:return s.advanceCodePoint(),[v.OpenParen,"(",s.representationStart,s.representationEnd,void 0];case 41:return s.advanceCodePoint(),[v.CloseParen,")",s.representationStart,s.representationEnd,void 0];case 91:return s.advanceCodePoint(),[v.OpenSquare,"[",s.representationStart,s.representationEnd,void 0];case 93:return s.advanceCodePoint(),[v.CloseSquare,"]",s.representationStart,s.representationEnd,void 0];case 123:return s.advanceCodePoint(),[v.OpenCurly,"{",s.representationStart,s.representationEnd,void 0];case 125:return s.advanceCodePoint(),[v.CloseCurly,"}",s.representationStart,s.representationEnd,void 0];case 39:case 34:return yl(a,s);case 35:return vl(a,s);case Bn:case 46:return eo(s)?js(a,s):(s.advanceCodePoint(),[v.Delim,s.source[s.representationStart],s.representationStart,s.representationEnd,{value:s.source[s.representationStart]}]);case Dn:case Mn:case 12:case 9:case 32:return wl(s);case St:return eo(s)?js(a,s):pl(s)?(s.advanceCodePoint(3),[v.CDC,"-->",s.representationStart,s.representationEnd,void 0]):Yr(0,s)?qs(a,s):(s.advanceCodePoint(),[v.Delim,"-",s.representationStart,s.representationEnd,{value:"-"}]);case 60:return il(s)?(s.advanceCodePoint(4),[v.CDO,"<!--",s.representationStart,s.representationEnd,void 0]):(s.advanceCodePoint(),[v.Delim,"<",s.representationStart,s.representationEnd,{value:"<"}]);case 64:if(s.advanceCodePoint(),Yr(0,s)){const i=Qr(a,s);return[v.AtKeyword,s.source.slice(s.representationStart,s.representationEnd+1),s.representationStart,s.representationEnd,{value:String.fromCodePoint(...i)}]}return[v.Delim,"@",s.representationStart,s.representationEnd,{value:"@"}];case 92:{if(Tn(s))return qs(a,s);s.advanceCodePoint();const i=[v.Delim,"\\",s.representationStart,s.representationEnd,{value:"\\"}];return a.onParseError(new an(qe.InvalidEscapeSequenceAfterBackslash,s.representationStart,s.representationEnd,["4.3.1. Consume a token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],i)),i}}return s.advanceCodePoint(),[v.Delim,s.source[s.representationStart],s.representationStart,s.representationEnd,{value:s.source[s.representationStart]}]},endOfFile:function(){return s.source.codePointAt(s.cursor)===void 0}}}function Fl(){}function ro(t,e){const n=[];for(const i of e)n.push(i.codePointAt(0));const r=xl(n);r[0]===101&&ts(r,0,r[0]);const s=String.fromCodePoint(...r),a=t[4].signCharacter==="+"?t[4].signCharacter:"",o=t[4].value.toString();t[1]=`${a}${o}${s}`,t[4].unit=e}function xl(t){let e=0;t[0]===St&&t[1]===St?e=2:t[0]===St&&t[1]?(e=2,On(t[1])||(e+=ts(t,1,t[1]))):On(t[0])?e=1:(e=1,e+=ts(t,0,t[0]));for(let n=e;n<t.length;n++)zs(t[n])||(n+=ts(t,n,t[n]));return t}function ts(t,e,n){const r=n.toString(16),s=[];for(const o of r)s.push(o.codePointAt(0));const a=t[e+1];return e===t.length-1||a&&bn(a)?(t.splice(e,1,92,...s,32),1+s.length):(t.splice(e,1,92,...s),s.length)}const Sl=Object.values(v);function Vs(t){return!!Array.isArray(t)&&!(t.length<4)&&!!Sl.includes(t[0])&&typeof t[1]=="string"&&typeof t[2]=="number"&&typeof t[3]=="number"}function pt(t){if(!t)return!1;switch(t[0]){case v.Dimension:case v.Number:case v.Percentage:return!0;default:return!1}}function so(t){if(!t)return!1;switch(t[0]){case v.Whitespace:case v.Comment:return!0;default:return!1}}function be(t){return!!t&&t[0]===v.Comma}function ao(t){return!!t&&t[0]===v.Comment}function es(t){return!!t&&t[0]===v.Delim}function nt(t){return!!t&&t[0]===v.Dimension}function Se(t){return!!t&&t[0]===v.EOF}function Al(t){return!!t&&t[0]===v.Function}function Pl(t){return!!t&&t[0]===v.Hash}function bt(t){return!!t&&t[0]===v.Ident}function H(t){return!!t&&t[0]===v.Number}function tt(t){return!!t&&t[0]===v.Percentage}function Ks(t){return!!t&&t[0]===v.Whitespace}function oo(t){return!!t&&t[0]===v.OpenParen}function Ml(t){return!!t&&t[0]===v.CloseParen}function Dl(t){return!!t&&t[0]===v.OpenSquare}function Bl(t){return!!t&&t[0]===v.OpenCurly}var re;function io(t){let e=t.slice();return(n,r,s)=>{let a=-1;for(let o=e.indexOf(r);o<e.length&&(a=n.indexOf(e[o]),a===-1||a<s);o++);return a===-1||a===s&&r===n[s]&&(a++,a>=n.length)?-1:(e=n.slice(),a)}}function ns(t,e){const n=e[0];if(oo(n)||Bl(n)||Dl(n)){const r=Ol(t,e);return{advance:r.advance,node:r.node}}if(Al(n)){const r=Rl(t,e);return{advance:r.advance,node:r.node}}if(Ks(n)){const r=co(t,e);return{advance:r.advance,node:r.node}}if(ao(n)){const r=Wl(t,e);return{advance:r.advance,node:r.node}}return{advance:1,node:new j(n)}}(function(t){t.Function="function",t.SimpleBlock="simple-block",t.Whitespace="whitespace",t.Comment="comment",t.Token="token"})(re||(re={}));class lo{constructor(){K(this,"value",[])}indexOf(e){return this.value.indexOf(e)}at(e){if(typeof e=="number")return e<0&&(e=this.value.length+e),this.value[e]}forEach(e,n){if(this.value.length===0)return;const r=io(this.value);let s=0;for(;s<this.value.length;){const a=this.value[s];let o;if(n&&(o={...n}),e({node:a,parent:this,state:o},s)===!1)return!1;if(s=r(this.value,a,s),s===-1)break}}walk(e,n){this.value.length!==0&&this.forEach((r,s)=>e(r,s)!==!1&&(!("walk"in r.node)||!this.value.includes(r.node)||r.node.walk(e,r.state)!==!1)&&void 0,n)}}class Ut extends lo{constructor(e,n,r){super(),K(this,"type",re.Function),K(this,"name"),K(this,"endToken"),this.name=e,this.endToken=n,this.value=r}getName(){return this.name[4].value}normalize(){Se(this.endToken)&&(this.endToken=[v.CloseParen,")",-1,-1,void 0])}tokens(){return Se(this.endToken)?[this.name,...this.value.flatMap(e=>e.tokens())]:[this.name,...this.value.flatMap(e=>e.tokens()),this.endToken]}toString(){const e=this.value.map(n=>Vs(n)?Ve(n):n.toString()).join("");return Ve(this.name)+e+Ve(this.endToken)}toJSON(){return{type:this.type,name:this.getName(),tokens:this.tokens(),value:this.value.map(e=>e.toJSON())}}isFunctionNode(){return Ut.isFunctionNode(this)}static isFunctionNode(e){return!!e&&e instanceof Ut&&e.type===re.Function}}function Rl(t,e){const n=[];let r=1;for(;;){const s=e[r];if(!s||Se(s))return t.onParseError(new dr("Unexpected EOF while consuming a function.",e[0][2],e[e.length-1][3],["5.4.9. Consume a function","Unexpected EOF"])),{advance:e.length,node:new Ut(e[0],s,n)};if(Ml(s))return{advance:r+1,node:new Ut(e[0],s,n)};if(so(s)){const o=uo(t,e.slice(r));r+=o.advance,n.push(...o.nodes);continue}const a=ns(t,e.slice(r));r+=a.advance,n.push(a.node)}}class fr extends lo{constructor(e,n,r){super(),K(this,"type",re.SimpleBlock),K(this,"startToken"),K(this,"endToken"),this.startToken=e,this.endToken=n,this.value=r}normalize(){if(Se(this.endToken)){const e=ml(this.startToken);e&&(this.endToken=e)}}tokens(){return Se(this.endToken)?[this.startToken,...this.value.flatMap(e=>e.tokens())]:[this.startToken,...this.value.flatMap(e=>e.tokens()),this.endToken]}toString(){const e=this.value.map(n=>Vs(n)?Ve(n):n.toString()).join("");return Ve(this.startToken)+e+Ve(this.endToken)}toJSON(){return{type:this.type,startToken:this.startToken,tokens:this.tokens(),value:this.value.map(e=>e.toJSON())}}isSimpleBlockNode(){return fr.isSimpleBlockNode(this)}static isSimpleBlockNode(e){return!!e&&e instanceof fr&&e.type===re.SimpleBlock}}function Ol(t,e){const n=dl(e[0][0]);if(!n)throw new Error("Failed to parse, a mirror variant must exist for all block open tokens.");const r=[];let s=1;for(;;){const a=e[s];if(!a||Se(a))return t.onParseError(new dr("Unexpected EOF while consuming a simple block.",e[0][2],e[e.length-1][3],["5.4.8. Consume a simple block","Unexpected EOF"])),{advance:e.length,node:new fr(e[0],a,r)};if(a[0]===n)return{advance:s+1,node:new fr(e[0],a,r)};if(so(a)){const i=uo(t,e.slice(s));s+=i.advance,r.push(...i.nodes);continue}const o=ns(t,e.slice(s));s+=o.advance,r.push(o.node)}}class ee{constructor(e){K(this,"type",re.Whitespace),K(this,"value"),this.value=e}tokens(){return this.value}toString(){return Ve(...this.value)}toJSON(){return{type:this.type,tokens:this.tokens()}}isWhitespaceNode(){return ee.isWhitespaceNode(this)}static isWhitespaceNode(e){return!!e&&e instanceof ee&&e.type===re.Whitespace}}function co(t,e){let n=0;for(;;){const r=e[n];if(!Ks(r))return{advance:n,node:new ee(e.slice(0,n))};n++}}class pr{constructor(e){K(this,"type",re.Comment),K(this,"value"),this.value=e}tokens(){return[this.value]}toString(){return Ve(this.value)}toJSON(){return{type:this.type,tokens:this.tokens()}}isCommentNode(){return pr.isCommentNode(this)}static isCommentNode(e){return!!e&&e instanceof pr&&e.type===re.Comment}}function Wl(t,e){return{advance:1,node:new pr(e[0])}}function uo(t,e){const n=[];let r=0;for(;;)if(Ks(e[r])){const s=co(0,e.slice(r));r+=s.advance,n.push(s.node)}else{if(!ao(e[r]))return{advance:r,nodes:n};n.push(new pr(e[r])),r++}}class j{constructor(e){K(this,"type",re.Token),K(this,"value"),this.value=e}tokens(){return[this.value]}toString(){return this.value[1]}toJSON(){return{type:this.type,tokens:this.tokens()}}isTokenNode(){return j.isTokenNode(this)}static isTokenNode(e){return!!e&&e instanceof j&&e.type===re.Token}}function Tl(t,e){const n={onParseError:()=>{}},r=[...t];Se(r[r.length-1])&&r.push([v.EOF,"",r[r.length-1][2],r[r.length-1][3],void 0]);const s=ns(n,r);if(Se(r[Math.min(s.advance,r.length-1)]))return s.node;n.onParseError(new dr("Expected EOF after parsing a component value.",t[0][2],t[t.length-1][3],["5.3.9. Parse a component value","Expected EOF"]))}function Il(t,e){const n={onParseError:e?.onParseError??(()=>{})},r=[...t];if(t.length===0)return[];Se(r[r.length-1])&&r.push([v.EOF,"",r[r.length-1][2],r[r.length-1][3],void 0]);const s=[];let a=[],o=0;for(;;){if(!r[o]||Se(r[o]))return a.length&&s.push(a),s;if(be(r[o])){s.push(a),a=[],o++;continue}const i=ns(n,t.slice(o));a.push(i.node),o+=i.advance}}function Ll(t,e,n){if(t.length===0)return;const r=io(t);let s=0;for(;s<t.length;){const a=t[s];if(e({node:a,parent:{value:t},state:void 0},s)===!1)return!1;if(s=r(t,a,s),s===-1)break}}function _l(t,e,n){t.length!==0&&Ll(t,(r,s)=>e(r,s)!==!1&&(!("walk"in r.node)||!t.includes(r.node)||r.node.walk(e,r.state)!==!1)&&void 0)}function Hl(t,e){for(let n=0;n<t.length;n++)_l(t[n],(r,s)=>{if(typeof s!="number")return;const a=e(r.node);a&&(Array.isArray(a)?r.parent.value.splice(s,1,...a):r.parent.value.splice(s,1,a))});return t}function Ul(t){return fr.isSimpleBlockNode(t)}function se(t){return Ut.isFunctionNode(t)}function ae(t){return ee.isWhitespaceNode(t)}function oe(t){return pr.isCommentNode(t)}function $n(t){return ae(t)||oe(t)}function L(t){return j.isTokenNode(t)}const zl=/[A-Z]/g;function zt(t){return t.replace(zl,e=>String.fromCharCode(e.charCodeAt(0)+32))}const jl={cm:"px",in:"px",mm:"px",pc:"px",pt:"px",px:"px",q:"px",deg:"deg",grad:"deg",rad:"deg",turn:"deg",ms:"s",s:"s",hz:"hz",khz:"hz"},Gl=new Map([["cm",t=>t],["mm",t=>10*t],["q",t=>40*t],["in",t=>t/2.54],["pc",t=>t/2.54*6],["pt",t=>t/2.54*72],["px",t=>t/2.54*96]]),rs=new Map([["deg",t=>t],["grad",t=>t/.9],["rad",t=>t/180*Math.PI],["turn",t=>t/360]]),mr=new Map([["deg",t=>.9*t],["grad",t=>t],["rad",t=>.9*t/180*Math.PI],["turn",t=>.9*t/360]]),ql=new Map([["hz",t=>t],["khz",t=>t/1e3]]),Vl=new Map([["cm",t=>2.54*t],["mm",t=>25.4*t],["q",t=>25.4*t*4],["in",t=>t],["pc",t=>6*t],["pt",t=>72*t],["px",t=>96*t]]),Kl=new Map([["hz",t=>1e3*t],["khz",t=>t]]),Xl=new Map([["cm",t=>t/10],["mm",t=>t],["q",t=>4*t],["in",t=>t/25.4],["pc",t=>t/25.4*6],["pt",t=>t/25.4*72],["px",t=>t/25.4*96]]),Yl=new Map([["ms",t=>t],["s",t=>t/1e3]]),Zl=new Map([["cm",t=>t/6*2.54],["mm",t=>t/6*25.4],["q",t=>t/6*25.4*4],["in",t=>t/6],["pc",t=>t],["pt",t=>t/6*72],["px",t=>t/6*96]]),Jl=new Map([["cm",t=>t/72*2.54],["mm",t=>t/72*25.4],["q",t=>t/72*25.4*4],["in",t=>t/72],["pc",t=>t/72*6],["pt",t=>t],["px",t=>t/72*96]]),Ql=new Map([["cm",t=>t/96*2.54],["mm",t=>t/96*25.4],["q",t=>t/96*25.4*4],["in",t=>t/96],["pc",t=>t/96*6],["pt",t=>t/96*72],["px",t=>t]]),tc=new Map([["cm",t=>t/4/10],["mm",t=>t/4],["q",t=>t],["in",t=>t/4/25.4],["pc",t=>t/4/25.4*6],["pt",t=>t/4/25.4*72],["px",t=>t/4/25.4*96]]),ho=new Map([["deg",t=>180*t/Math.PI],["grad",t=>180*t/Math.PI/.9],["rad",t=>t],["turn",t=>180*t/Math.PI/360]]),ec=new Map([["ms",t=>1e3*t],["s",t=>t]]),gr=new Map([["deg",t=>360*t],["grad",t=>360*t/.9],["rad",t=>360*t/180*Math.PI],["turn",t=>t]]),fo=new Map([["cm",Gl],["mm",Xl],["q",tc],["in",Vl],["pc",Zl],["pt",Jl],["px",Ql],["ms",Yl],["s",ec],["deg",rs],["grad",mr],["rad",ho],["turn",gr],["hz",ql],["khz",Kl]]);function Jt(t,e){if(!nt(t)||!nt(e))return e;const n=zt(t[4].unit),r=zt(e[4].unit);if(n===r)return e;const s=fo.get(r);if(!s)return e;const a=s.get(n);if(!a)return e;const o=a(e[4].value),i=[v.Dimension,"",e[2],e[3],{...e[4],signCharacter:o<0?"-":void 0,type:Number.isInteger(o)?x.Integer:x.Number,value:o}];return ro(i,t[4].unit),i}function nc(t){if(!nt(t))return t;const e=zt(t[4].unit),n=jl[e];if(e===n)return t;const r=fo.get(e);if(!r)return t;const s=r.get(n);if(!s)return t;const a=s(t[4].value),o=[v.Dimension,"",t[2],t[3],{...t[4],signCharacter:a<0?"-":void 0,type:Number.isInteger(a)?x.Integer:x.Number,value:a}];return ro(o,n),o}function rc(t){if(t.length!==2)return-1;const e=t[0].value;let n=t[1].value;if(H(e)&&H(n)){const r=e[4].value+n[4].value;return new j([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number}])}if(tt(e)&&tt(n)){const r=e[4].value+n[4].value;return new j([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(nt(e)&&nt(n)&&(n=Jt(e,n),zt(e[4].unit)===zt(n[4].unit))){const r=e[4].value+n[4].value;return new j([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number,unit:e[4].unit}])}return-1}function sc(t){if(t.length!==2)return-1;const e=t[0].value,n=t[1].value;if(H(e)&&H(n)){const r=e[4].value/n[4].value;return new j([v.Number,r.toString(),e[2],n[3],{value:r,type:Number.isInteger(r)?x.Integer:x.Number}])}if(tt(e)&&H(n)){const r=e[4].value/n[4].value;return new j([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(nt(e)&&H(n)){const r=e[4].value/n[4].value;return new j([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:Number.isInteger(r)?x.Integer:x.Number,unit:e[4].unit}])}return-1}function yn(t){return!!t&&typeof t=="object"&&"inputs"in t&&Array.isArray(t.inputs)&&"operation"in t}function jt(t){if(t===-1)return-1;const e=[];for(let n=0;n<t.inputs.length;n++){const r=t.inputs[n];if(L(r)){e.push(r);continue}const s=jt(r);if(s===-1)return-1;e.push(s)}return t.operation(e)}function ac(t){if(t.length!==2)return-1;const e=t[0].value,n=t[1].value;if(H(e)&&H(n)){const r=e[4].value*n[4].value;return new j([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number}])}if(tt(e)&&H(n)){const r=e[4].value*n[4].value;return new j([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(H(e)&&tt(n)){const r=e[4].value*n[4].value;return new j([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(nt(e)&&H(n)){const r=e[4].value*n[4].value;return new j([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number,unit:e[4].unit}])}if(H(e)&&nt(n)){const r=e[4].value*n[4].value;return new j([v.Dimension,r.toString()+n[4].unit,e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number,unit:n[4].unit}])}return-1}function In(t,e){for(let n=0;n<t.length;n++){const r=t[n];if(!L(r))continue;const s=r.value;if(!bt(s))continue;const a=zt(s[4].value);switch(a){case"e":t.splice(n,1,new j([v.Number,Math.E.toString(),s[2],s[3],{value:Math.E,type:x.Number}]));break;case"pi":t.splice(n,1,new j([v.Number,Math.PI.toString(),s[2],s[3],{value:Math.PI,type:x.Number}]));break;case"infinity":t.splice(n,1,new j([v.Number,"infinity",s[2],s[3],{value:1/0,type:x.Number}]));break;case"-infinity":t.splice(n,1,new j([v.Number,"-infinity",s[2],s[3],{value:-1/0,type:x.Number}]));break;case"nan":t.splice(n,1,new j([v.Number,"NaN",s[2],s[3],{value:Number.NaN,type:x.Number}]));break;default:if(e.has(a)){const o=e.get(a);t.splice(n,1,new j(o))}}}return t}function ss(t){if(t.length!==1)return-1;const e=t[0].value;return pt(e)?t[0]:-1}function ie(t,e,n){return nt(e)?vr(t,e[4].unit,n):tt(e)?oc(t,n):H(e)?Ae(t,n):-1}function vr(t,e,n){const r=t.tokens();return{inputs:[new j([v.Dimension,n.toString()+e,r[0][2],r[r.length-1][3],{value:n,type:Number.isInteger(n)?x.Integer:x.Number,unit:e}])],operation:ss}}function oc(t,e){const n=t.tokens();return{inputs:[new j([v.Percentage,e.toString()+"%",n[0][2],n[n.length-1][3],{value:e}])],operation:ss}}function Ae(t,e){const n=t.tokens();return{inputs:[new j([v.Number,e.toString(),n[0][2],n[n.length-1][3],{value:e,type:Number.isInteger(e)?x.Integer:x.Number}])],operation:ss}}function ic(t,e){const n=e.value;return H(n)?vr(t,"rad",Math.acos(n[4].value)):-1}function lc(t,e){const n=e.value;return H(n)?vr(t,"rad",Math.asin(n[4].value)):-1}function cc(t,e){const n=e.value;return H(n)?vr(t,"rad",Math.atan(n[4].value)):-1}function as(t){return nt(t)||H(t)}function Xs(t){if(t.length===0)return!0;const e=t[0];if(!pt(e))return!1;if(t.length===1)return!0;if(nt(e)){const n=zt(e[4].unit);for(let r=1;r<t.length;r++){const s=t[r];if(e[0]!==s[0]||n!==zt(s[4].unit))return!1}return!0}for(let n=1;n<t.length;n++){const r=t[n];if(e[0]!==r[0])return!1}return!0}function Xe(t,e){return!!pt(t)&&(nt(t)?t[0]===e[0]&&zt(t[4].unit)===zt(e[4].unit):t[0]===e[0])}function uc(t,e,n){const r=e.value;if(!as(r))return-1;const s=Jt(r,n.value);return Xe(r,s)?vr(t,"rad",Math.atan2(r[4].value,s[4].value)):-1}function hc(t,e,n){const r=e.value;return!pt(r)||!n.rawPercentages&&tt(r)?-1:ie(t,r,Math.abs(r[4].value))}function fc(t,e,n,r,s){if(!L(e)||!L(n)||!L(r))return-1;const a=e.value;if(!pt(a)||!s.rawPercentages&&tt(a))return-1;const o=Jt(a,n.value);if(!Xe(a,o))return-1;const i=Jt(a,r.value);return Xe(a,i)?ie(t,a,Math.max(a[4].value,Math.min(o[4].value,i[4].value))):-1}function pc(t,e){const n=e.value;if(!as(n))return-1;let r=n[4].value;if(nt(n))switch(n[4].unit.toLowerCase()){case"rad":break;case"deg":r=rs.get("rad")(n[4].value);break;case"grad":r=mr.get("rad")(n[4].value);break;case"turn":r=gr.get("rad")(n[4].value);break;default:return-1}return r=Math.cos(r),Ae(t,r)}function dc(t,e){const n=e.value;return H(n)?Ae(t,Math.exp(n[4].value)):-1}function mc(t,e,n){if(!e.every(L))return-1;const r=e[0].value;if(!pt(r)||!n.rawPercentages&&tt(r))return-1;const s=e.map(i=>Jt(r,i.value));if(!Xs(s))return-1;const a=s.map(i=>i[4].value),o=Math.hypot(...a);return ie(t,r,o)}function po(t,e,n){if(!e.every(L))return-1;const r=e[0].value;if(!pt(r)||!n.rawPercentages&&tt(r))return-1;const s=e.map(i=>Jt(r,i.value));if(!Xs(s))return-1;const a=s.map(i=>i[4].value),o=Math.max(...a);return ie(t,r,o)}function mo(t,e,n){if(!e.every(L))return-1;const r=e[0].value;if(!pt(r)||!n.rawPercentages&&tt(r))return-1;const s=e.map(i=>Jt(r,i.value));if(!Xs(s))return-1;const a=s.map(i=>i[4].value),o=Math.min(...a);return ie(t,r,o)}function gc(t,e,n){const r=e.value;if(!pt(r))return-1;const s=Jt(r,n.value);if(!Xe(r,s))return-1;let a;return a=s[4].value===0?Number.NaN:Number.isFinite(r[4].value)&&(Number.isFinite(s[4].value)||(s[4].value!==Number.POSITIVE_INFINITY||r[4].value!==Number.NEGATIVE_INFINITY&&!Object.is(0*r[4].value,-0))&&(s[4].value!==Number.NEGATIVE_INFINITY||r[4].value!==Number.POSITIVE_INFINITY&&!Object.is(0*r[4].value,0)))?Number.isFinite(s[4].value)?(r[4].value%s[4].value+s[4].value)%s[4].value:r[4].value:Number.NaN,ie(t,r,a)}function vc(t,e,n){const r=e.value,s=n.value;return!H(r)||!Xe(r,s)?-1:Ae(t,Math.pow(r[4].value,s[4].value))}function bc(t,e,n){const r=e.value;if(!pt(r))return-1;const s=Jt(r,n.value);if(!Xe(r,s))return-1;let a;return a=s[4].value===0?Number.NaN:Number.isFinite(r[4].value)?Number.isFinite(s[4].value)?r[4].value%s[4].value:r[4].value:Number.NaN,ie(t,r,a)}function wc(t,e,n,r,s){const a=n.value;if(!pt(a)||!s.rawPercentages&&tt(a))return-1;const o=Jt(a,r.value);if(!Xe(a,o))return-1;let i;if(o[4].value===0)i=Number.NaN;else if(Number.isFinite(a[4].value)||Number.isFinite(o[4].value))if(!Number.isFinite(a[4].value)&&Number.isFinite(o[4].value))i=a[4].value;else if(Number.isFinite(a[4].value)&&!Number.isFinite(o[4].value))switch(e){case"down":i=a[4].value<0?-1/0:Object.is(-0,0*a[4].value)?-0:0;break;case"up":i=a[4].value>0?1/0:Object.is(0,0*a[4].value)?0:-0;break;default:i=Object.is(0,0*a[4].value)?0:-0}else if(Number.isFinite(o[4].value))switch(e){case"down":i=Math.floor(a[4].value/o[4].value)*o[4].value;break;case"up":i=Math.ceil(a[4].value/o[4].value)*o[4].value;break;case"to-zero":i=Math.trunc(a[4].value/o[4].value)*o[4].value;break;default:{let c=Math.floor(a[4].value/o[4].value)*o[4].value,l=Math.ceil(a[4].value/o[4].value)*o[4].value;if(c>l){const d=c;c=l,l=d}const h=Math.abs(a[4].value-c),f=Math.abs(a[4].value-l);i=h===f?l:h<f?c:l;break}}else i=a[4].value;else i=Number.NaN;return ie(t,a,i)}function $c(t,e,n){const r=e.value;return!pt(r)||!n.rawPercentages&&tt(r)?-1:Ae(t,Math.sign(r[4].value))}function yc(t,e){const n=e.value;if(!as(n))return-1;let r=n[4].value;if(nt(n))switch(zt(n[4].unit)){case"rad":break;case"deg":r=rs.get("rad")(n[4].value);break;case"grad":r=mr.get("rad")(n[4].value);break;case"turn":r=gr.get("rad")(n[4].value);break;default:return-1}return r=Math.sin(r),Ae(t,r)}function Nc(t,e){const n=e.value;return H(n)?Ae(t,Math.sqrt(n[4].value)):-1}function Ec(t,e){const n=e.value;if(!as(n))return-1;const r=n[4].value;let s=0,a=n[4].value;if(nt(n))switch(zt(n[4].unit)){case"rad":s=ho.get("deg")(r);break;case"deg":s=r,a=rs.get("rad")(r);break;case"grad":s=mr.get("deg")(r),a=mr.get("rad")(r);break;case"turn":s=gr.get("deg")(r),a=gr.get("rad")(r);break;default:return-1}const o=s/90;return a=s%90==0&&o%2!=0?o>0?1/0:-1/0:Math.tan(a),Ae(t,a)}function Cc(t){if(t.length!==2)return-1;const e=t[0].value;let n=t[1].value;if(H(e)&&H(n)){const r=e[4].value-n[4].value;return new j([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number}])}if(tt(e)&&tt(n)){const r=e[4].value-n[4].value;return new j([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(nt(e)&&nt(n)&&(n=Jt(e,n),zt(e[4].unit)===zt(n[4].unit))){const r=e[4].value-n[4].value;return new j([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number,unit:e[4].unit}])}return-1}function kc(t,e){if(e.length===1){const n=e[0];if(!n||!L(n))return-1;const r=n.value;return H(r)?Ae(t,Math.log(r[4].value)):-1}if(e.length===2){const n=e[0];if(!n||!L(n))return-1;const r=n.value;if(!H(r))return-1;const s=e[1];if(!s||!L(s))return-1;const a=s.value;return H(a)?Ae(t,Math.log(r[4].value)/Math.log(a[4].value)):-1}return-1}const Fc=/^none$/i;function Ys(t){if(Array.isArray(t)){const n=t.filter(r=>!(ae(r)&&oe(r)));return n.length===1&&Ys(n[0])}if(!L(t))return!1;const e=t.value;return!!bt(e)&&Fc.test(e[4].value)}const xc=String.fromCodePoint(0);function Sc(t,e,n,r,s,a){var o;if(e.fixed===-1&&!a.randomCaching)return-1;a.randomCaching||(a.randomCaching={propertyName:"",propertyN:0,elementID:"",documentID:""}),a.randomCaching&&!a.randomCaching.propertyN&&(a.randomCaching.propertyN=0);const i=n.value;if(!pt(i))return-1;const c=Jt(i,r.value);if(!Xe(i,c))return-1;let l=null;if(s&&(l=Jt(i,s.value),!Xe(i,l)))return-1;if(!Number.isFinite(i[4].value)||!Number.isFinite(c[4].value)||!Number.isFinite(c[4].value-i[4].value))return ie(t,i,Number.NaN);if(l&&!Number.isFinite(l[4].value))return ie(t,i,i[4].value);const h=e.fixed===-1?Ac(Pc([e.dashedIdent?e.dashedIdent:`${(o=a.randomCaching)==null?void 0:o.propertyName} ${a.randomCaching.propertyN++}`,e.elementShared?"":a.randomCaching.elementID,a.randomCaching.documentID].join(xc))):()=>e.fixed;let f=i[4].value,d=c[4].value;if(f>d&&([f,d]=[d,f]),l&&(l[4].value<=0||Math.abs(f-d)/l[4].value>1e10)&&(l=null),l){const m=Math.max(l[4].value/1e3,1e-9),$=[f];let b=0;for(;;){b+=l[4].value;const N=f+b;if(!(N+m<d)){$.push(d);break}if($.push(N),N+l[4].value-m>d)break}const w=h();return ie(t,i,Number($[Math.floor($.length*w)].toFixed(5)))}const p=h();return ie(t,i,Number((p*(d-f)+f).toFixed(5)))}function Ac(t=.34944106645296036,e=.19228640875738723,n=.8784393832007205,r=.04850964319275053){return()=>{const s=((t|=0)+(e|=0)|0)+(r|=0)|0;return r=r+1|0,t=e^e>>>9,e=(n|=0)+(n<<3)|0,n=(n=n<<21|n>>>11)+s|0,(s>>>0)/4294967296}}function Pc(t){let e=0,n=0,r=0;e=~e;for(let s=0,a=t.length;s<a;s++)r=255&(e^t.charCodeAt(s)),n=+("0x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substring(9*r,9*r+8)),e=e>>>8^n;return~e>>>0}const Zs=new Map([["abs",function(t,e,n){return Pe(t,e,n,hc)}],["acos",function(t,e,n){return Pe(t,e,n,ic)}],["asin",function(t,e,n){return Pe(t,e,n,lc)}],["atan",function(t,e,n){return Pe(t,e,n,cc)}],["atan2",function(t,e,n){return os(t,e,n,uc)}],["calc",Wt],["clamp",function(t,e,n){const r=In([...t.value.filter(m=>!$n(m))],e),s=[],a=[],o=[];{let m=s;for(let $=0;$<r.length;$++){const b=r[$];if(L(b)&&be(b.value)){if(m===o)return-1;if(m===a){m=o;continue}if(m===s){m=a;continue}return-1}m.push(b)}}const i=Ys(s),c=Ys(o);if(i&&c)return Wt(Qt(a),e,n);const l=jt(Wt(Qt(a),e,n));if(l===-1)return-1;if(i){const m=jt(Wt(Qt(o),e,n));return m===-1?-1:mo((h=l,f=m,new Ut([v.Function,"min(",-1,-1,{value:"min"}],[v.CloseParen,")",-1,-1,void 0],[h,new j([v.Comma,",",-1,-1,void 0]),f])),[l,m],n)}if(c){const m=jt(Wt(Qt(s),e,n));return m===-1?-1:po(Oc(m,l),[m,l],n)}var h,f;const d=jt(Wt(Qt(s),e,n));if(d===-1)return-1;const p=jt(Wt(Qt(o),e,n));return p===-1?-1:fc(t,d,l,p,n)}],["cos",function(t,e,n){return Pe(t,e,n,pc)}],["exp",function(t,e,n){return Pe(t,e,n,dc)}],["hypot",function(t,e,n){return is(t,t.value,e,n,mc)}],["log",function(t,e,n){return is(t,t.value,e,n,kc)}],["max",function(t,e,n){return is(t,t.value,e,n,po)}],["min",function(t,e,n){return is(t,t.value,e,n,mo)}],["mod",function(t,e,n){return os(t,e,n,gc)}],["pow",function(t,e,n){return os(t,e,n,vc)}],["random",function(t,e,n){const r=Rc(t.value.filter(h=>!$n(h)),e,n);if(r===-1)return-1;const[s,a]=r,o=go(a,e,n);if(o===-1)return-1;const[i,c,l]=o;return!i||!c?-1:Sc(t,s,i,c,l,n)}],["rem",function(t,e,n){return os(t,e,n,bc)}],["round",function(t,e,n){const r=In([...t.value.filter(h=>!$n(h))],e);let s="",a=!1;const o=[],i=[];{let h=o;for(let f=0;f<r.length;f++){const d=r[f];if(!s&&o.length===0&&i.length===0&&L(d)&&bt(d.value)){const p=d.value[4].value.toLowerCase();if(Bc.has(p)){s=p;continue}}if(L(d)&&be(d.value)){if(h===i)return-1;if(h===o&&s&&o.length===0)continue;if(h===o){a=!0,h=i;continue}return-1}h.push(d)}}const c=jt(Wt(Qt(o),e,n));if(c===-1)return-1;a||i.length!==0||i.push(new j([v.Number,"1",-1,-1,{value:1,type:x.Integer}]));const l=jt(Wt(Qt(i),e,n));return l===-1?-1:(s||(s="nearest"),wc(t,s,c,l,n))}],["sign",function(t,e,n){return Pe(t,e,n,$c)}],["sin",function(t,e,n){return Pe(t,e,n,yc)}],["sqrt",function(t,e,n){return Pe(t,e,n,Nc)}],["tan",function(t,e,n){return Pe(t,e,n,Ec)}]]);function Wt(t,e,n){const r=In([...t.value.filter(a=>!$n(a))],e);if(r.length===1&&L(r[0]))return{inputs:[r[0]],operation:ss};let s=0;for(;s<r.length;){const a=r[s];if(Ul(a)&&oo(a.startToken)){const o=Wt(a,e,n);if(o===-1)return-1;r.splice(s,1,o)}else if(se(a)){const o=Zs.get(a.getName().toLowerCase());if(!o)return-1;const i=o(a,e,n);if(i===-1)return-1;r.splice(s,1,i)}else s++}if(s=0,r.length===1&&yn(r[0]))return r[0];for(;s<r.length;){const a=r[s];if(!a||!L(a)&&!yn(a)){s++;continue}const o=r[s+1];if(!o||!L(o)){s++;continue}const i=o.value;if(!es(i)||i[4].value!=="*"&&i[4].value!=="/"){s++;continue}const c=r[s+2];if(!c||!L(c)&&!yn(c))return-1;i[4].value!=="*"?i[4].value!=="/"?s++:r.splice(s,3,{inputs:[a,c],operation:sc}):r.splice(s,3,{inputs:[a,c],operation:ac})}if(s=0,r.length===1&&yn(r[0]))return r[0];for(;s<r.length;){const a=r[s];if(!a||!L(a)&&!yn(a)){s++;continue}const o=r[s+1];if(!o||!L(o)){s++;continue}const i=o.value;if(!es(i)||i[4].value!=="+"&&i[4].value!=="-"){s++;continue}const c=r[s+2];if(!c||!L(c)&&!yn(c))return-1;i[4].value!=="+"?i[4].value!=="-"?s++:r.splice(s,3,{inputs:[a,c],operation:Cc}):r.splice(s,3,{inputs:[a,c],operation:rc})}return r.length===1&&yn(r[0])?r[0]:-1}function Pe(t,e,n,r){const s=Mc(t.value,e,n);return s===-1?-1:r(t,s,n)}function Mc(t,e,n){const r=jt(Wt(Qt(In([...t.filter(s=>!$n(s))],e)),e,n));return r===-1?-1:r}function os(t,e,n,r){const s=Dc(t.value,e,n);if(s===-1)return-1;const[a,o]=s;return r(t,a,o,n)}function Dc(t,e,n){const r=In([...t.filter(c=>!$n(c))],e),s=[],a=[];{let c=s;for(let l=0;l<r.length;l++){const h=r[l];if(L(h)&&be(h.value)){if(c===a)return-1;if(c===s){c=a;continue}return-1}c.push(h)}}const o=jt(Wt(Qt(s),e,n));if(o===-1)return-1;const i=jt(Wt(Qt(a),e,n));return i===-1?-1:[o,i]}function is(t,e,n,r,s){const a=go(t.value,n,r);return a===-1?-1:s(t,a,r)}function go(t,e,n){const r=In([...t.filter(a=>!$n(a))],e),s=[];{const a=[];let o=[];for(let i=0;i<r.length;i++){const c=r[i];L(c)&&be(c.value)?(a.push(o),o=[]):o.push(c)}a.push(o);for(let i=0;i<a.length;i++){if(a[i].length===0)return-1;const c=jt(Wt(Qt(a[i]),e,n));if(c===-1)return-1;s.push(c)}}return s}const Bc=new Set(["nearest","up","down","to-zero"]);function Rc(t,e,n){const r={isAuto:!1,dashedIdent:"",fixed:-1,elementShared:!1},s=t[0];if(!L(s)||!bt(s.value))return[r,t];for(let a=0;a<t.length;a++){const o=t[a];if(!L(o))return-1;if(be(o.value))return[r,t.slice(a+1)];if(!bt(o.value))return-1;const i=o.value[4].value.toLowerCase();if(i!=="element-shared")if(i!=="fixed")if(i!=="auto"){if(i.startsWith("--")){if(r.fixed!==-1||r.isAuto)return-1;r.dashedIdent=i}}else{if(r.fixed!==-1||r.dashedIdent)return-1;r.isAuto=!0}else{if(r.elementShared||r.dashedIdent||r.isAuto)return-1;a++;const c=t[a];if(!c)return-1;const l=jt(Wt(Qt([c]),e,n));if(l===-1||!H(l.value)||l.value[4].value<0||l.value[4].value>1)return-1;r.fixed=Math.max(0,Math.min(l.value[4].value,1-1e-9))}else{if(r.fixed!==-1)return-1;r.elementShared=!0}}return-1}function Qt(t){return new Ut([v.Function,"calc(",-1,-1,{value:"calc"}],[v.CloseParen,")",-1,-1,void 0],t)}function Oc(t,e){return new Ut([v.Function,"max(",-1,-1,{value:"max"}],[v.CloseParen,")",-1,-1,void 0],[t,new j([v.Comma,",",-1,-1,void 0]),e])}function Wc(t){if(t===-1)return-1;if(se(t))return t;const e=t.value;return pt(e)&&Number.isNaN(e[4].value)?H(e)?new Ut([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new j([v.Ident,"NaN",e[2],e[3],{value:"NaN"}])]):nt(e)?new Ut([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new j([v.Ident,"NaN",e[2],e[3],{value:"NaN"}]),new ee([[v.Whitespace," ",e[2],e[3],void 0]]),new j([v.Delim,"*",e[2],e[3],{value:"*"}]),new ee([[v.Whitespace," ",e[2],e[3],void 0]]),new j([v.Dimension,"1"+e[4].unit,e[2],e[3],{value:1,type:x.Integer,unit:e[4].unit}])]):tt(e)?new Ut([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new j([v.Ident,"NaN",e[2],e[3],{value:"NaN"}]),new ee([[v.Whitespace," ",e[2],e[3],void 0]]),new j([v.Delim,"*",e[2],e[3],{value:"*"}]),new ee([[v.Whitespace," ",e[2],e[3],void 0]]),new j([v.Percentage,"1%",e[2],e[3],{value:1}])]):-1:t}function Tc(t){if(t===-1)return-1;if(se(t))return t;const e=t.value;if(!pt(e)||Number.isFinite(e[4].value)||Number.isNaN(e[4].value))return t;let n="";return Number.NEGATIVE_INFINITY===e[4].value&&(n="-"),H(e)?new Ut([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new j([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}])]):nt(e)?new Ut([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new j([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}]),new ee([[v.Whitespace," ",e[2],e[3],void 0]]),new j([v.Delim,"*",e[2],e[3],{value:"*"}]),new ee([[v.Whitespace," ",e[2],e[3],void 0]]),new j([v.Dimension,"1"+e[4].unit,e[2],e[3],{value:1,type:x.Integer,unit:e[4].unit}])]):new Ut([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new j([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}]),new ee([[v.Whitespace," ",e[2],e[3],void 0]]),new j([v.Delim,"*",e[2],e[3],{value:"*"}]),new ee([[v.Whitespace," ",e[2],e[3],void 0]]),new j([v.Percentage,"1%",e[2],e[3],{value:1}])])}function Ic(t){if(t===-1)return-1;if(se(t))return t;const e=t.value;return pt(e)&&Object.is(-0,e[4].value)&&(e[1]==="-0"||(tt(e)?e[1]="-0%":nt(e)?e[1]="-0"+e[4].unit:e[1]="-0")),t}function Lc(t,e=13){if(t===-1)return-1;if(e<=0||se(t))return t;const n=t.value;if(!pt(n)||Number.isInteger(n[4].value))return t;const r=Number(n[4].value.toFixed(e)).toString();return H(n)?n[1]=r:tt(n)?n[1]=r+"%":nt(n)&&(n[1]=r+n[4].unit),t}function _c(t){return t===-1?-1:(se(t)||nt(t.value)&&(t.value=nc(t.value)),t)}function Hc(t,e){let n=t;return e!=null&&e.toCanonicalUnits&&(n=_c(n)),n=Lc(n,e?.precision),n=Ic(n),e!=null&&e.censorIntoStandardRepresentableValues||(n=Wc(n),n=Tc(n)),n}function Uc(t){const e=new Map;if(!t)return e;for(const[n,r]of t)if(Vs(r))e.set(n,r);else if(typeof r=="string"){const s=no({css:r}),a=s.nextToken();if(s.nextToken(),!s.endOfFile()||!pt(a))continue;e.set(n,a)}return e}function Tt(t,e){return br(Il(Ke({css:t}),{}),e).map(n=>n.map(r=>Ve(...r.tokens())).join("")).join(",")}function br(t,e){const n=Uc(e?.globals);return Hl(t,r=>{if(!se(r))return;const s=Zs.get(r.getName().toLowerCase());if(!s)return;const a=Hc(jt(s(r,n,e??{})),e);return a!==-1?a:void 0})}const ls=new Set(Zs.keys()),Ln=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,vo=new Set,Js=typeof process=="object"&&process?process:{},bo=(t,e,n,r)=>{typeof Js.emitWarning=="function"?Js.emitWarning(t,e,n,r):console.error(`[${n}] ${e}: ${t}`)};let cs=globalThis.AbortController,wo=globalThis.AbortSignal;var $o;if(typeof cs>"u"){wo=class{constructor(){K(this,"onabort"),K(this,"_onabort",[]),K(this,"reason"),K(this,"aborted",!1)}addEventListener(n,r){this._onabort.push(r)}},cs=class{constructor(){K(this,"signal",new wo),e()}abort(n){var r,s;if(!this.signal.aborted){this.signal.reason=n,this.signal.aborted=!0;for(const a of this.signal._onabort)a(n);(s=(r=this.signal).onabort)==null||s.call(r,n)}}};let t=(($o=Js.env)==null?void 0:$o.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const e=()=>{t&&(t=!1,bo("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",e))}}const zc=t=>!vo.has(t),on=t=>t&&t===Math.floor(t)&&t>0&&isFinite(t),yo=t=>on(t)?t<=Math.pow(2,8)?Uint8Array:t<=Math.pow(2,16)?Uint16Array:t<=Math.pow(2,32)?Uint32Array:t<=Number.MAX_SAFE_INTEGER?us:null:null;class us extends Array{constructor(e){super(e),this.fill(0)}}var wr;const No=class Kr{constructor(e,n){if(K(this,"heap"),K(this,"length"),!u(Kr,wr))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new n(e),this.length=0}static create(e){const n=yo(e);if(!n)return[];C(Kr,wr,!0);const r=new Kr(e,n);return C(Kr,wr,!1),r}push(e){this.heap[this.length++]=e}pop(){return this.heap[--this.length]}};wr=new WeakMap,z(No,wr,!1);let jc=No;var Eo,Co,Me,le,De,Be,$r,yr,Ct,ce,yt,it,X,Gt,ue,It,kt,Re,Ft,Oe,We,he,Te,Nn,qt,W,Qs,_n,ln,hs,fe,ko,Hn,Nr,fs,cn,un,ta,ps,ds,at,ea,Er,hn,na;const Gc=class sl{constructor(e){z(this,W),z(this,Me),z(this,le),z(this,De),z(this,Be),z(this,$r),z(this,yr),K(this,"ttl"),K(this,"ttlResolution"),K(this,"ttlAutopurge"),K(this,"updateAgeOnGet"),K(this,"updateAgeOnHas"),K(this,"allowStale"),K(this,"noDisposeOnSet"),K(this,"noUpdateTTL"),K(this,"maxEntrySize"),K(this,"sizeCalculation"),K(this,"noDeleteOnFetchRejection"),K(this,"noDeleteOnStaleGet"),K(this,"allowStaleOnFetchAbort"),K(this,"allowStaleOnFetchRejection"),K(this,"ignoreFetchAbort"),z(this,Ct),z(this,ce),z(this,yt),z(this,it),z(this,X),z(this,Gt),z(this,ue),z(this,It),z(this,kt),z(this,Re),z(this,Ft),z(this,Oe),z(this,We),z(this,he),z(this,Te),z(this,Nn),z(this,qt),z(this,_n,()=>{}),z(this,ln,()=>{}),z(this,hs,()=>{}),z(this,fe,()=>!1),z(this,Hn,R=>{}),z(this,Nr,(R,U,G)=>{}),z(this,fs,(R,U,G,Y)=>{if(G||Y)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0}),K(this,Eo,"LRUCache");const{max:n=0,ttl:r,ttlResolution:s=1,ttlAutopurge:a,updateAgeOnGet:o,updateAgeOnHas:i,allowStale:c,dispose:l,disposeAfter:h,noDisposeOnSet:f,noUpdateTTL:d,maxSize:p=0,maxEntrySize:m=0,sizeCalculation:$,fetchMethod:b,memoMethod:w,noDeleteOnFetchRejection:N,noDeleteOnStaleGet:E,allowStaleOnFetchRejection:D,allowStaleOnFetchAbort:k,ignoreFetchAbort:O}=e;if(n!==0&&!on(n))throw new TypeError("max option must be a nonnegative integer");const A=n?yo(n):Array;if(!A)throw new Error("invalid max value: "+n);if(C(this,Me,n),C(this,le,p),this.maxEntrySize=m||u(this,le),this.sizeCalculation=$,this.sizeCalculation){if(!u(this,le)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(w!==void 0&&typeof w!="function")throw new TypeError("memoMethod must be a function if defined");if(C(this,yr,w),b!==void 0&&typeof b!="function")throw new TypeError("fetchMethod must be a function if specified");if(C(this,$r,b),C(this,Nn,!!b),C(this,yt,new Map),C(this,it,new Array(n).fill(void 0)),C(this,X,new Array(n).fill(void 0)),C(this,Gt,new A(n)),C(this,ue,new A(n)),C(this,It,0),C(this,kt,0),C(this,Re,jc.create(n)),C(this,Ct,0),C(this,ce,0),typeof l=="function"&&C(this,De,l),typeof h=="function"?(C(this,Be,h),C(this,Ft,[])):(C(this,Be,void 0),C(this,Ft,void 0)),C(this,Te,!!u(this,De)),C(this,qt,!!u(this,Be)),this.noDisposeOnSet=!!f,this.noUpdateTTL=!!d,this.noDeleteOnFetchRejection=!!N,this.allowStaleOnFetchRejection=!!D,this.allowStaleOnFetchAbort=!!k,this.ignoreFetchAbort=!!O,this.maxEntrySize!==0){if(u(this,le)!==0&&!on(u(this,le)))throw new TypeError("maxSize must be a positive integer if specified");if(!on(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");I(this,W,ko).call(this)}if(this.allowStale=!!c,this.noDeleteOnStaleGet=!!E,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!i,this.ttlResolution=on(s)||s===0?s:1,this.ttlAutopurge=!!a,this.ttl=r||0,this.ttl){if(!on(this.ttl))throw new TypeError("ttl must be a positive integer if specified");I(this,W,Qs).call(this)}if(u(this,Me)===0&&this.ttl===0&&u(this,le)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!u(this,Me)&&!u(this,le)){const R="LRU_CACHE_UNBOUNDED";zc(R)&&(vo.add(R),bo("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",R,sl))}}static unsafeExposeInternals(e){return{starts:u(e,We),ttls:u(e,he),sizes:u(e,Oe),keyMap:u(e,yt),keyList:u(e,it),valList:u(e,X),next:u(e,Gt),prev:u(e,ue),get head(){return u(e,It)},get tail(){return u(e,kt)},free:u(e,Re),isBackgroundFetch:n=>{var r;return I(r=e,W,at).call(r,n)},backgroundFetch:(n,r,s,a)=>{var o;return I(o=e,W,ds).call(o,n,r,s,a)},moveToTail:n=>{var r;return I(r=e,W,Er).call(r,n)},indexes:n=>{var r;return I(r=e,W,cn).call(r,n)},rindexes:n=>{var r;return I(r=e,W,un).call(r,n)},isStale:n=>{var r;return u(r=e,fe).call(r,n)}}}get max(){return u(this,Me)}get maxSize(){return u(this,le)}get calculatedSize(){return u(this,ce)}get size(){return u(this,Ct)}get fetchMethod(){return u(this,$r)}get memoMethod(){return u(this,yr)}get dispose(){return u(this,De)}get disposeAfter(){return u(this,Be)}getRemainingTTL(e){return u(this,yt).has(e)?1/0:0}*entries(){for(const e of I(this,W,cn).call(this))u(this,X)[e]!==void 0&&u(this,it)[e]!==void 0&&!I(this,W,at).call(this,u(this,X)[e])&&(yield[u(this,it)[e],u(this,X)[e]])}*rentries(){for(const e of I(this,W,un).call(this))u(this,X)[e]!==void 0&&u(this,it)[e]!==void 0&&!I(this,W,at).call(this,u(this,X)[e])&&(yield[u(this,it)[e],u(this,X)[e]])}*keys(){for(const e of I(this,W,cn).call(this)){const n=u(this,it)[e];n!==void 0&&!I(this,W,at).call(this,u(this,X)[e])&&(yield n)}}*rkeys(){for(const e of I(this,W,un).call(this)){const n=u(this,it)[e];n!==void 0&&!I(this,W,at).call(this,u(this,X)[e])&&(yield n)}}*values(){for(const e of I(this,W,cn).call(this))u(this,X)[e]!==void 0&&!I(this,W,at).call(this,u(this,X)[e])&&(yield u(this,X)[e])}*rvalues(){for(const e of I(this,W,un).call(this))u(this,X)[e]!==void 0&&!I(this,W,at).call(this,u(this,X)[e])&&(yield u(this,X)[e])}[(Co=Symbol.iterator,Eo=Symbol.toStringTag,Co)](){return this.entries()}find(e,n={}){for(const r of I(this,W,cn).call(this)){const s=u(this,X)[r],a=I(this,W,at).call(this,s)?s.__staleWhileFetching:s;if(a!==void 0&&e(a,u(this,it)[r],this))return this.get(u(this,it)[r],n)}}forEach(e,n=this){for(const r of I(this,W,cn).call(this)){const s=u(this,X)[r],a=I(this,W,at).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&e.call(n,a,u(this,it)[r],this)}}rforEach(e,n=this){for(const r of I(this,W,un).call(this)){const s=u(this,X)[r],a=I(this,W,at).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&e.call(n,a,u(this,it)[r],this)}}purgeStale(){let e=!1;for(const n of I(this,W,un).call(this,{allowStale:!0}))u(this,fe).call(this,n)&&(I(this,W,hn).call(this,u(this,it)[n],"expire"),e=!0);return e}info(e){const n=u(this,yt).get(e);if(n===void 0)return;const r=u(this,X)[n],s=I(this,W,at).call(this,r)?r.__staleWhileFetching:r;if(s===void 0)return;const a={value:s};if(u(this,he)&&u(this,We)){const o=u(this,he)[n],i=u(this,We)[n];if(o&&i){const c=o-(Ln.now()-i);a.ttl=c,a.start=Date.now()}}return u(this,Oe)&&(a.size=u(this,Oe)[n]),a}dump(){const e=[];for(const n of I(this,W,cn).call(this,{allowStale:!0})){const r=u(this,it)[n],s=u(this,X)[n],a=I(this,W,at).call(this,s)?s.__staleWhileFetching:s;if(a===void 0||r===void 0)continue;const o={value:a};if(u(this,he)&&u(this,We)){o.ttl=u(this,he)[n];const i=Ln.now()-u(this,We)[n];o.start=Math.floor(Date.now()-i)}u(this,Oe)&&(o.size=u(this,Oe)[n]),e.unshift([r,o])}return e}load(e){this.clear();for(const[n,r]of e){if(r.start){const s=Date.now()-r.start;r.start=Ln.now()-s}this.set(n,r.value,r)}}set(e,n,r={}){var s,a,o,i,c;if(n===void 0)return this.delete(e),this;const{ttl:l=this.ttl,start:h,noDisposeOnSet:f=this.noDisposeOnSet,sizeCalculation:d=this.sizeCalculation,status:p}=r;let{noUpdateTTL:m=this.noUpdateTTL}=r;const $=u(this,fs).call(this,e,n,r.size||0,d);if(this.maxEntrySize&&$>this.maxEntrySize)return p&&(p.set="miss",p.maxEntrySizeExceeded=!0),I(this,W,hn).call(this,e,"set"),this;let b=u(this,Ct)===0?void 0:u(this,yt).get(e);if(b===void 0)b=u(this,Ct)===0?u(this,kt):u(this,Re).length!==0?u(this,Re).pop():u(this,Ct)===u(this,Me)?I(this,W,ps).call(this,!1):u(this,Ct),u(this,it)[b]=e,u(this,X)[b]=n,u(this,yt).set(e,b),u(this,Gt)[u(this,kt)]=b,u(this,ue)[b]=u(this,kt),C(this,kt,b),Us(this,Ct)._++,u(this,Nr).call(this,b,$,p),p&&(p.set="add"),m=!1;else{I(this,W,Er).call(this,b);const w=u(this,X)[b];if(n!==w){if(u(this,Nn)&&I(this,W,at).call(this,w)){w.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:N}=w;N!==void 0&&!f&&(u(this,Te)&&((s=u(this,De))==null||s.call(this,N,e,"set")),u(this,qt)&&((a=u(this,Ft))==null||a.push([N,e,"set"])))}else f||(u(this,Te)&&((o=u(this,De))==null||o.call(this,w,e,"set")),u(this,qt)&&((i=u(this,Ft))==null||i.push([w,e,"set"])));if(u(this,Hn).call(this,b),u(this,Nr).call(this,b,$,p),u(this,X)[b]=n,p){p.set="replace";const N=w&&I(this,W,at).call(this,w)?w.__staleWhileFetching:w;N!==void 0&&(p.oldValue=N)}}else p&&(p.set="update")}if(l!==0&&!u(this,he)&&I(this,W,Qs).call(this),u(this,he)&&(m||u(this,hs).call(this,b,l,h),p&&u(this,ln).call(this,p,b)),!f&&u(this,qt)&&u(this,Ft)){const w=u(this,Ft);let N;for(;N=w?.shift();)(c=u(this,Be))==null||c.call(this,...N)}return this}pop(){var e;try{for(;u(this,Ct);){const n=u(this,X)[u(this,It)];if(I(this,W,ps).call(this,!0),I(this,W,at).call(this,n)){if(n.__staleWhileFetching)return n.__staleWhileFetching}else if(n!==void 0)return n}}finally{if(u(this,qt)&&u(this,Ft)){const n=u(this,Ft);let r;for(;r=n?.shift();)(e=u(this,Be))==null||e.call(this,...r)}}}has(e,n={}){const{updateAgeOnHas:r=this.updateAgeOnHas,status:s}=n,a=u(this,yt).get(e);if(a!==void 0){const o=u(this,X)[a];if(I(this,W,at).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(u(this,fe).call(this,a))s&&(s.has="stale",u(this,ln).call(this,s,a));else return r&&u(this,_n).call(this,a),s&&(s.has="hit",u(this,ln).call(this,s,a)),!0}else s&&(s.has="miss");return!1}peek(e,n={}){const{allowStale:r=this.allowStale}=n,s=u(this,yt).get(e);if(s===void 0||!r&&u(this,fe).call(this,s))return;const a=u(this,X)[s];return I(this,W,at).call(this,a)?a.__staleWhileFetching:a}async fetch(e,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:i=this.noDisposeOnSet,size:c=0,sizeCalculation:l=this.sizeCalculation,noUpdateTTL:h=this.noUpdateTTL,noDeleteOnFetchRejection:f=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:d=this.allowStaleOnFetchRejection,ignoreFetchAbort:p=this.ignoreFetchAbort,allowStaleOnFetchAbort:m=this.allowStaleOnFetchAbort,context:$,forceRefresh:b=!1,status:w,signal:N}=n;if(!u(this,Nn))return w&&(w.fetch="get"),this.get(e,{allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,status:w});const E={allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,ttl:o,noDisposeOnSet:i,size:c,sizeCalculation:l,noUpdateTTL:h,noDeleteOnFetchRejection:f,allowStaleOnFetchRejection:d,allowStaleOnFetchAbort:m,ignoreFetchAbort:p,status:w,signal:N};let D=u(this,yt).get(e);if(D===void 0){w&&(w.fetch="miss");const k=I(this,W,ds).call(this,e,D,E,$);return k.__returned=k}else{const k=u(this,X)[D];if(I(this,W,at).call(this,k)){const U=r&&k.__staleWhileFetching!==void 0;return w&&(w.fetch="inflight",U&&(w.returnedStale=!0)),U?k.__staleWhileFetching:k.__returned=k}const O=u(this,fe).call(this,D);if(!b&&!O)return w&&(w.fetch="hit"),I(this,W,Er).call(this,D),s&&u(this,_n).call(this,D),w&&u(this,ln).call(this,w,D),k;const A=I(this,W,ds).call(this,e,D,E,$),R=A.__staleWhileFetching!==void 0&&r;return w&&(w.fetch=O?"stale":"refresh",R&&O&&(w.returnedStale=!0)),R?A.__staleWhileFetching:A.__returned=A}}async forceFetch(e,n={}){const r=await this.fetch(e,n);if(r===void 0)throw new Error("fetch() returned undefined");return r}memo(e,n={}){const r=u(this,yr);if(!r)throw new Error("no memoMethod provided to constructor");const{context:s,forceRefresh:a,...o}=n,i=this.get(e,o);if(!a&&i!==void 0)return i;const c=r(e,i,{options:o,context:s});return this.set(e,c,o),c}get(e,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,status:o}=n,i=u(this,yt).get(e);if(i!==void 0){const c=u(this,X)[i],l=I(this,W,at).call(this,c);return o&&u(this,ln).call(this,o,i),u(this,fe).call(this,i)?(o&&(o.get="stale"),l?(o&&r&&c.__staleWhileFetching!==void 0&&(o.returnedStale=!0),r?c.__staleWhileFetching:void 0):(a||I(this,W,hn).call(this,e,"expire"),o&&r&&(o.returnedStale=!0),r?c:void 0)):(o&&(o.get="hit"),l?c.__staleWhileFetching:(I(this,W,Er).call(this,i),s&&u(this,_n).call(this,i),c))}else o&&(o.get="miss")}delete(e){return I(this,W,hn).call(this,e,"delete")}clear(){return I(this,W,na).call(this,"delete")}};Me=new WeakMap,le=new WeakMap,De=new WeakMap,Be=new WeakMap,$r=new WeakMap,yr=new WeakMap,Ct=new WeakMap,ce=new WeakMap,yt=new WeakMap,it=new WeakMap,X=new WeakMap,Gt=new WeakMap,ue=new WeakMap,It=new WeakMap,kt=new WeakMap,Re=new WeakMap,Ft=new WeakMap,Oe=new WeakMap,We=new WeakMap,he=new WeakMap,Te=new WeakMap,Nn=new WeakMap,qt=new WeakMap,W=new WeakSet,Qs=function(){const t=new us(u(this,Me)),e=new us(u(this,Me));C(this,he,t),C(this,We,e),C(this,hs,(s,a,o=Ln.now())=>{if(e[s]=a!==0?o:0,t[s]=a,a!==0&&this.ttlAutopurge){const i=setTimeout(()=>{u(this,fe).call(this,s)&&I(this,W,hn).call(this,u(this,it)[s],"expire")},a+1);i.unref&&i.unref()}}),C(this,_n,s=>{e[s]=t[s]!==0?Ln.now():0}),C(this,ln,(s,a)=>{if(t[a]){const o=t[a],i=e[a];if(!o||!i)return;s.ttl=o,s.start=i,s.now=n||r();const c=s.now-i;s.remainingTTL=o-c}});let n=0;const r=()=>{const s=Ln.now();if(this.ttlResolution>0){n=s;const a=setTimeout(()=>n=0,this.ttlResolution);a.unref&&a.unref()}return s};this.getRemainingTTL=s=>{const a=u(this,yt).get(s);if(a===void 0)return 0;const o=t[a],i=e[a];if(!o||!i)return 1/0;const c=(n||r())-i;return o-c},C(this,fe,s=>{const a=e[s],o=t[s];return!!o&&!!a&&(n||r())-a>o})},_n=new WeakMap,ln=new WeakMap,hs=new WeakMap,fe=new WeakMap,ko=function(){const t=new us(u(this,Me));C(this,ce,0),C(this,Oe,t),C(this,Hn,e=>{C(this,ce,u(this,ce)-t[e]),t[e]=0}),C(this,fs,(e,n,r,s)=>{if(I(this,W,at).call(this,n))return 0;if(!on(r))if(s){if(typeof s!="function")throw new TypeError("sizeCalculation must be a function");if(r=s(n,e),!on(r))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return r}),C(this,Nr,(e,n,r)=>{if(t[e]=n,u(this,le)){const s=u(this,le)-t[e];for(;u(this,ce)>s;)I(this,W,ps).call(this,!0)}C(this,ce,u(this,ce)+t[e]),r&&(r.entrySize=n,r.totalCalculatedSize=u(this,ce))})},Hn=new WeakMap,Nr=new WeakMap,fs=new WeakMap,cn=function*({allowStale:t=this.allowStale}={}){if(u(this,Ct))for(let e=u(this,kt);!(!I(this,W,ta).call(this,e)||((t||!u(this,fe).call(this,e))&&(yield e),e===u(this,It)));)e=u(this,ue)[e]},un=function*({allowStale:t=this.allowStale}={}){if(u(this,Ct))for(let e=u(this,It);!(!I(this,W,ta).call(this,e)||((t||!u(this,fe).call(this,e))&&(yield e),e===u(this,kt)));)e=u(this,Gt)[e]},ta=function(t){return t!==void 0&&u(this,yt).get(u(this,it)[t])===t},ps=function(t){var e,n;const r=u(this,It),s=u(this,it)[r],a=u(this,X)[r];return u(this,Nn)&&I(this,W,at).call(this,a)?a.__abortController.abort(new Error("evicted")):(u(this,Te)||u(this,qt))&&(u(this,Te)&&((e=u(this,De))==null||e.call(this,a,s,"evict")),u(this,qt)&&((n=u(this,Ft))==null||n.push([a,s,"evict"]))),u(this,Hn).call(this,r),t&&(u(this,it)[r]=void 0,u(this,X)[r]=void 0,u(this,Re).push(r)),u(this,Ct)===1?(C(this,It,C(this,kt,0)),u(this,Re).length=0):C(this,It,u(this,Gt)[r]),u(this,yt).delete(s),Us(this,Ct)._--,r},ds=function(t,e,n,r){const s=e===void 0?void 0:u(this,X)[e];if(I(this,W,at).call(this,s))return s;const a=new cs,{signal:o}=n;o?.addEventListener("abort",()=>a.abort(o.reason),{signal:a.signal});const i={signal:a.signal,options:n,context:r},c=(m,$=!1)=>{const{aborted:b}=a.signal,w=n.ignoreFetchAbort&&m!==void 0;if(n.status&&(b&&!$?(n.status.fetchAborted=!0,n.status.fetchError=a.signal.reason,w&&(n.status.fetchAbortIgnored=!0)):n.status.fetchResolved=!0),b&&!w&&!$)return h(a.signal.reason);const N=d;return u(this,X)[e]===d&&(m===void 0?N.__staleWhileFetching?u(this,X)[e]=N.__staleWhileFetching:I(this,W,hn).call(this,t,"fetch"):(n.status&&(n.status.fetchUpdated=!0),this.set(t,m,i.options))),m},l=m=>(n.status&&(n.status.fetchRejected=!0,n.status.fetchError=m),h(m)),h=m=>{const{aborted:$}=a.signal,b=$&&n.allowStaleOnFetchAbort,w=b||n.allowStaleOnFetchRejection,N=w||n.noDeleteOnFetchRejection,E=d;if(u(this,X)[e]===d&&(!N||E.__staleWhileFetching===void 0?I(this,W,hn).call(this,t,"fetch"):b||(u(this,X)[e]=E.__staleWhileFetching)),w)return n.status&&E.__staleWhileFetching!==void 0&&(n.status.returnedStale=!0),E.__staleWhileFetching;if(E.__returned===E)throw m},f=(m,$)=>{var b;const w=(b=u(this,$r))==null?void 0:b.call(this,t,s,i);w&&w instanceof Promise&&w.then(N=>m(N===void 0?void 0:N),$),a.signal.addEventListener("abort",()=>{(!n.ignoreFetchAbort||n.allowStaleOnFetchAbort)&&(m(void 0),n.allowStaleOnFetchAbort&&(m=N=>c(N,!0)))})};n.status&&(n.status.fetchDispatched=!0);const d=new Promise(f).then(c,l),p=Object.assign(d,{__abortController:a,__staleWhileFetching:s,__returned:void 0});return e===void 0?(this.set(t,p,{...i.options,status:void 0}),e=u(this,yt).get(t)):u(this,X)[e]=p,p},at=function(t){if(!u(this,Nn))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof cs},ea=function(t,e){u(this,ue)[e]=t,u(this,Gt)[t]=e},Er=function(t){t!==u(this,kt)&&(t===u(this,It)?C(this,It,u(this,Gt)[t]):I(this,W,ea).call(this,u(this,ue)[t],u(this,Gt)[t]),I(this,W,ea).call(this,u(this,kt),t),C(this,kt,t))},hn=function(t,e){var n,r,s,a;let o=!1;if(u(this,Ct)!==0){const i=u(this,yt).get(t);if(i!==void 0)if(o=!0,u(this,Ct)===1)I(this,W,na).call(this,e);else{u(this,Hn).call(this,i);const c=u(this,X)[i];if(I(this,W,at).call(this,c)?c.__abortController.abort(new Error("deleted")):(u(this,Te)||u(this,qt))&&(u(this,Te)&&((n=u(this,De))==null||n.call(this,c,t,e)),u(this,qt)&&((r=u(this,Ft))==null||r.push([c,t,e]))),u(this,yt).delete(t),u(this,it)[i]=void 0,u(this,X)[i]=void 0,i===u(this,kt))C(this,kt,u(this,ue)[i]);else if(i===u(this,It))C(this,It,u(this,Gt)[i]);else{const l=u(this,ue)[i];u(this,Gt)[l]=u(this,Gt)[i];const h=u(this,Gt)[i];u(this,ue)[h]=u(this,ue)[i]}Us(this,Ct)._--,u(this,Re).push(i)}}if(u(this,qt)&&(s=u(this,Ft))!=null&&s.length){const i=u(this,Ft);let c;for(;c=i?.shift();)(a=u(this,Be))==null||a.call(this,...c)}return o},na=function(t){var e,n,r;for(const s of I(this,W,un).call(this,{allowStale:!0})){const a=u(this,X)[s];if(I(this,W,at).call(this,a))a.__abortController.abort(new Error("deleted"));else{const o=u(this,it)[s];u(this,Te)&&((e=u(this,De))==null||e.call(this,a,o,t)),u(this,qt)&&((n=u(this,Ft))==null||n.push([a,o,t]))}}if(u(this,yt).clear(),u(this,X).fill(void 0),u(this,it).fill(void 0),u(this,he)&&u(this,We)&&(u(this,he).fill(0),u(this,We).fill(0)),u(this,Oe)&&u(this,Oe).fill(0),C(this,It,0),C(this,kt,0),u(this,Re).length=0,C(this,ce,0),C(this,Ct,0),u(this,qt)&&u(this,Ft)){const s=u(this,Ft);let a;for(;a=s?.shift();)(r=u(this,Be))==null||r.call(this,...a)}};let qc=Gc;const S=t=>typeof t=="string"||t instanceof String,Un=t=>S(t)||typeof t=="number",ms="(?:0|[1-9]\\d*)",Vc="clamp|max|min",Kc="exp|hypot|log|pow|sqrt",Xc="abs|sign",Yc="mod|rem|round",Zc="a?(?:cos|sin|tan)|atan2",Fo=`${Vc}|${Kc}|${Xc}|${Yc}|${Zc}`,ra=`calc|${Fo}`,Jc=`var|${ra}`,zn="deg|g?rad|turn",Cr="[cm]m|[dls]?v(?:[bhiw]|max|min)|in|p[ctx]|q|r?(?:[cl]h|cap|e[mx]|ic)",Lt=`[+-]?(?:${ms}(?:\\.\\d*)?|\\.\\d+)(?:e-?${ms})?`,xo=`\\+?(?:${ms}(?:\\.\\d*)?|\\.\\d+)(?:e-?${ms})?`,g="none",_t=`${Lt}%`,gs=`^(?:${ra})\\(|(?<=[*\\/\\s\\(])(?:${ra})\\(`,So=`^(?:${Fo})\\($`,kr="^var\\(|(?<=[*\\/\\s\\(])var\\(",Qc=`^(?:${Jc})\\(`,vs=`(?:\\s*\\/\\s*(?:${Lt}|${_t}|${g}))?`,Ao=`(?:\\s*,\\s*(?:${Lt}|${_t}))?`,Po="(?:ok)?l(?:ab|ch)|color|hsla?|hwb|rgba?",tu="[a-z]+|#[\\da-f]{3}|#[\\da-f]{4}|#[\\da-f]{6}|#[\\da-f]{8}",Mo="(?:ok)?lch|hsl|hwb",Do="(?:de|in)creasing|longer|shorter",eu=`${Lt}(?:${zn})?`,Bo=`(?:${Lt}(?:${zn})?|${g})`,Fr=`(?:${Lt}|${_t}|${g})`,Ro=`(?:${Mo})(?:\\s(?:${Do})\\shue)?`,nu=`(${Mo})(?:\\s(${Do})\\shue)?`,Oo="(?:ok)?lab",ru="(?:ok)?lch",su="srgb(?:-linear)?",sa=`(?:a98|prophoto)-rgb|display-p3|rec2020|${su}`,aa="xyz(?:-d(?:50|65))?",Wo=`${Oo}|${sa}|${aa}`,oa=`${Ro}|${Wo}`,ut="color(",jn="color-mix(",xr=`(?:${Po})\\(\\s*from\\s+`,au=`(${Po})\\(\\s*from\\s+`,ia="var(",To=`(?:${sa}|${aa})(?:\\s+${Fr}){3}${vs}`,Io=`^${xr}|(?<=[\\s])${xr}`,la=`${Bo}(?:\\s+${Fr}){2}${vs}`,Lo=`${eu}(?:\\s*,\\s*${_t}){2}${Ao}`,ca=`(?:${Fr}\\s+){2}${Bo}${vs}`,bs=`${Fr}(?:\\s+${Fr}){2}${vs}`,_o=`(?:${Lt}(?:\\s*,\\s*${Lt}){2}|${_t}(?:\\s*,\\s*${_t}){2})${Ao}`,Gn=`${tu}|hsla?\\(\\s*${Lo}\\s*\\)|rgba?\\(\\s*${_o}\\s*\\)|(?:hsla?|hwb)\\(\\s*${la}\\s*\\)|(?:(?:ok)?lab|rgba?)\\(\\s*${bs}\\s*\\)|(?:ok)?lch\\(\\s*${ca}\\s*\\)|color\\(\\s*${To}\\s*\\)`,qn=`(?:${Gn})(?:\\s+${_t})?`,ws=`color-mix\\(\\s*in\\s+(?:${oa})\\s*,\\s*${qn}\\s*,\\s*${qn}\\s*\\)`,ou=`color-mix\\(\\s*in\\s+(${oa})\\s*,\\s*(${qn})\\s*,\\s*(${qn})\\s*\\)`,ot="computedValue",Z="mixValue",rt="specifiedValue",ua="color",ha=.001,Sr=.5,fa=2,dt=3,Ie=4,Le=8,Ar=10,$s=12,T=16,iu=60,Vn=180,pe=360,q=100,_=255,En=2,Pr=3,Kn=2.4,Xn=12.92,de=.055,Mr=116,Ho=500,Uo=200,pa=216/24389,ys=24389/27,zo=[.3457/.3585,1,(1-.3457-.3585)/.3585],Dr=[[.955473421488075,-.02309845494876471,.06325924320057072],[-.0283697093338637,1.0099953980813041,.021041441191917323],[.012314014864481998,-.020507649298898964,1.330365926242124]],we=[[1.0479297925449969,.022946870601609652,-.05019226628920524],[.02962780877005599,.9904344267538799,-.017073799063418826],[-.009243040646204504,.015055191490298152,.7518742814281371]],da=[[506752/1228815,87881/245763,12673/70218],[87098/409605,175762/245763,12673/175545],[7918/409605,87881/737289,1001167/1053270]],Ns=[[12831/3959,-329/214,-1974/3959],[-851781/878810,1648619/878810,36519/878810],[705/12673,-2585/12673,705/667]],lu=[[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],jo=[[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],Go=[[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]],cu=[[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],uu=[[608311/1250200,189793/714400,198249/1000160],[35783/156275,247089/357200,198249/2500400],[0/1,32229/714400,5220557/5000800]],hu=[[63426534/99577255,20160776/139408157,47086771/278816314],[26158966/99577255,472592308/697040785,8267143/139408157],[0/1,19567812/697040785,295819943/278816314]],fu=[[573536/994567,263643/1420810,187206/994567],[591459/1989134,6239551/9945670,374412/4972835],[53769/1989134,351524/4972835,4929758/4972835]],pu=[[.7977666449006423,.13518129740053308,.0313477341283922],[.2880748288194013,.711835234241873,8993693872564e-17],[0,0,.8251046025104602]],qo=new RegExp(`^(?:${Gn})$`),Es=new RegExp(`^${nu}$`),du=/^xyz(?:-d(?:50|65))?$/,wt=/^currentColor$/i,Yn=new RegExp(`^color\\(\\s*(${To})\\s*\\)$`),ma=new RegExp(`^hsla?\\(\\s*(${la}|${Lo})\\s*\\)$`),ga=new RegExp(`^hwb\\(\\s*(${la})\\s*\\)$`),va=new RegExp(`^lab\\(\\s*(${bs})\\s*\\)$`),ba=new RegExp(`^lch\\(\\s*(${ca})\\s*\\)$`),Vo=new RegExp(`^${ws}$`),mu=new RegExp(`^${ou}$`),Ko=new RegExp(`${ws}`,"g"),wa=new RegExp(`^oklab\\(\\s*(${bs})\\s*\\)$`),$a=new RegExp(`^oklch\\(\\s*(${ca})\\s*\\)$`),Vt=/^(?:specifi|comput)edValue$/,Zn={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},$e=(t,e,n=!1)=>{if(e===rt){const s="";return F(t,s),s}if(n)return F(t,null),new P;const r=["rgb",0,0,0,0];return F(t,r),r},ye=(t,e=!1)=>{switch(t){case"hsl":case"hwb":case Z:return new P;case rt:return"";default:return e?new P:["rgb",0,0,0,0]}},me=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{alpha:n=!1,minLength:r=dt,maxLength:s=Ie,minRange:a=0,maxRange:o=1,validateRange:i=!0}=e;if(!Number.isFinite(r))throw new TypeError(`${r} is not a number.`);if(!Number.isFinite(s))throw new TypeError(`${s} is not a number.`);if(!Number.isFinite(a))throw new TypeError(`${a} is not a number.`);if(!Number.isFinite(o))throw new TypeError(`${o} is not a number.`);const c=t.length;if(c<r||c>s)throw new Error(`Unexpected array length ${c}.`);let l=0;for(;l<c;){const h=t[l];if(Number.isFinite(h)){if(l<dt&&i&&(h<a||h>o))throw new RangeError(`${h} is not between ${a} and ${o}.`);if(l===dt&&(h<0||h>1))throw new RangeError(`${h} is not between 0 and 1.`)}else throw new TypeError(`${h} is not a number.`);l++}return n&&c===dt&&t.push(1),t},J=(t,e,n=!1)=>{if(Array.isArray(t)){if(t.length!==dt)throw new Error(`Unexpected array length ${t.length}.`);if(!n)for(let N of t)N=me(N,{maxLength:dt,validateRange:!1})}else throw new TypeError(`${t} is not an array.`);const[[r,s,a],[o,i,c],[l,h,f]]=t;let d,p,m;n?[d,p,m]=e:[d,p,m]=me(e,{maxLength:dt,validateRange:!1});const $=r*d+s*p+a*m,b=o*d+i*p+c*m,w=l*d+h*p+f*m;return[$,b,w]},Br=(t,e,n=!1)=>{if(Array.isArray(t)){if(t.length!==Ie)throw new Error(`Unexpected array length ${t.length}.`)}else throw new TypeError(`${t} is not an array.`);if(Array.isArray(e)){if(e.length!==Ie)throw new Error(`Unexpected array length ${e.length}.`)}else throw new TypeError(`${e} is not an array.`);let r=0;for(;r<Ie;)t[r]===g&&e[r]===g?(t[r]=0,e[r]=0):t[r]===g?t[r]=e[r]:e[r]===g&&(e[r]=t[r]),r++;if(n)return[t,e];const s=me(t,{minLength:Ie,validateRange:!1}),a=me(e,{minLength:Ie,validateRange:!1});return[s,a]},Rr=t=>{if(Number.isFinite(t)){if(t=Math.round(t),t<0||t>_)throw new RangeError(`${t} is not between 0 and ${_}.`)}else throw new TypeError(`${t} is not a number.`);let e=t.toString(T);return e.length===1&&(e=`0${e}`),e},Cs=t=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const e=pe/400,n=pe/(Math.PI*fa),r=new RegExp(`^(${Lt})(${zn})?$`);if(!r.test(t))throw new SyntaxError(`Invalid property value: ${t}`);const[,s,a]=t.match(r);let o;switch(a){case"grad":o=parseFloat(s)*e;break;case"rad":o=parseFloat(s)*n;break;case"turn":o=parseFloat(s)*pe;break;default:o=parseFloat(s)}return o%=pe,o<0?o+=pe:Object.is(o,-0)&&(o=0),o},Ye=(t="")=>{if(S(t))if(t=t.trim(),!t)t="1";else if(t===g)t="0";else{let e;if(t.endsWith("%")?e=parseFloat(t)/q:e=parseFloat(t),!Number.isFinite(e))throw new TypeError(`${e} is not a finite number.`);e<ha?t="0":e>1?t="1":t=e.toFixed(dt)}else t="1";return parseFloat(t)},Xo=t=>{if(S(t)){if(t==="")throw new SyntaxError("Invalid property value: (empty string)");t=t.trim()}else throw new TypeError(`${t} is not a string.`);let e=parseInt(t,T);if(e<=0)return 0;if(e>=_)return 1;const n=new Map;for(let r=1;r<q;r++)n.set(Math.round(r*_/q),r);return n.has(e)?e=n.get(e)/q:e=Math.round(e/_/ha)*ha,parseFloat(e.toFixed(dt))},ya=(t,e=!1)=>{let n,r,s;e?[n,r,s]=t:[n,r,s]=me(t,{maxLength:dt,maxRange:_});let a=n/_,o=r/_,i=s/_;const c=.04045;return a>c?a=Math.pow((a+de)/(1+de),Kn):a/=Xn,o>c?o=Math.pow((o+de)/(1+de),Kn):o/=Xn,i>c?i=Math.pow((i+de)/(1+de),Kn):i/=Xn,[a,o,i]},Na=(t,e=!1)=>(e||(t=me(t,{maxLength:dt,maxRange:_})),t=ya(t,!0),J(da,t,!0)),Yo=(t,e=!1)=>{let[n,r,s]=me(t,{maxLength:dt});const a=809/258400;return n>a?n=Math.pow(n,1/Kn)*(1+de)-de:n*=Xn,n*=_,r>a?r=Math.pow(r,1/Kn)*(1+de)-de:r*=Xn,r*=_,s>a?s=Math.pow(s,1/Kn)*(1+de)-de:s*=Xn,s*=_,[e?Math.round(n):n,e?Math.round(r):r,e?Math.round(s):s]},Jn=(t,e=!1)=>{e||(t=me(t,{maxLength:dt,validateRange:!1}));let[n,r,s]=J(Ns,t,!0);return[n,r,s]=Yo([Math.min(Math.max(n,0),1),Math.min(Math.max(r,0),1),Math.min(Math.max(s,0),1)],!0),[n,r,s]},Zo=(t,e=!1)=>{const[n,r,s]=Jn(t,e),a=n/_,o=r/_,i=s/_,c=Math.max(a,o,i),l=Math.min(a,o,i),h=c-l,f=(c+l)*Sr*q;let d,p;if(Math.round(f)===0||Math.round(f)===q)d=0,p=0;else if(p=h/(1-Math.abs(c+l-1))*q,p===0)d=0;else{switch(c){case a:d=(o-i)/h;break;case o:d=(i-a)/h+fa;break;case i:default:d=(a-o)/h+Ie;break}d=d*iu%pe,d<0&&(d+=pe)}return[d,p,f]},gu=(t,e=!1)=>{const[n,r,s]=Jn(t,e),a=Math.min(n,r,s)/_,o=1-Math.max(n,r,s)/_;let i;return a+o===1?i=0:[i]=Zo(t),[i,a*q,o*q]},Jo=(t,e=!1)=>{e||(t=me(t,{maxLength:dt,validateRange:!1}));const n=J(lu,t,!0).map(i=>Math.cbrt(i));let[r,s,a]=J(cu,n,!0);r=Math.min(Math.max(r,0),1);const o=Math.round(parseFloat(r.toFixed(Ie))*q);return(o===0||o===q)&&(s=0,a=0),[r,s,a]},vu=(t,e=!1)=>{const[n,r,s]=Jo(t,e);let a,o;const i=Math.round(parseFloat(n.toFixed(Ie))*q);return i===0||i===q?(a=0,o=0):(a=Math.max(Math.sqrt(Math.pow(r,En)+Math.pow(s,En)),0),parseFloat(a.toFixed(Ie))===0?o=0:(o=Math.atan2(s,r)*Vn/Math.PI,o<0&&(o+=pe))),[n,a,o]},Qo=(t,e=!1)=>{e||(t=me(t,{maxLength:dt,validateRange:!1}));const n=J(Dr,t,!0);return Jn(n,!0)},ti=(t,e=!1)=>{e||(t=me(t,{maxLength:dt,validateRange:!1}));const n=t.map((l,h)=>l/zo[h]),[r,s,a]=n.map(l=>l>pa?Math.cbrt(l):(l*ys+T)/Mr),o=Math.min(Math.max(Mr*s-T,0),q);let i,c;return o===0||o===q?(i=0,c=0):(i=(r-s)*Ho,c=(s-a)*Uo),[o,i,c]},bu=(t,e=!1)=>{const[n,r,s]=ti(t,e);let a,o;return n===0||n===q?(a=0,o=0):(a=Math.max(Math.sqrt(Math.pow(r,En)+Math.pow(s,En)),0),o=Math.atan2(s,r)*Vn/Math.PI,o<0&&(o+=pe)),[n,a,o]},ei=t=>{const[e,n,r,s]=me(t,{alpha:!0,maxRange:_}),a=Rr(e),o=Rr(n),i=Rr(r),c=Rr(s*_);let l;return c==="ff"?l=`#${a}${o}${i}`:l=`#${a}${o}${i}${c}`,l},Ea=t=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);if(!(/^#[\da-f]{6}$/.test(t)||/^#[\da-f]{3}$/.test(t)||/^#[\da-f]{8}$/.test(t)||/^#[\da-f]{4}$/.test(t)))throw new SyntaxError(`Invalid property value: ${t}`);const e=[];if(/^#[\da-f]{3}$/.test(t)){const[,n,r,s]=t.match(/^#([\da-f])([\da-f])([\da-f])$/);e.push(parseInt(`${n}${n}`,T),parseInt(`${r}${r}`,T),parseInt(`${s}${s}`,T),1)}else if(/^#[\da-f]{4}$/.test(t)){const[,n,r,s,a]=t.match(/^#([\da-f])([\da-f])([\da-f])([\da-f])$/);e.push(parseInt(`${n}${n}`,T),parseInt(`${r}${r}`,T),parseInt(`${s}${s}`,T),Xo(`${a}${a}`))}else if(/^#[\da-f]{8}$/.test(t)){const[,n,r,s,a]=t.match(/^#([\da-f]{2})([\da-f]{2})([\da-f]{2})([\da-f]{2})$/);e.push(parseInt(n,T),parseInt(r,T),parseInt(s,T),Xo(a))}else{const[,n,r,s]=t.match(/^#([\da-f]{2})([\da-f]{2})([\da-f]{2})$/);e.push(parseInt(n,T),parseInt(r,T),parseInt(s,T),1)}return e},wu=t=>{const[e,n,r,s]=Ea(t),[a,o,i]=ya([e,n,r],!0);return[a,o,i,s]},$u=t=>{const[e,n,r,s]=wu(t),[a,o,i]=J(da,[e,n,r],!0);return[a,o,i,s]},ni=(t,e={})=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e,s=new RegExp(`^rgba?\\(\\s*(${bs}|${_o})\\s*\\)$`);if(!s.test(t)){const m=ye(n,r);return m instanceof P||S(m),m}const[,a]=t.match(s),[o,i,c,l=""]=a.replace(/[,/]/g," ").split(/\s+/);let h,f,d;o===g?h=0:(o.endsWith("%")?h=parseFloat(o)*_/q:h=parseFloat(o),h=Math.min(Math.max(M(h,Le),0),_)),i===g?f=0:(i.endsWith("%")?f=parseFloat(i)*_/q:f=parseFloat(i),f=Math.min(Math.max(M(f,Le),0),_)),c===g?d=0:(c.endsWith("%")?d=parseFloat(c)*_/q:d=parseFloat(c),d=Math.min(Math.max(M(d,Le),0),_));const p=Ye(l);return["rgb",h,f,d,n===Z&&l===g?g:p]},ks=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!ma.test(t)){const D=ye(n,r);return D instanceof P||S(D),D}const[,s]=t.match(ma),[a,o,i,c=""]=s.replace(/[,/]/g," ").split(/\s+/);let l,h,f;a===g?l=0:l=Cs(a),o===g?h=0:h=Math.min(Math.max(parseFloat(o),0),q),i===g?f=0:f=Math.min(Math.max(parseFloat(i),0),q);const d=Ye(c);if(n==="hsl")return[n,a===g?a:l,o===g?o:h,i===g?i:f,c===g?c:d];l=l/pe*$s,f/=q;const p=h/q*Math.min(f,1-f),m=l%$s,$=(8+l)%$s,b=(4+l)%$s,w=f-p*Math.max(-1,Math.min(m-dt,dt**En-m,1)),N=f-p*Math.max(-1,Math.min($-dt,dt**En-$,1)),E=f-p*Math.max(-1,Math.min(b-dt,dt**En-b,1));return["rgb",Math.min(Math.max(M(w*_,Le),0),_),Math.min(Math.max(M(N*_,Le),0),_),Math.min(Math.max(M(E*_,Le),0),_),d]},Ca=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!ga.test(t)){const w=ye(n,r);return w instanceof P||S(w),w}const[,s]=t.match(ga),[a,o,i,c=""]=s.replace("/"," ").split(/\s+/);let l,h,f;a===g?l=0:l=Cs(a),o===g?h=0:h=Math.min(Math.max(parseFloat(o),0),q)/q,i===g?f=0:f=Math.min(Math.max(parseFloat(i),0),q)/q;const d=Ye(c);if(n==="hwb")return[n,a===g?a:l,o===g?o:h*q,i===g?i:f*q,c===g?c:d];if(h+f>=1){const w=M(h/(h+f)*_,Le);return["rgb",w,w,w,d]}const p=(1-h-f)/_;let[,m,$,b]=ks(`hsl(${l} 100 50)`);return m=M((m*p+h)*_,Le),$=M(($*p+h)*_,Le),b=M((b*p+h)*_,Le),["rgb",Math.min(Math.max(m,0),_),Math.min(Math.max($,0),_),Math.min(Math.max(b,0),_),d]},Or=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!va.test(t)){const U=ye(n,r);return U instanceof P||S(U),U}const s=1.25,a=8,[,o]=t.match(va),[i,c,l,h=""]=o.replace("/"," ").split(/\s+/);let f,d,p;i===g?f=0:(i.endsWith("%")?(f=parseFloat(i),f>q&&(f=q)):f=parseFloat(i),f<0&&(f=0)),c===g?d=0:d=c.endsWith("%")?parseFloat(c)*s:parseFloat(c),l===g?p=0:p=l.endsWith("%")?parseFloat(l)*s:parseFloat(l);const m=Ye(h);if(Vt.test(n))return["lab",i===g?i:M(f,T),c===g?c:M(d,T),l===g?l:M(p,T),h===g?h:m];const $=(f+T)/Mr,b=d/Ho+$,w=$-p/Uo,N=Math.pow($,Pr),E=Math.pow(b,Pr),D=Math.pow(w,Pr),k=[E>pa?E:(b*Mr-T)/ys,f>a?N:f/ys,D>pa?D:(w*Mr-T)/ys],[O,A,R]=k.map((U,G)=>U*zo[G]);return["xyz-d50",M(O,T),M(A,T),M(R,T),m]},Fs=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!ba.test(t)){const E=ye(n,r);return E instanceof P||S(E),E}const s=1.5,[,a]=t.match(ba),[o,i,c,l=""]=a.replace("/"," ").split(/\s+/);let h,f,d;o===g?h=0:(h=parseFloat(o),h<0&&(h=0)),i===g?f=0:f=i.endsWith("%")?parseFloat(i)*s:parseFloat(i),c===g?d=0:d=Cs(c);const p=Ye(l);if(Vt.test(n))return["lch",o===g?o:M(h,T),i===g?i:M(f,T),c===g?c:M(d,T),l===g?l:p];const m=f*Math.cos(d*Math.PI/Vn),$=f*Math.sin(d*Math.PI/Vn),[,b,w,N]=Or(`lab(${h} ${m} ${$})`);return["xyz-d50",M(b,T),M(w,T),M(N,T),p]},xs=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!wa.test(t)){const N=ye(n,r);return N instanceof P||S(N),N}const s=.4,[,a]=t.match(wa),[o,i,c,l=""]=a.replace("/"," ").split(/\s+/);let h,f,d;o===g?h=0:(h=o.endsWith("%")?parseFloat(o)/q:parseFloat(o),h<0&&(h=0)),i===g?f=0:i.endsWith("%")?f=parseFloat(i)*s/q:f=parseFloat(i),c===g?d=0:c.endsWith("%")?d=parseFloat(c)*s/q:d=parseFloat(c);const p=Ye(l);if(Vt.test(n))return["oklab",o===g?o:M(h,T),i===g?i:M(f,T),c===g?c:M(d,T),l===g?l:p];const m=J(Go,[h,f,d]).map(N=>Math.pow(N,Pr)),[$,b,w]=J(jo,m,!0);return["xyz-d65",M($,T),M(b,T),M(w,T),p]},Ss=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!$a.test(t)){const D=ye(n,r);return D instanceof P||S(D),D}const s=.4,[,a]=t.match($a),[o,i,c,l=""]=a.replace("/"," ").split(/\s+/);let h,f,d;o===g?h=0:(h=o.endsWith("%")?parseFloat(o)/q:parseFloat(o),h<0&&(h=0)),i===g?f=0:(i.endsWith("%")?f=parseFloat(i)*s/q:f=parseFloat(i),f<0&&(f=0)),c===g?d=0:d=Cs(c);const p=Ye(l);if(Vt.test(n))return["oklch",o===g?o:M(h,T),i===g?i:M(f,T),c===g?c:M(d,T),l===g?l:p];const m=f*Math.cos(d*Math.PI/Vn),$=f*Math.sin(d*Math.PI/Vn),b=J(Go,[h,m,$]).map(D=>Math.pow(D,Pr)),[w,N,E]=J(jo,b,!0);return["xyz-d65",M(w,T),M(N,T),M(E,T),p]},vt=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",d50:r=!1,format:s="",nullable:a=!1}=e;if(!Yn.test(t)){const E=ye(s,a);return E instanceof P||S(E),E}const[,o]=t.match(Yn);let[i,c,l,h,f=""]=o.replace("/"," ").split(/\s+/),d,p,m;i==="xyz"&&(i="xyz-d65"),c===g?d=0:d=c.endsWith("%")?parseFloat(c)/q:parseFloat(c),l===g?p=0:p=l.endsWith("%")?parseFloat(l)/q:parseFloat(l),h===g?m=0:m=h.endsWith("%")?parseFloat(h)/q:parseFloat(h);const $=Ye(f);if(Vt.test(s)||s===Z&&i===n)return[i,c===g?c:M(d,Ar),l===g?l:M(p,Ar),h===g?h:M(m,Ar),f===g?f:$];let b=0,w=0,N=0;if(i==="srgb-linear")[b,w,N]=J(da,[d,p,m]),r&&([b,w,N]=J(we,[b,w,N],!0));else if(i==="display-p3"){const E=ya([d*_,p*_,m*_]);[b,w,N]=J(uu,E),r&&([b,w,N]=J(we,[b,w,N],!0))}else if(i==="rec2020"){const E=1.09929682680944,D=.018053968510807,k=.45,O=[d,p,m].map(A=>{let R;return A<D*k*Ar?R=A/(k*Ar):R=Math.pow((A+E-1)/E,1/k),R});[b,w,N]=J(hu,O),r&&([b,w,N]=J(we,[b,w,N],!0))}else if(i==="a98-rgb"){const E=2.19921875,D=[d,p,m].map(k=>Math.pow(k,E));[b,w,N]=J(fu,D),r&&([b,w,N]=J(we,[b,w,N],!0))}else if(i==="prophoto-rgb"){const E=[d,p,m].map(D=>{let k;return D>1/(T*fa)?k=Math.pow(D,1.8):k=D/T,k});[b,w,N]=J(pu,E),r||([b,w,N]=J(Dr,[b,w,N],!0))}else/^xyz(?:-d(?:50|65))?$/.test(i)?([b,w,N]=[d,p,m],i==="xyz-d50"?r||([b,w,N]=J(Dr,[b,w,N])):r&&([b,w,N]=J(we,[b,w,N],!0))):([b,w,N]=Na([d*_,p*_,m*_]),r&&([b,w,N]=J(we,[b,w,N],!0)));return[r?"xyz-d50":"xyz-d65",M(b,T),M(w,T),M(N,T),s===Z&&f===g?f:$]},Nt=(t,e={})=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{d50:n=!1,format:r="",nullable:s=!1}=e;if(!qo.test(t)){const l=ye(r,s);return l instanceof P||S(l),l}let a=0,o=0,i=0,c=0;if(wt.test(t)){if(r===ot)return["rgb",0,0,0,0];if(r===rt)return t}else if(/^[a-z]+$/.test(t))if(Object.prototype.hasOwnProperty.call(Zn,t)){if(r===rt)return t;const[l,h,f]=Zn[t];if(c=1,r===ot)return["rgb",l,h,f,c];[a,o,i]=Na([l,h,f],!0),n&&([a,o,i]=J(we,[a,o,i],!0))}else switch(r){case ot:return s&&t!=="transparent"?new P:["rgb",0,0,0,0];case rt:return t==="transparent"?t:"";case Z:return t==="transparent"?["rgb",0,0,0,0]:new P}else if(t[0]==="#"){if(Vt.test(r))return["rgb",...Ea(t)];[a,o,i,c]=$u(t),n&&([a,o,i]=J(we,[a,o,i],!0))}else if(t.startsWith("lab")){if(Vt.test(r))return Or(t,e);[,a,o,i,c]=Or(t),n||([a,o,i]=J(Dr,[a,o,i],!0))}else if(t.startsWith("lch")){if(Vt.test(r))return Fs(t,e);[,a,o,i,c]=Fs(t),n||([a,o,i]=J(Dr,[a,o,i],!0))}else if(t.startsWith("oklab")){if(Vt.test(r))return xs(t,e);[,a,o,i,c]=xs(t),n&&([a,o,i]=J(we,[a,o,i],!0))}else if(t.startsWith("oklch")){if(Vt.test(r))return Ss(t,e);[,a,o,i,c]=Ss(t),n&&([a,o,i]=J(we,[a,o,i],!0))}else{let l,h,f;if(t.startsWith("hsl")?[,l,h,f,c]=ks(t):t.startsWith("hwb")?[,l,h,f,c]=Ca(t):[,l,h,f,c]=ni(t,e),Vt.test(r))return["rgb",Math.round(l),Math.round(h),Math.round(f),c];[a,o,i]=Na([l,h,f]),n&&([a,o,i]=J(we,[a,o,i],!0))}return[n?"xyz-d50":"xyz-d65",M(a,T),M(o,T),M(i,T),c]},Cn=(t,e={})=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",format:r="",nullable:s=!1}=e,a=gt({namespace:ua,name:"resolveColorValue",value:t},e),o=mt(a);if(o instanceof st){if(o.isNull)return o;const p=o.item;return S(p),p}if(!qo.test(t)){const p=ye(r,s);return p instanceof P?(F(a,null),p):(F(a,p),S(p),p)}let i="",c=0,l=0,h=0,f=0;if(wt.test(t)){if(r===rt)return F(a,t),t}else if(/^[a-z]+$/.test(t))if(Object.prototype.hasOwnProperty.call(Zn,t)){if(r===rt)return F(a,t),t;[c,l,h]=Zn[t],f=1}else switch(r){case rt:{if(t==="transparent")return F(a,t),t;const p="";return F(a,p),p}case Z:{if(t==="transparent"){const p=["rgb",0,0,0,0];return F(a,p),p}return F(a,null),new P}case ot:default:{if(s&&t!=="transparent")return F(a,null),new P;const p=["rgb",0,0,0,0];return F(a,p),p}}else if(t[0]==="#")[c,l,h,f]=Ea(t);else if(t.startsWith("hsl"))[,c,l,h,f]=ks(t,e);else if(t.startsWith("hwb"))[,c,l,h,f]=Ca(t,e);else if(/^l(?:ab|ch)/.test(t)){let p,m,$;if(t.startsWith("lab")?[i,p,m,$,f]=Or(t,e):[i,p,m,$,f]=Fs(t,e),Vt.test(r)){const b=[i,p,m,$,f];return F(a,b),b}[c,l,h]=Qo([p,m,$])}else if(/^okl(?:ab|ch)/.test(t)){let p,m,$;if(t.startsWith("oklab")?[i,p,m,$,f]=xs(t,e):[i,p,m,$,f]=Ss(t,e),Vt.test(r)){const b=[i,p,m,$,f];return F(a,b),b}[c,l,h]=Jn([p,m,$])}else[,c,l,h,f]=ni(t,e);if(r===Z&&n==="srgb"){const p=["srgb",c/_,l/_,h/_,f];return F(a,p),p}const d=["rgb",Math.round(c),Math.round(l),Math.round(h),f];return F(a,d),d},Ze=(t,e={})=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",format:r="",nullable:s=!1}=e,a=gt({namespace:ua,name:"resolveColorFunc",value:t},e),o=mt(a);if(o instanceof st){if(o.isNull)return o;const D=o.item;return S(D),D}if(!Yn.test(t)){const D=ye(r,s);return D instanceof P?(F(a,null),D):(F(a,D),S(D),D)}const[i,c,l,h,f]=vt(t,e);if(Vt.test(r)||r===Z&&i===n){const D=[i,c,l,h,f];return F(a,D),D}const d=parseFloat(`${c}`),p=parseFloat(`${l}`),m=parseFloat(`${h}`),$=Ye(`${f}`),[b,w,N]=Jn([d,p,m],!0),E=["rgb",b,w,N,$];return F(a,E),E},ka=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",format:r=""}=e;let s="",a,o,i,c,l,h,f;if(r===Z){let d;if(t.startsWith(ut)?d=vt(t,e):d=Nt(t,e),d instanceof P)return d;if([s,l,h,f,c]=d,s===n)return[l,h,f,c];[a,o,i]=J(Ns,[l,h,f],!0)}else if(t.startsWith(ut)){const[,d]=t.match(Yn),[p]=d.replace("/"," ").split(/\s+/);p==="srgb-linear"?[,a,o,i,c]=Ze(t,{format:ot}):([,l,h,f,c]=vt(t),[a,o,i]=J(Ns,[l,h,f],!0))}else[,l,h,f,c]=Nt(t),[a,o,i]=J(Ns,[l,h,f],!0);return[Math.min(Math.max(a,0),1),Math.min(Math.max(o,0),1),Math.min(Math.max(i,0),1),c]},Wr=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(n===Z){let i;if(t.startsWith(ut)?i=Ze(t,e):i=Cn(t,e),i instanceof P)return i;[,r,s,a,o]=i}else if(t.startsWith(ut)){const[,i]=t.match(Yn),[c]=i.replace("/"," ").split(/\s+/);c==="srgb"?([,r,s,a,o]=Ze(t,{format:ot}),r*=_,s*=_,a*=_):[,r,s,a,o]=Ze(t)}else/^(?:ok)?l(?:ab|ch)/.test(t)?([r,s,a,o]=ka(t),[r,s,a]=Yo([r,s,a])):[,r,s,a,o]=Cn(t,{format:ot});return[r,s,a,o]},ri=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{d50:n=!1,format:r=""}=e;let s,a,o,i;if(r===Z){let c;if(t.startsWith(ut)?c=vt(t,e):c=Nt(t,e),c instanceof P)return c;[,s,a,o,i]=c}else if(t.startsWith(ut)){const[,c]=t.match(Yn),[l]=c.replace("/"," ").split(/\s+/);n?l==="xyz-d50"?[,s,a,o,i]=Ze(t,{format:ot}):[,s,a,o,i]=vt(t,e):/^xyz(?:-d65)?$/.test(l)?[,s,a,o,i]=Ze(t,{format:ot}):[,s,a,o,i]=vt(t)}else[,s,a,o,i]=Nt(t,e);return[s,a,o,i]},Fa=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(ma.test(t))return[,r,s,a,o]=ks(t,{format:"hsl"}),n==="hsl"?[Math.round(r),Math.round(s),Math.round(a),o]:[r,s,a,o];let i,c,l;if(n===Z){let h;if(t.startsWith(ut)?h=vt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,c,l,o]=h}else t.startsWith(ut)?[,i,c,l,o]=vt(t):[,i,c,l,o]=Nt(t);return[r,s,a]=Zo([i,c,l],!0),n==="hsl"?[Math.round(r),Math.round(s),Math.round(a),o]:[n===Z&&s===0?g:r,s,a,o]},xa=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(ga.test(t))return[,r,s,a,o]=Ca(t,{format:"hwb"}),n==="hwb"?[Math.round(r),Math.round(s),Math.round(a),o]:[r,s,a,o];let i,c,l;if(n===Z){let h;if(t.startsWith(ut)?h=vt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,c,l,o]=h}else t.startsWith(ut)?[,i,c,l,o]=vt(t):[,i,c,l,o]=Nt(t);return[r,s,a]=gu([i,c,l],!0),n==="hwb"?[Math.round(r),Math.round(s),Math.round(a),o]:[n===Z&&s+a>=100?g:r,s,a,o]},Sa=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(va.test(t))return[,r,s,a,o]=Or(t,{format:ot}),[r,s,a,o];let i,c,l;if(n===Z){let h;if(e.d50=!0,t.startsWith(ut)?h=vt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,c,l,o]=h}else t.startsWith(ut)?[,i,c,l,o]=vt(t,{d50:!0}):[,i,c,l,o]=Nt(t,{d50:!0});return[r,s,a]=ti([i,c,l],!0),[r,s,a,o]},Aa=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(ba.test(t))return[,r,s,a,o]=Fs(t,{format:ot}),[r,s,a,o];let i,c,l;if(n===Z){let h;if(e.d50=!0,t.startsWith(ut)?h=vt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,c,l,o]=h}else t.startsWith(ut)?[,i,c,l,o]=vt(t,{d50:!0}):[,i,c,l,o]=Nt(t,{d50:!0});return[r,s,a]=bu([i,c,l],!0),[r,s,n===Z&&s===0?g:a,o]},Pa=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(wa.test(t))return[,r,s,a,o]=xs(t,{format:ot}),[r,s,a,o];let i,c,l;if(n===Z){let h;if(t.startsWith(ut)?h=vt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,c,l,o]=h}else t.startsWith(ut)?[,i,c,l,o]=vt(t):[,i,c,l,o]=Nt(t);return[r,s,a]=Jo([i,c,l],!0),[r,s,a,o]},Ma=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if($a.test(t))return[,r,s,a,o]=Ss(t,{format:ot}),[r,s,a,o];let i,c,l;if(n===Z){let h;if(t.startsWith(ut)?h=vt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,c,l,o]=h}else t.startsWith(ut)?[,i,c,l,o]=vt(t):[,i,c,l,o]=Nt(t);return[r,s,a]=vu([i,c,l],!0),[r,s,n===Z&&s===0?g:a,o]},As=(t,e={})=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e,s=gt({namespace:ua,name:"resolveColorMix",value:t},e),a=mt(s);if(a instanceof st){if(a.isNull)return a;const k=a.item;return S(k),k}const o=[];if(!Vo.test(t))if(t.startsWith(jn)&&Ko.test(t)){const k=new RegExp(`^(?:${sa}|${aa})$`),O=t.match(Ko);for(const A of O)if(A){let R=As(A,{format:n===rt?n:ot});if(Array.isArray(R)){const[U,G,Y,Q,ct]=R;if(G===0&&Y===0&&Q===0&&ct===0){t="";break}k.test(U)?ct===1?R=`color(${U} ${G} ${Y} ${Q})`:R=`color(${U} ${G} ${Y} ${Q} / ${ct})`:ct===1?R=`${U}(${G} ${Y} ${Q})`:R=`${U}(${G} ${Y} ${Q} / ${ct})`}else if(!Vo.test(R)){t="";break}o.push(R),t=t.replace(A,R)}if(!t)return $e(s,n,r)}else return $e(s,n,r);let i="",c="",l="",h="",f="",d="";if(o.length&&n===rt){const k=new RegExp(`^color-mix\\(\\s*in\\s+(${oa})\\s*,`),[,O]=t.match(k);if(Es.test(O)?[,i,c]=O.match(Es):i=O,o.length===2){let[A,R]=o;A=A.replace(/(?=[()])/g,"\\"),R=R.replace(/(?=[()])/g,"\\");const U=new RegExp(`(${A})(?:\\s+(${_t}))?`),G=new RegExp(`(${R})(?:\\s+(${_t}))?`);[,l,h]=t.match(U),[,f,d]=t.match(G)}else{let[A]=o;A=A.replace(/(?=[()])/g,"\\");const R=`${A}(?:\\s+${_t})?`,U=`(${A})(?:\\s+(${_t}))?`,G=new RegExp(`^${U}$`),Y=new RegExp(`${U}\\s*\\)$`),Q=new RegExp(`^(${Gn})(?:\\s+(${_t}))?$`);if(Y.test(t)){const ct=new RegExp(`(${qn})\\s*,\\s*(${R})\\s*\\)$`),[,Et,Xt]=t.match(ct);[,l,h]=Et.match(Q),[,f,d]=Xt.match(G)}else{const ct=new RegExp(`(${R})\\s*,\\s*(${qn})\\s*\\)$`),[,Et,Xt]=t.match(ct);[,l,h]=Et.match(G),[,f,d]=Xt.match(Q)}}}else{const[,k,O,A]=t.match(mu),R=new RegExp(`^(${Gn})(?:\\s+(${_t}))?$`);[,l,h]=O.match(R),[,f,d]=A.match(R),Es.test(k)?[,i,c]=k.match(Es):i=k}let p,m,$;if(h&&d){const k=parseFloat(h)/q,O=parseFloat(d)/q;if(k<0||k>1||O<0||O>1)return $e(s,n,r);const A=k+O;if(A===0)return $e(s,n,r);p=k/A,m=O/A,$=A<1?A:1}else{if(h){if(p=parseFloat(h)/q,p<0||p>1)return $e(s,n,r);m=1-p}else if(d){if(m=parseFloat(d)/q,m<0||m>1)return $e(s,n,r);p=1-m}else p=Sr,m=Sr;$=1}if(i==="xyz"&&(i="xyz-d65"),n===rt){let k="",O="";if(l.startsWith(jn))k=l;else if(l.startsWith(ut)){const[A,R,U,G,Y]=vt(l,e);Y===1?k=`color(${A} ${R} ${U} ${G})`:k=`color(${A} ${R} ${U} ${G} / ${Y})`}else{const A=Nt(l,e);if(Array.isArray(A)){const[R,U,G,Y,Q]=A;Q===1?R==="rgb"?k=`${R}(${U}, ${G}, ${Y})`:k=`${R}(${U} ${G} ${Y})`:R==="rgb"?k=`${R}a(${U}, ${G}, ${Y}, ${Q})`:k=`${R}(${U} ${G} ${Y} / ${Q})`}else{if(!S(A)||!A)return F(s,""),"";k=A}}if(f.startsWith(jn))O=f;else if(f.startsWith(ut)){const[A,R,U,G,Y]=vt(f,e);Y===1?O=`color(${A} ${R} ${U} ${G})`:O=`color(${A} ${R} ${U} ${G} / ${Y})`}else{const A=Nt(f,e);if(Array.isArray(A)){const[R,U,G,Y,Q]=A;Q===1?R==="rgb"?O=`${R}(${U}, ${G}, ${Y})`:O=`${R}(${U} ${G} ${Y})`:R==="rgb"?O=`${R}a(${U}, ${G}, ${Y}, ${Q})`:O=`${R}(${U} ${G} ${Y} / ${Q})`}else{if(!S(A)||!A)return F(s,""),"";O=A}}if(h&&d)k+=` ${parseFloat(h)}%`,O+=` ${parseFloat(d)}%`;else if(h){const A=parseFloat(h);A!==q*Sr&&(k+=` ${A}%`)}else if(d){const A=q-parseFloat(d);A!==q*Sr&&(k+=` ${A}%`)}if(c){const A=`color-mix(in ${i} ${c} hue, ${k}, ${O})`;return F(s,A),A}else{const A=`color-mix(in ${i}, ${k}, ${O})`;return F(s,A),A}}let b=0,w=0,N=0,E=0;if(/^srgb(?:-linear)?$/.test(i)){let k,O;if(i==="srgb"?(wt.test(l)?k=[g,g,g,g]:k=Wr(l,{colorSpace:i,format:Z}),wt.test(f)?O=[g,g,g,g]:O=Wr(f,{colorSpace:i,format:Z})):(wt.test(l)?k=[g,g,g,g]:k=ka(l,{colorSpace:i,format:Z}),wt.test(f)?O=[g,g,g,g]:O=ka(f,{colorSpace:i,format:Z})),k instanceof P||O instanceof P)return $e(s,n,r);const[A,R,U,G]=k,[Y,Q,ct,Et]=O,Xt=A===g&&Y===g,ve=R===g&&Q===g,ke=U===g&&ct===g,Fe=G===g&&Et===g,[[Yt,Mt,Dt,xe],[Zt,Bt,xt,Ge]]=Br([A,R,U,G],[Y,Q,ct,Et],!0),ht=xe*p,ft=Ge*m;if(E=ht+ft,E===0?(b=Yt*p+Zt*m,w=Mt*p+Bt*m,N=Dt*p+xt*m):(b=(Yt*ht+Zt*ft)/E,w=(Mt*ht+Bt*ft)/E,N=(Dt*ht+xt*ft)/E,E=parseFloat(E.toFixed(3))),n===ot){const $t=[i,Xt?g:M(b,T),ve?g:M(w,T),ke?g:M(N,T),Fe?g:E*$];return F(s,$t),$t}b*=_,w*=_,N*=_}else if(du.test(i)){let k,O;if(wt.test(l)?k=[g,g,g,g]:k=ri(l,{colorSpace:i,d50:i==="xyz-d50",format:Z}),wt.test(f)?O=[g,g,g,g]:O=ri(f,{colorSpace:i,d50:i==="xyz-d50",format:Z}),k instanceof P||O instanceof P)return $e(s,n,r);const[A,R,U,G]=k,[Y,Q,ct,Et]=O,Xt=A===g&&Y===g,ve=R===g&&Q===g,ke=U===g&&ct===g,Fe=G===g&&Et===g,[[Yt,Mt,Dt,xe],[Zt,Bt,xt,Ge]]=Br([A,R,U,G],[Y,Q,ct,Et],!0),ht=xe*p,ft=Ge*m;E=ht+ft;let $t,Rt,Ot;if(E===0?($t=Yt*p+Zt*m,Rt=Mt*p+Bt*m,Ot=Dt*p+xt*m):($t=(Yt*ht+Zt*ft)/E,Rt=(Mt*ht+Bt*ft)/E,Ot=(Dt*ht+xt*ft)/E,E=parseFloat(E.toFixed(3))),n===ot){const sn=[i,Xt?g:M($t,T),ve?g:M(Rt,T),ke?g:M(Ot,T),Fe?g:E*$];return F(s,sn),sn}i==="xyz-d50"?[b,w,N]=Qo([$t,Rt,Ot],!0):[b,w,N]=Jn([$t,Rt,Ot],!0)}else if(/^h(?:sl|wb)$/.test(i)){let k,O;if(i==="hsl"?(wt.test(l)?k=[g,g,g,g]:k=Fa(l,{colorSpace:i,format:Z}),wt.test(f)?O=[g,g,g,g]:O=Fa(f,{colorSpace:i,format:Z})):(wt.test(l)?k=[g,g,g,g]:k=xa(l,{colorSpace:i,format:Z}),wt.test(f)?O=[g,g,g,g]:O=xa(f,{colorSpace:i,format:Z})),k instanceof P||O instanceof P)return $e(s,n,r);const[A,R,U,G]=k,[Y,Q,ct,Et]=O,Xt=G===g&&Et===g;let[[ve,ke,Fe,Yt],[Mt,Dt,xe,Zt]]=Br([A,R,U,G],[Y,Q,ct,Et],!0);c&&([ve,Mt]=_i(ve,Mt,c));const Bt=Yt*p,xt=Zt*m;E=Bt+xt;const Ge=(ve*p+Mt*m)%pe;let ht,ft;if(E===0?(ht=ke*p+Dt*m,ft=Fe*p+xe*m):(ht=(ke*Bt+Dt*xt)/E,ft=(Fe*Bt+xe*xt)/E,E=parseFloat(E.toFixed(3))),[b,w,N]=Wr(`${i}(${Ge} ${ht} ${ft})`),n===ot){const $t=["srgb",M(b/_,T),M(w/_,T),M(N/_,T),Xt?g:E*$];return F(s,$t),$t}}else if(/^(?:ok)?lch$/.test(i)){let k,O;if(i==="lch"?(wt.test(l)?k=[g,g,g,g]:k=Aa(l,{colorSpace:i,format:Z}),wt.test(f)?O=[g,g,g,g]:O=Aa(f,{colorSpace:i,format:Z})):(wt.test(l)?k=[g,g,g,g]:k=Ma(l,{colorSpace:i,format:Z}),wt.test(f)?O=[g,g,g,g]:O=Ma(f,{colorSpace:i,format:Z})),k instanceof P||O instanceof P)return $e(s,n,r);const[A,R,U,G]=k,[Y,Q,ct,Et]=O,Xt=A===g&&Y===g,ve=R===g&&Q===g,ke=U===g&&ct===g,Fe=G===g&&Et===g;let[[Yt,Mt,Dt,xe],[Zt,Bt,xt,Ge]]=Br([A,R,U,G],[Y,Q,ct,Et],!0);c&&([Dt,xt]=_i(Dt,xt,c));const ht=xe*p,ft=Ge*m;E=ht+ft;const $t=(Dt*p+xt*m)%pe;let Rt,Ot;if(E===0?(Rt=Yt*p+Zt*m,Ot=Mt*p+Bt*m):(Rt=(Yt*ht+Zt*ft)/E,Ot=(Mt*ht+Bt*ft)/E,E=parseFloat(E.toFixed(3))),n===ot){const sn=[i,Xt?g:M(Rt,T),ve?g:M(Ot,T),ke?g:M($t,T),Fe?g:E*$];return F(s,sn),sn}[,b,w,N]=Cn(`${i}(${Rt} ${Ot} ${$t})`)}else{let k,O;if(i==="lab"?(wt.test(l)?k=[g,g,g,g]:k=Sa(l,{colorSpace:i,format:Z}),wt.test(f)?O=[g,g,g,g]:O=Sa(f,{colorSpace:i,format:Z})):(wt.test(l)?k=[g,g,g,g]:k=Pa(l,{colorSpace:i,format:Z}),wt.test(f)?O=[g,g,g,g]:O=Pa(f,{colorSpace:i,format:Z})),k instanceof P||O instanceof P)return $e(s,n,r);const[A,R,U,G]=k,[Y,Q,ct,Et]=O,Xt=A===g&&Y===g,ve=R===g&&Q===g,ke=U===g&&ct===g,Fe=G===g&&Et===g,[[Yt,Mt,Dt,xe],[Zt,Bt,xt,Ge]]=Br([A,R,U,G],[Y,Q,ct,Et],!0),ht=xe*p,ft=Ge*m;E=ht+ft;let $t,Rt,Ot;if(E===0?($t=Yt*p+Zt*m,Rt=Mt*p+Bt*m,Ot=Dt*p+xt*m):($t=(Yt*ht+Zt*ft)/E,Rt=(Mt*ht+Bt*ft)/E,Ot=(Dt*ht+xt*ft)/E,E=parseFloat(E.toFixed(3))),n===ot){const sn=[i,Xt?g:M($t,T),ve?g:M(Rt,T),ke?g:M(Ot,T),Fe?g:E*$];return F(s,sn),sn}[,b,w,N]=Cn(`${i}(${$t} ${Rt} ${Ot})`)}const D=["rgb",Math.round(b),Math.round(w),Math.round(N),parseFloat((E*$).toFixed(3))];return F(s,D),D},{CloseParen:si,Comment:yu,EOF:Nu,Ident:Eu,Whitespace:Cu}=v,ku="css-var",ai=new RegExp(gs),oi=new RegExp(kr);function ii(t,e={}){if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{customProperty:n={}}=e,r=[];for(;t.length;){const o=t.shift();if(!Array.isArray(o))throw new TypeError(`${o} is not an array.`);const[i,c]=o;if(i===si)break;if(c===ia){const[l,h]=ii(t,e);t=l,h&&r.push(h)}else if(i===Eu)if(c.startsWith("--")){let l;Object.hasOwnProperty.call(n,c)?l=n[c]:typeof n.callback=="function"&&(l=n.callback(c)),l&&r.push(l)}else c&&r.push(c)}let s=!1;if(r.length>1){const o=r[r.length-1];s=fn(o)}let a="";for(let o of r){if(o=o.trim(),oi.test(o)){const i=Tr(o,e);S(i)&&(s?fn(i)&&(a=i):a=i)}else ai.test(o)?(o=Gr(o,e),s?fn(o)&&(a=o):a=o):o&&!/^(?:inherit|initial|revert(?:-layer)?|unset)$/.test(o)&&(s?fn(o)&&(a=o):a=o);if(a)break}return[t,a]}function Fu(t,e={}){const n=[];for(;t.length;){const r=t.shift(),[s="",a=""]=r;if(a===ia){const[o,i]=ii(t,e);if(!i)return new P;t=o,n.push(i)}else switch(s){case si:{n.length&&n[n.length-1]===" "?n.splice(-1,1,a):n.push(a);break}case Cu:{if(n.length){const o=n[n.length-1];S(o)&&!o.endsWith("(")&&o!==" "&&n.push(a)}break}default:s!==yu&&s!==Nu&&n.push(a)}}return n}function Tr(t,e={}){const{format:n=""}=e;if(S(t)){if(!oi.test(t)||n===rt)return t;t=t.trim()}else throw new TypeError(`${t} is not a string.`);const r=gt({namespace:ku,name:"resolveVar",value:t},e),s=mt(r);if(s instanceof st)return s.isNull?s:s.item;const a=Ke({css:t}),o=Fu(a,e);if(Array.isArray(o)){let i=o.join("");return ai.test(i)&&(i=Gr(i,e)),F(r,i),i}else return F(r,null),new P}const xu=(t,e={})=>{const n=Tr(t,e);return S(n)?n:""};function At(t,e){return[t[0]*e[0]+t[1]*e[1]+t[2]*e[2],t[3]*e[0]+t[4]*e[1]+t[5]*e[2],t[6]*e[0]+t[7]*e[1]+t[8]*e[2]]}const Su=[.955473421488075,-.02309845494876471,.06325924320057072,-.0283697093338637,1.0099953980813041,.021041441191917323,.012314014864481998,-.020507649298898964,1.330365926242124];/**
 * Bradford chromatic adaptation from D50 to D65
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function _e(t){return At(Su,t)}const Au=[1.0479297925449969,.022946870601609652,-.05019226628920524,.02962780877005599,.9904344267538799,-.017073799063418826,-.009243040646204504,.015055191490298152,.7518742814281371];/**
 * Bradford chromatic adaptation from D65 to D50
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_ChromAdapt.html
 */function He(t){return At(Au,t)}/**
 * @param {number} hue - Hue as degrees 0..360
 * @param {number} sat - Saturation as percentage 0..100
 * @param {number} light - Lightness as percentage 0..100
 * @return {number[]} Array of sRGB components; in-gamut colors in range [0..1]
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hslToRgb.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hslToRgb.js
 */function li(t){let e=t[0]%360;const n=t[1]/100,r=t[2]/100;return e<0&&(e+=360),[Da(0,e,n,r),Da(8,e,n,r),Da(4,e,n,r)]}function Da(t,e,n,r){const s=(t+e/30)%12;return r-n*Math.min(r,1-r)*Math.max(-1,Math.min(s-3,9-s,1))}/**
 * @param {number} hue -  Hue as degrees 0..360
 * @param {number} white -  Whiteness as percentage 0..100
 * @param {number} black -  Blackness as percentage 0..100
 * @return {number[]} Array of RGB components 0..1
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hwbToRgb.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hwbToRgb.js
 */function Pu(t){const e=t[0],n=t[1]/100,r=t[2]/100;if(n+r>=1){const o=n/(n+r);return[o,o,o]}const s=li([e,100,50]),a=1-n-r;return[s[0]*a+n,s[1]*a+n,s[2]*a+n]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Mu(t){const e=t[2]*Math.PI/180;return[t[0],t[1]*Math.cos(e),t[1]*Math.sin(e)]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Du(t){const e=180*Math.atan2(t[2],t[1])/Math.PI;return[t[0],Math.sqrt(Math.pow(t[1],2)+Math.pow(t[2],2)),e>=0?e:e+360]}const Qn=[.3457/.3585,1,.2958/.3585];/**
 * Convert Lab to D50-adapted XYZ
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */function ci(t){const e=903.2962962962963,n=216/24389,r=(t[0]+16)/116,s=t[1]/500+r,a=r-t[2]/200;return[(Math.pow(s,3)>n?Math.pow(s,3):(116*s-16)/e)*Qn[0],(t[0]>8?Math.pow((t[0]+16)/116,3):t[0]/e)*Qn[1],(Math.pow(a,3)>n?Math.pow(a,3):(116*a-16)/e)*Qn[2]]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function ui(t){const e=t[2]*Math.PI/180;return[t[0],t[1]*Math.cos(e),t[1]*Math.sin(e)]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function hi(t){const e=180*Math.atan2(t[2],t[1])/Math.PI;return[t[0],Math.sqrt(t[1]**2+t[2]**2),e>=0?e:e+360]}const Bu=[1.2268798758459243,-.5578149944602171,.2813910456659647,-.0405757452148008,1.112286803280317,-.0717110580655164,-.0763729366746601,-.4214933324022432,1.5869240198367816],Ru=[1,.3963377773761749,.2158037573099136,1,-.1055613458156586,-.0638541728258133,1,-.0894841775298119,-1.2914855480194092];/**
 * Given OKLab, convert to XYZ relative to D65
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function Ba(t){const e=At(Ru,t);return At(Bu,[e[0]**3,e[1]**3,e[2]**3])}/**
 * Assuming XYZ is relative to D50, convert to CIE Lab
 * from CIE standard, which now defines these as a rational fraction
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function fi(t){const e=Ra(t[0]/Qn[0]),n=Ra(t[1]/Qn[1]);return[116*n-16,500*(e-n),200*(n-Ra(t[2]/Qn[2]))]}const Ou=216/24389,Wu=24389/27;function Ra(t){return t>Ou?Math.cbrt(t):(Wu*t+16)/116}const Tu=[.819022437996703,.3619062600528904,-.1288737815209879,.0329836539323885,.9292868615863434,.0361446663506424,.0481771893596242,.2642395317527308,.6335478284694309],Iu=[.210454268309314,.7936177747023054,-.0040720430116193,1.9779985324311684,-2.42859224204858,.450593709617411,.0259040424655478,.7827717124575296,-.8086757549230774];/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * XYZ <-> LMS matrices recalculated for consistent reference white
 * @see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484
 */function Oa(t){const e=At(Tu,t);return At(Iu,[Math.cbrt(e[0]),Math.cbrt(e[1]),Math.cbrt(e[2])])}const Lu=[30757411/17917100,-6372589/17917100,-4539589/17917100,-.666684351832489,1.616481236634939,467509/29648200,792561/44930125,-1921689/44930125,.942103121235474];/**
 * Convert XYZ to linear-light rec2020
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const _u=[446124/178915,-333277/357830,-72051/178915,-14852/17905,63121/35810,423/17905,11844/330415,-50337/660830,316169/330415];/**
 * Convert XYZ to linear-light P3
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Hu(t){return At(_u,t)}const Uu=[1.3457868816471583,-.25557208737979464,-.05110186497554526,-.5446307051249019,1.5082477428451468,.02052744743642139,0,0,1.2119675456389452];/**
 * Convert D50 XYZ to linear-light prophoto-rgb
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */const zu=[1829569/896150,-506331/896150,-308931/896150,-851781/878810,1648619/878810,36519/878810,16779/1248040,-147721/1248040,1266979/1248040];/**
 * Convert XYZ to linear-light a98-rgb
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const ju=[12831/3959,-329/214,-1974/3959,-851781/878810,1648619/878810,36519/878810,705/12673,-2585/12673,705/667];/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Ir(t){return At(ju,t)}/**
 * Convert an array of linear-light rec2020 RGB  in the range 0.0-1.0
 * to gamma corrected form ITU-R BT.2020-2 p.4
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const pi=1.09929682680944,Gu=.018053968510807;function Wa(t){const e=t<0?-1:1,n=Math.abs(t);return n>Gu?e*(pi*Math.pow(n,.45)-(pi-1)):4.5*t}/**
 * Convert an array of linear-light sRGB values in the range 0.0-1.0 to gamma corrected form
 * Extended transfer function:
 *  For negative values, linear portion extends on reflection
 *  of axis, then uses reflected pow below that
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://en.wikipedia.org/wiki/SRGB
 */function Lr(t){return[Ta(t[0]),Ta(t[1]),Ta(t[2])]}function Ta(t){const e=t<0?-1:1,n=Math.abs(t);return n>.0031308?e*(1.055*Math.pow(n,1/2.4)-.055):12.92*t}/**
 * Convert an array of linear-light display-p3 RGB in the range 0.0-1.0
 * to gamma corrected form
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function qu(t){return Lr(t)}/**
 * Convert an array of linear-light prophoto-rgb in the range 0.0-1.0
 * to gamma corrected form.
 * Transfer curve is gamma 1.8 with a small linear portion.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const Vu=1/512;function Ia(t){const e=t<0?-1:1,n=Math.abs(t);return n>=Vu?e*Math.pow(n,1/1.8):16*t}/**
 * Convert an array of linear-light a98-rgb in the range 0.0-1.0
 * to gamma corrected form. Negative values are also now accepted
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function La(t){const e=t<0?-1:1,n=Math.abs(t);return e*Math.pow(n,256/563)}/**
 * Convert an array of rec2020 RGB values in the range 0.0 - 1.0
 * to linear light (un-companded) form.
 * ITU-R BT.2020-2 p.4
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const di=1.09929682680944,Ku=.018053968510807;function _a(t){const e=t<0?-1:1,n=Math.abs(t);return n<4.5*Ku?t/4.5:e*Math.pow((n+di-1)/di,1/.45)}const Xu=[63426534/99577255,20160776/139408157,47086771/278816314,26158966/99577255,.677998071518871,8267143/139408157,0,19567812/697040785,1.0609850577107909];/**
 * Convert an array of linear-light rec2020 values to CIE XYZ
 * using  D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 *//**
 * Convert an array of of sRGB values where in-gamut values are in the range
 * [0 - 1] to linear light (un-companded) form.
 * Extended transfer function:
 *  For negative values, linear portion is extended on reflection of axis,
 *  then reflected power function is used.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://en.wikipedia.org/wiki/SRGB
 */function Ps(t){return[Ha(t[0]),Ha(t[1]),Ha(t[2])]}function Ha(t){const e=t<0?-1:1,n=Math.abs(t);return n<=.04045?t/12.92:e*Math.pow((n+.055)/1.055,2.4)}/**
 * Convert an array of display-p3 RGB values in the range 0.0 - 1.0
 * to linear light (un-companded) form.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Yu(t){return Ps(t)}const Zu=[608311/1250200,189793/714400,198249/1000160,35783/156275,247089/357200,198249/2500400,0,32229/714400,5220557/5000800];/**
 * Convert an array of linear-light display-p3 values to CIE XYZ
 * using D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */function Ju(t){return At(Zu,t)}/**
 * Convert an array of prophoto-rgb values where in-gamut Colors are in the
 * range [0.0 - 1.0] to linear light (un-companded) form. Transfer curve is
 * gamma 1.8 with a small linear portion. Extended transfer function
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const Qu=16/512;function Ua(t){const e=t<0?-1:1,n=Math.abs(t);return n<=Qu?t/16:e*Math.pow(n,1.8)}const th=[.7977666449006423,.13518129740053308,.0313477341283922,.2880748288194013,.711835234241873,8993693872564e-17,0,0,.8251046025104602];/**
 * Convert an array of linear-light prophoto-rgb values to CIE D50 XYZ.
 * Matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see see https://github.com/w3c/csswg-drafts/issues/7675
 */function za(t){const e=t<0?-1:1,n=Math.abs(t);return e*Math.pow(n,563/256)}const eh=[573536/994567,263643/1420810,187206/994567,591459/1989134,6239551/9945670,374412/4972835,53769/1989134,351524/4972835,4929758/4972835];/**
 * Convert an array of linear-light a98-rgb values to CIE XYZ
 * http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 * has greater numerical precision than section ******* of
 * https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf
 * but the values below were calculated from first principles
 * from the chromaticity coordinates of R G B W
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 * @see https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/matrixmaker.html
 */const nh=[506752/1228815,87881/245763,12673/70218,87098/409605,175762/245763,12673/175545,7918/409605,87881/737289,1001167/1053270];/**
 * Convert an array of linear-light sRGB values to CIE XYZ
 * using sRGB's own white, D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function _r(t){return At(nh,t)}/**
 * Convert an array of gamma-corrected sRGB values in the 0.0 to 1.0 range to HSL.
 *
 * @param {Color} RGB [r, g, b]
 * - Red component 0..1
 * - Green component 0..1
 * - Blue component 0..1
 * @return {number[]} Array of HSL values: Hue as degrees 0..360, Saturation and Lightness as percentages 0..100
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/utilities.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/better-rgbToHsl.js
 */function rh(t){const e=t[0],n=t[1],r=t[2],s=Math.max(e,n,r),a=Math.min(e,n,r),o=(a+s)/2,i=s-a;let c=Number.NaN,l=0;if(Math.round(1e5*i)!==0){const h=Math.round(1e5*o);switch(l=h===0||h===1e5?0:(s-o)/Math.min(o,1-o),s){case e:c=(n-r)/i+(n<r?6:0);break;case n:c=(r-e)/i+2;break;case r:c=(e-n)/i+4}c*=60}return l<0&&(c+=180,l=Math.abs(l)),c>=360&&(c-=360),[c,100*l,100*o]}function sh(t){const e=t[0],n=t[1],r=t[2],s=Math.max(e,n,r),a=Math.min(e,n,r);let o=Number.NaN;const i=s-a;if(i!==0){switch(s){case e:o=(n-r)/i+(n<r?6:0);break;case n:o=(r-e)/i+2;break;case r:o=(e-n)/i+4}o*=60}return o>=360&&(o-=360),o}function ah(t){let e=t;return e=Ps(e),e=_r(e),e=He(e),e}function ja(t){let e=t;return e=_e(e),e=Ir(e),e=Lr(e),e}function oh(t){let e=t;return e=li(e),e=Ps(e),e=_r(e),e=He(e),e}function ih(t){let e=t;return e=_e(e),e=Ir(e),e=Lr(e),e=rh(e),e}function lh(t){let e=t;return e=Pu(e),e=Ps(e),e=_r(e),e=He(e),e}function ch(t){let e=t;e=_e(e),e=Ir(e);const n=Lr(e),r=Math.min(n[0],n[1],n[2]),s=1-Math.max(n[0],n[1],n[2]);return[sh(n),100*r,100*s]}function uh(t){let e=t;return e=ci(e),e}function hh(t){let e=t;return e=fi(e),e}function fh(t){let e=t;return e=Mu(e),e=ci(e),e}function ph(t){let e=t;return e=fi(e),e=Du(e),e}function dh(t){let e=t;return e=Ba(e),e=He(e),e}function mh(t){let e=t;return e=_e(e),e=Oa(e),e}function gh(t){let e=t;return e=ui(e),e=Ba(e),e=He(e),e}function mi(t){let e=t;return e=_e(e),e=Oa(e),e=hi(e),e}function vh(t){let e=t;return e=_r(e),e=He(e),e}function bh(t){let e=t;return e=_e(e),e=Ir(e),e}function wh(t){let e=t;/**
 * Convert an array of a98-rgb values in the range 0.0 - 1.0
 * to linear light (un-companded) form. Negative values are also now accepted
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */var n;return e=[za((n=e)[0]),za(n[1]),za(n[2])],e=At(eh,e),e=He(e),e}function $h(t){let e=t;var n;return e=_e(e),e=At(zu,e),e=[La((n=e)[0]),La(n[1]),La(n[2])],e}function yh(t){let e=t;return e=Yu(e),e=Ju(e),e=He(e),e}function Nh(t){let e=t;return e=_e(e),e=Hu(e),e=qu(e),e}function Eh(t){let e=t;var n;return e=[_a((n=e)[0]),_a(n[1]),_a(n[2])],e=At(Xu,e),e=He(e),e}function Ch(t){let e=t;var n;return e=_e(e),e=At(Lu,e),e=[Wa((n=e)[0]),Wa(n[1]),Wa(n[2])],e}function kh(t){let e=t;var n;return e=[Ua((n=e)[0]),Ua(n[1]),Ua(n[2])],e=At(th,e),e}function Fh(t){let e=t;var n;return e=At(Uu,e),e=[Ia((n=e)[0]),Ia(n[1]),Ia(n[2])],e}function xh(t){let e=t;return e=He(e),e}function Sh(t){let e=t;return e=_e(e),e}function Ah(t){return t[0]>=-1e-4&&t[0]<=1.0001&&t[1]>=-1e-4&&t[1]<=1.0001&&t[2]>=-1e-4&&t[2]<=1.0001}function gi(t){return[t[0]<0?0:t[0]>1?1:t[0],t[1]<0?0:t[1]>1?1:t[1],t[2]<0?0:t[2]>1?1:t[2]]}/**
 * @license MIT https://github.com/facelessuser/coloraide/blob/main/LICENSE.md
 */function Ph(t,e,n){const r=t[0],s=t[2];let a=e(t);const o=e([r,0,s]);for(let i=0;i<4;i++){if(i>0){const l=n(a);l[0]=r,l[2]=s,a=e(l)}const c=Mh(o,a);if(!c)break;a=c}return gi(a)}function Mh(t,e){let n=1/0,r=-1/0;const s=[0,0,0];for(let a=0;a<3;a++){const o=t[a],i=e[a]-o;s[a]=i;const c=0,l=1;if(i){const h=1/i,f=(c-o)*h,d=(l-o)*h;r=Math.max(Math.min(f,d),r),n=Math.min(Math.max(f,d),n)}else if(o<c||o>l)return!1}return!(r>n||n<0)&&(r<0&&(r=n),!!isFinite(r)&&[t[0]+s[0]*r,t[1]+s[1]*r,t[2]+s[2]*r])}const Dh={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};function vi(t){const[e,n,r]=t.map(s=>s<=.03928?s/12.92:Math.pow((s+.055)/1.055,2.4));return .2126*e+.7152*n+.0722*r}function bi(t,e){const n=vi(t),r=vi(e);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)}var y,B;function Pt(t){return[Number.isNaN(t[0])?0:t[0],Number.isNaN(t[1])?0:t[1],Number.isNaN(t[2])?0:t[2]]}function wi(t){switch(t.colorNotation){case y.HEX:case y.RGB:case y.sRGB:return{...t,colorNotation:y.XYZ_D50,channels:ah(Pt(t.channels))};case y.Linear_sRGB:return{...t,colorNotation:y.XYZ_D50,channels:vh(Pt(t.channels))};case y.Display_P3:return{...t,colorNotation:y.XYZ_D50,channels:yh(Pt(t.channels))};case y.Rec2020:return{...t,colorNotation:y.XYZ_D50,channels:Eh(Pt(t.channels))};case y.A98_RGB:return{...t,colorNotation:y.XYZ_D50,channels:wh(Pt(t.channels))};case y.ProPhoto_RGB:return{...t,colorNotation:y.XYZ_D50,channels:kh(Pt(t.channels))};case y.HSL:return{...t,colorNotation:y.XYZ_D50,channels:oh(Pt(t.channels))};case y.HWB:return{...t,colorNotation:y.XYZ_D50,channels:lh(Pt(t.channels))};case y.Lab:return{...t,colorNotation:y.XYZ_D50,channels:uh(Pt(t.channels))};case y.OKLab:return{...t,colorNotation:y.XYZ_D50,channels:dh(Pt(t.channels))};case y.LCH:return{...t,colorNotation:y.XYZ_D50,channels:fh(Pt(t.channels))};case y.OKLCH:return{...t,colorNotation:y.XYZ_D50,channels:gh(Pt(t.channels))};case y.XYZ_D50:return{...t,colorNotation:y.XYZ_D50,channels:Pt(t.channels)};case y.XYZ_D65:return{...t,colorNotation:y.XYZ_D50,channels:xh(Pt(t.channels))};default:throw new Error("Unsupported color notation")}}(function(t){t.A98_RGB="a98-rgb",t.Display_P3="display-p3",t.HEX="hex",t.HSL="hsl",t.HWB="hwb",t.LCH="lch",t.Lab="lab",t.Linear_sRGB="srgb-linear",t.OKLCH="oklch",t.OKLab="oklab",t.ProPhoto_RGB="prophoto-rgb",t.RGB="rgb",t.sRGB="srgb",t.Rec2020="rec2020",t.XYZ_D50="xyz-d50",t.XYZ_D65="xyz-d65"})(y||(y={})),function(t){t.ColorKeyword="color-keyword",t.HasAlpha="has-alpha",t.HasDimensionValues="has-dimension-values",t.HasNoneKeywords="has-none-keywords",t.HasNumberValues="has-number-values",t.HasPercentageAlpha="has-percentage-alpha",t.HasPercentageValues="has-percentage-values",t.HasVariableAlpha="has-variable-alpha",t.Hex="hex",t.LegacyHSL="legacy-hsl",t.LegacyRGB="legacy-rgb",t.NamedColor="named-color",t.RelativeColorSyntax="relative-color-syntax",t.ColorMix="color-mix",t.ContrastColor="contrast-color",t.Experimental="experimental"}(B||(B={}));const $i=new Set([y.A98_RGB,y.Display_P3,y.HEX,y.Linear_sRGB,y.ProPhoto_RGB,y.RGB,y.sRGB,y.Rec2020,y.XYZ_D50,y.XYZ_D65]);function tr(t,e){const n={...t};if(t.colorNotation!==e){const r=wi(n);switch(e){case y.HEX:case y.RGB:n.colorNotation=y.RGB,n.channels=ja(r.channels);break;case y.sRGB:n.colorNotation=y.sRGB,n.channels=ja(r.channels);break;case y.Linear_sRGB:n.colorNotation=y.Linear_sRGB,n.channels=bh(r.channels);break;case y.Display_P3:n.colorNotation=y.Display_P3,n.channels=Nh(r.channels);break;case y.Rec2020:n.colorNotation=y.Rec2020,n.channels=Ch(r.channels);break;case y.ProPhoto_RGB:n.colorNotation=y.ProPhoto_RGB,n.channels=Fh(r.channels);break;case y.A98_RGB:n.colorNotation=y.A98_RGB,n.channels=$h(r.channels);break;case y.HSL:n.colorNotation=y.HSL,n.channels=ih(r.channels);break;case y.HWB:n.colorNotation=y.HWB,n.channels=ch(r.channels);break;case y.Lab:n.colorNotation=y.Lab,n.channels=hh(r.channels);break;case y.LCH:n.colorNotation=y.LCH,n.channels=ph(r.channels);break;case y.OKLCH:n.colorNotation=y.OKLCH,n.channels=mi(r.channels);break;case y.OKLab:n.colorNotation=y.OKLab,n.channels=mh(r.channels);break;case y.XYZ_D50:n.colorNotation=y.XYZ_D50,n.channels=r.channels;break;case y.XYZ_D65:n.colorNotation=y.XYZ_D65,n.channels=Sh(r.channels);break;default:throw new Error("Unsupported color notation")}}else n.channels=Pt(t.channels);if(e===t.colorNotation)n.channels=Kt(t.channels,[0,1,2],n.channels,[0,1,2]);else if($i.has(e)&&$i.has(t.colorNotation))n.channels=Kt(t.channels,[0,1,2],n.channels,[0,1,2]);else switch(e){case y.HSL:switch(t.colorNotation){case y.HWB:n.channels=Kt(t.channels,[0],n.channels,[0]);break;case y.Lab:case y.OKLab:n.channels=Kt(t.channels,[2],n.channels,[0]);break;case y.LCH:case y.OKLCH:n.channels=Kt(t.channels,[0,1,2],n.channels,[2,1,0])}break;case y.HWB:switch(t.colorNotation){case y.HSL:n.channels=Kt(t.channels,[0],n.channels,[0]);break;case y.LCH:case y.OKLCH:n.channels=Kt(t.channels,[0],n.channels,[2])}break;case y.Lab:case y.OKLab:switch(t.colorNotation){case y.HSL:n.channels=Kt(t.channels,[0],n.channels,[2]);break;case y.Lab:case y.OKLab:n.channels=Kt(t.channels,[0,1,2],n.channels,[0,1,2]);break;case y.LCH:case y.OKLCH:n.channels=Kt(t.channels,[0],n.channels,[0])}break;case y.LCH:case y.OKLCH:switch(t.colorNotation){case y.HSL:n.channels=Kt(t.channels,[0,1,2],n.channels,[2,1,0]);break;case y.HWB:n.channels=Kt(t.channels,[0],n.channels,[2]);break;case y.Lab:case y.OKLab:n.channels=Kt(t.channels,[0],n.channels,[0]);break;case y.LCH:case y.OKLCH:n.channels=Kt(t.channels,[0,1,2],n.channels,[0,1,2])}}return n.channels=Bh(n.channels,e),n}function Bh(t,e){const n=[...t];switch(e){case y.HSL:!Number.isNaN(n[1])&&Hr(n[1],4)<=0&&(n[0]=Number.NaN);break;case y.HWB:Math.max(0,Hr(n[1],4))+Math.max(0,Hr(n[2],4))>=100&&(n[0]=Number.NaN);break;case y.LCH:!Number.isNaN(n[1])&&Hr(n[1],4)<=0&&(n[2]=Number.NaN);break;case y.OKLCH:!Number.isNaN(n[1])&&Hr(n[1],6)<=0&&(n[2]=Number.NaN)}return n}function Kt(t,e,n,r){const s=[...n];for(const a of e)Number.isNaN(t[e[a]])&&(s[r[a]]=Number.NaN);return s}function yi(t){const e=new Map;switch(t.colorNotation){case y.RGB:case y.HEX:e.set("r",et(255*t.channels[0])),e.set("g",et(255*t.channels[1])),e.set("b",et(255*t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case y.HSL:e.set("h",et(t.channels[0])),e.set("s",et(t.channels[1])),e.set("l",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case y.HWB:e.set("h",et(t.channels[0])),e.set("w",et(t.channels[1])),e.set("b",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case y.Lab:case y.OKLab:e.set("l",et(t.channels[0])),e.set("a",et(t.channels[1])),e.set("b",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case y.LCH:case y.OKLCH:e.set("l",et(t.channels[0])),e.set("c",et(t.channels[1])),e.set("h",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case y.sRGB:case y.A98_RGB:case y.Display_P3:case y.Rec2020:case y.Linear_sRGB:case y.ProPhoto_RGB:e.set("r",et(t.channels[0])),e.set("g",et(t.channels[1])),e.set("b",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case y.XYZ_D50:case y.XYZ_D65:e.set("x",et(t.channels[0])),e.set("y",et(t.channels[1])),e.set("z",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha))}return e}function Ni(t){const e=new Map(t);for(const[n,r]of t)Number.isNaN(r[4].value)&&e.set(n,et(0));return e}function et(t){return Number.isNaN(t)?[v.Number,"none",-1,-1,{value:Number.NaN,type:x.Number}]:[v.Number,t.toString(),-1,-1,{value:t,type:x.Number}]}function Hr(t,e=7){if(Number.isNaN(t))return 0;const n=Math.pow(10,e);return Math.round(t*n)/n}function V(t,e,n,r){return Math.min(Math.max(t/e,n),r)}const Rh=/[A-Z]/g;function lt(t){return t.replace(Rh,e=>String.fromCharCode(e.charCodeAt(0)+32))}function Ur(t,e,n){if(bt(t)&&lt(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(tt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,100,-2147483647,2147483647);return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,1,-2147483647,2147483647);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}const Oh=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","xyz","xyz-d50","xyz-d65"]);function Wh(t,e){const n=[],r=[],s=[],a=[];let o,i,c=!1,l=!1;const h={colorNotation:y.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([])};let f=n;for(let b=0;b<t.value.length;b++){let w=t.value[b];if(ae(w)||oe(w))for(;ae(t.value[b+1])||oe(t.value[b+1]);)b++;else if(f===n&&n.length&&(f=r),f===r&&r.length&&(f=s),L(w)&&es(w.value)&&w.value[4].value==="/"){if(f===a)return!1;f=a}else{if(se(w)){if(f===a&&lt(w.getName())==="var"){h.syntaxFlags.add(B.HasVariableAlpha),f.push(w);continue}if(!ls.has(lt(w.getName())))return!1;const[[N]]=br([[w]],{censorIntoStandardRepresentableValues:!0,globals:i,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!N||!L(N)||!pt(N.value))return!1;Number.isNaN(N.value[4].value)&&(N.value[4].value=0),w=N}if(f===n&&n.length===0&&L(w)&&bt(w.value)&&Oh.has(lt(w.value[4].value))){if(c)return!1;c=lt(w.value[4].value),h.colorNotation=Th(c),l&&(l.colorNotation!==h.colorNotation&&(l=tr(l,h.colorNotation)),o=yi(l),i=Ni(o))}else if(f===n&&n.length===0&&L(w)&&bt(w.value)&&lt(w.value[4].value)==="from"){if(l||c)return!1;for(;ae(t.value[b+1])||oe(t.value[b+1]);)b++;if(b++,w=t.value[b],l=e(w),l===!1)return!1;l.syntaxFlags.has(B.Experimental)&&h.syntaxFlags.add(B.Experimental),h.syntaxFlags.add(B.RelativeColorSyntax)}else{if(!L(w))return!1;if(bt(w.value)&&o&&o.has(lt(w.value[4].value))){f.push(new j(o.get(lt(w.value[4].value))));continue}f.push(w)}}}if(!c||f.length!==1||n.length!==1||r.length!==1||s.length!==1||!L(n[0])||!L(r[0])||!L(s[0])||o&&!o.has("alpha"))return!1;const d=Ur(n[0].value,0,h);if(!d||!H(d))return!1;const p=Ur(r[0].value,1,h);if(!p||!H(p))return!1;const m=Ur(s[0].value,2,h);if(!m||!H(m))return!1;const $=[d,p,m];if(a.length===1)if(h.syntaxFlags.add(B.HasAlpha),L(a[0])){const b=Ur(a[0].value,3,h);if(!b||!H(b))return!1;$.push(b)}else h.alpha=a[0];else if(o&&o.has("alpha")){const b=Ur(o.get("alpha"),3,h);if(!b||!H(b))return!1;$.push(b)}return h.channels=[$[0][4].value,$[1][4].value,$[2][4].value],$.length===4&&(h.alpha=$[3][4].value),h}function Th(t){switch(t){case"srgb":return y.sRGB;case"srgb-linear":return y.Linear_sRGB;case"display-p3":return y.Display_P3;case"a98-rgb":return y.A98_RGB;case"prophoto-rgb":return y.ProPhoto_RGB;case"rec2020":return y.Rec2020;case"xyz":case"xyz-d65":return y.XYZ_D65;case"xyz-d50":return y.XYZ_D50;default:throw new Error("Unknown color space name: "+t)}}const Ih=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","lab","oklab","xyz","xyz-d50","xyz-d65"]),Ga=new Set(["hsl","hwb","lch","oklch"]),Lh=new Set(["shorter","longer","increasing","decreasing"]);function _h(t,e){let n=null,r=null,s=null,a=!1;for(let o=0;o<t.value.length;o++){const i=t.value[o];if(!ae(i)&&!oe(i)){if(L(i)&&bt(i.value)){if(!n&&lt(i.value[4].value)==="in"){n=i;continue}if(n&&!r){r=lt(i.value[4].value);continue}if(n&&r&&!s&&Ga.has(r)){s=lt(i.value[4].value);continue}if(n&&r&&s&&!a&&lt(i.value[4].value)==="hue"){a=!0;continue}return!1}return!(!L(i)||!be(i.value))&&!!r&&(s||a?!!(r&&s&&a&&Ga.has(r)&&Lh.has(s))&&Ei(r,s,qa(t.value.slice(o+1),e)):Ih.has(r)?Hh(r,qa(t.value.slice(o+1),e)):!!Ga.has(r)&&Ei(r,"shorter",qa(t.value.slice(o+1),e)))}}return!1}function qa(t,e){const n=[];let r=1,s=!1,a=!1;for(let c=0;c<t.length;c++){let l=t[c];if(!ae(l)&&!oe(l)){if(!L(l)||!be(l.value)){if(!s){const h=e(l);if(h){s=h;continue}}if(!a){if(se(l)&&ls.has(lt(l.getName()))){if([[l]]=br([[l]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0}),!l||!L(l)||!pt(l.value))return!1;Number.isNaN(l.value[4].value)&&(l.value[4].value=0)}if(L(l)&&tt(l.value)&&l.value[4].value>=0){a=l.value[4].value;continue}}return!1}if(!s)return!1;n.push({color:s,percentage:a}),s=!1,a=!1}}if(s&&n.push({color:s,percentage:a}),n.length!==2)return!1;let o=n[0].percentage,i=n[1].percentage;return(o===!1||!(o<0||o>100))&&(i===!1||!(i<0||i>100))&&(o===!1&&i===!1?(o=50,i=50):o!==!1&&i===!1?i=100-o:o===!1&&i!==!1&&(o=100-i),(o!==0||i!==0)&&o!==!1&&i!==!1&&(o+i>100&&(o=o/(o+i)*100,i=i/(o+i)*100),o+i<100&&(r=(o+i)/100,o=o/(o+i)*100,i=i/(o+i)*100),{a:{color:n[0].color,percentage:o},b:{color:n[1].color,percentage:i},alphaMultiplier:r}))}function Hh(t,e){if(!e)return!1;const n=e.a.color,r=e.b.color,s=e.a.percentage/100;let a=n.channels,o=r.channels,i=y.RGB,c=n.alpha;if(typeof c!="number")return!1;let l=r.alpha;if(typeof l!="number")return!1;switch(c=Number.isNaN(c)?l:c,l=Number.isNaN(l)?c:l,t){case"srgb":i=y.RGB;break;case"srgb-linear":i=y.Linear_sRGB;break;case"display-p3":i=y.Display_P3;break;case"a98-rgb":i=y.A98_RGB;break;case"prophoto-rgb":i=y.ProPhoto_RGB;break;case"rec2020":i=y.Rec2020;break;case"lab":i=y.Lab;break;case"oklab":i=y.OKLab;break;case"xyz-d50":i=y.XYZ_D50;break;case"xyz":case"xyz-d65":i=y.XYZ_D65}a=tr(n,i).channels,o=tr(r,i).channels,a[0]=ge(a[0],o[0]),o[0]=ge(o[0],a[0]),a[1]=ge(a[1],o[1]),o[1]=ge(o[1],a[1]),a[2]=ge(a[2],o[2]),o[2]=ge(o[2],a[2]),a[0]=Ue(a[0],c),a[1]=Ue(a[1],c),a[2]=Ue(a[2],c),o[0]=Ue(o[0],l),o[1]=Ue(o[1],l),o[2]=Ue(o[2],l);const h=Ne(c,l,s),f={colorNotation:i,channels:[kn(Ne(a[0],o[0],s),h),kn(Ne(a[1],o[1],s),h),kn(Ne(a[2],o[2],s),h)],alpha:h*e.alphaMultiplier,syntaxFlags:new Set([B.ColorMix])};return(e.a.color.syntaxFlags.has(B.Experimental)||e.b.color.syntaxFlags.has(B.Experimental))&&f.syntaxFlags.add(B.Experimental),f}function Ei(t,e,n){if(!n)return!1;const r=n.a.color,s=n.b.color,a=n.a.percentage/100;let o=r.channels,i=s.channels,c=0,l=0,h=0,f=0,d=0,p=0,m=y.RGB,$=r.alpha;if(typeof $!="number")return!1;let b=s.alpha;if(typeof b!="number")return!1;switch($=Number.isNaN($)?b:$,b=Number.isNaN(b)?$:b,t){case"hsl":m=y.HSL;break;case"hwb":m=y.HWB;break;case"lch":m=y.LCH;break;case"oklch":m=y.OKLCH}switch(o=tr(r,m).channels,i=tr(s,m).channels,t){case"hsl":case"hwb":c=o[0],l=i[0],h=o[1],f=i[1],d=o[2],p=i[2];break;case"lch":case"oklch":h=o[0],f=i[0],d=o[1],p=i[1],c=o[2],l=i[2]}c=ge(c,l),Number.isNaN(c)&&(c=0),l=ge(l,c),Number.isNaN(l)&&(l=0),h=ge(h,f),f=ge(f,h),d=ge(d,p),p=ge(p,d);const w=l-c;switch(e){case"shorter":w>180?c+=360:w<-180&&(l+=360);break;case"longer":-180<w&&w<180&&(w>0?c+=360:l+=360);break;case"increasing":w<0&&(l+=360);break;case"decreasing":w>0&&(c+=360);break;default:throw new Error("Unknown hue interpolation method")}h=Ue(h,$),d=Ue(d,$),f=Ue(f,b),p=Ue(p,b);let N=[0,0,0];const E=Ne($,b,a);switch(t){case"hsl":case"hwb":N=[Ne(c,l,a),kn(Ne(h,f,a),E),kn(Ne(d,p,a),E)];break;case"lch":case"oklch":N=[kn(Ne(h,f,a),E),kn(Ne(d,p,a),E),Ne(c,l,a)]}const D={colorNotation:m,channels:N,alpha:E*n.alphaMultiplier,syntaxFlags:new Set([B.ColorMix])};return(n.a.color.syntaxFlags.has(B.Experimental)||n.b.color.syntaxFlags.has(B.Experimental))&&D.syntaxFlags.add(B.Experimental),D}function ge(t,e){return Number.isNaN(t)?e:t}function Ne(t,e,n){return t*n+e*(1-n)}function Ue(t,e){return Number.isNaN(e)?t:Number.isNaN(t)?Number.NaN:t*e}function kn(t,e){return e===0||Number.isNaN(e)?t:Number.isNaN(t)?Number.NaN:t/e}function Uh(t){const e=lt(t[4].value);if(e.match(/[^a-f0-9]/))return!1;const n={colorNotation:y.HEX,channels:[0,0,0],alpha:1,syntaxFlags:new Set([B.Hex])},r=e.length;if(r===3){const s=e[0],a=e[1],o=e[2];return n.channels=[parseInt(s+s,16)/255,parseInt(a+a,16)/255,parseInt(o+o,16)/255],n}if(r===6){const s=e[0]+e[1],a=e[2]+e[3],o=e[4]+e[5];return n.channels=[parseInt(s,16)/255,parseInt(a,16)/255,parseInt(o,16)/255],n}if(r===4){const s=e[0],a=e[1],o=e[2],i=e[3];return n.channels=[parseInt(s+s,16)/255,parseInt(a+a,16)/255,parseInt(o+o,16)/255],n.alpha=parseInt(i+i,16)/255,n.syntaxFlags.add(B.HasAlpha),n}if(r===8){const s=e[0]+e[1],a=e[2]+e[3],o=e[4]+e[5],i=e[6]+e[7];return n.channels=[parseInt(s,16)/255,parseInt(a,16)/255,parseInt(o,16)/255],n.alpha=parseInt(i,16)/255,n.syntaxFlags.add(B.HasAlpha),n}return!1}function zr(t){if(H(t))return t[4].value=t[4].value%360,t[1]=t[4].value.toString(),t;if(nt(t)){let e=t[4].value;switch(lt(t[4].unit)){case"deg":break;case"rad":e=180*t[4].value/Math.PI;break;case"grad":e=.9*t[4].value;break;case"turn":e=360*t[4].value;break;default:return!1}return e%=360,[v.Number,e.toString(),t[2],t[3],{value:e,type:x.Number}]}return!1}function zh(t,e,n){if(e===0){const r=zr(t);return r!==!1&&(nt(t)&&n.syntaxFlags.add(B.HasDimensionValues),r)}if(tt(t)){e===3?n.syntaxFlags.add(B.HasPercentageAlpha):n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,1,0,100);return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){if(e!==3)return!1;let r=V(t[4].value,1,0,100);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function jh(t,e,n){if(bt(t)&&lt(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(e===0){const r=zr(t);return r!==!1&&(nt(t)&&n.syntaxFlags.add(B.HasDimensionValues),r)}if(tt(t)){e===3?n.syntaxFlags.add(B.HasPercentageAlpha):n.syntaxFlags.add(B.HasPercentageValues);let r=t[4].value;return e===3?r=V(t[4].value,100,0,1):e===1&&(r=V(t[4].value,1,0,2147483647)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=t[4].value;return e===3?r=V(t[4].value,1,0,1):e===1&&(r=V(t[4].value,1,0,2147483647)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function Ci(t,e,n,r){const s=[],a=[],o=[],i=[],c={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let l=s;for(let m=0;m<t.value.length;m++){let $=t.value[m];if(!ae($)&&!oe($)){if(L($)&&be($.value)){if(l===s){l=a;continue}if(l===a){l=o;continue}if(l===o){l=i;continue}if(l===i)return!1}if(se($)){if(l===i&&$.getName().toLowerCase()==="var"){c.syntaxFlags.add(B.HasVariableAlpha),l.push($);continue}if(!ls.has($.getName().toLowerCase()))return!1;const[[b]]=br([[$]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!b||!L(b)||!pt(b.value))return!1;Number.isNaN(b.value[4].value)&&(b.value[4].value=0),$=b}if(!L($))return!1;l.push($)}}if(l.length!==1||s.length!==1||a.length!==1||o.length!==1||!L(s[0])||!L(a[0])||!L(o[0]))return!1;const h=e(s[0].value,0,c);if(!h||!H(h))return!1;const f=e(a[0].value,1,c);if(!f||!H(f))return!1;const d=e(o[0].value,2,c);if(!d||!H(d))return!1;const p=[h,f,d];if(i.length===1)if(c.syntaxFlags.add(B.HasAlpha),L(i[0])){const m=e(i[0].value,3,c);if(!m||!H(m))return!1;p.push(m)}else c.alpha=i[0];return c.channels=[p[0][4].value,p[1][4].value,p[2][4].value],p.length===4&&(c.alpha=p[3][4].value),c}function Fn(t,e,n,r,s){const a=[],o=[],i=[],c=[];let l,h,f=!1;const d={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let p=a;for(let N=0;N<t.value.length;N++){let E=t.value[N];if(ae(E)||oe(E))for(;ae(t.value[N+1])||oe(t.value[N+1]);)N++;else if(p===a&&a.length&&(p=o),p===o&&o.length&&(p=i),L(E)&&es(E.value)&&E.value[4].value==="/"){if(p===c)return!1;p=c}else{if(se(E)){if(p===c&&E.getName().toLowerCase()==="var"){d.syntaxFlags.add(B.HasVariableAlpha),p.push(E);continue}if(!ls.has(E.getName().toLowerCase()))return!1;const[[D]]=br([[E]],{censorIntoStandardRepresentableValues:!0,globals:h,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!D||!L(D)||!pt(D.value))return!1;Number.isNaN(D.value[4].value)&&(D.value[4].value=0),E=D}if(p===a&&a.length===0&&L(E)&&bt(E.value)&&E.value[4].value.toLowerCase()==="from"){if(f)return!1;for(;ae(t.value[N+1])||oe(t.value[N+1]);)N++;if(N++,E=t.value[N],f=s(E),f===!1)return!1;f.syntaxFlags.has(B.Experimental)&&d.syntaxFlags.add(B.Experimental),d.syntaxFlags.add(B.RelativeColorSyntax),f.colorNotation!==n&&(f=tr(f,n)),l=yi(f),h=Ni(l)}else{if(!L(E))return!1;if(bt(E.value)&&l){const D=E.value[4].value.toLowerCase();if(l.has(D)){p.push(new j(l.get(D)));continue}}p.push(E)}}}if(p.length!==1||a.length!==1||o.length!==1||i.length!==1||!L(a[0])||!L(o[0])||!L(i[0])||l&&!l.has("alpha"))return!1;const m=e(a[0].value,0,d);if(!m||!H(m))return!1;const $=e(o[0].value,1,d);if(!$||!H($))return!1;const b=e(i[0].value,2,d);if(!b||!H(b))return!1;const w=[m,$,b];if(c.length===1)if(d.syntaxFlags.add(B.HasAlpha),L(c[0])){const N=e(c[0].value,3,d);if(!N||!H(N))return!1;w.push(N)}else d.alpha=c[0];else if(l&&l.has("alpha")){const N=e(l.get("alpha"),3,d);if(!N||!H(N))return!1;w.push(N)}return d.channels=[w[0][4].value,w[1][4].value,w[2][4].value],w.length===4&&(d.alpha=w[3][4].value),d}function Gh(t,e){if(t.value.some(n=>L(n)&&be(n.value))){const n=qh(t);if(n!==!1)return n}{const n=Vh(t,e);if(n!==!1)return n}return!1}function qh(t){return Ci(t,zh,y.HSL,[B.LegacyHSL])}function Vh(t,e){return Fn(t,jh,y.HSL,[],e)}function Kh(t,e,n){if(bt(t)&&lt(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(e===0){const r=zr(t);return r!==!1&&(nt(t)&&n.syntaxFlags.add(B.HasDimensionValues),r)}if(tt(t)){e===3?n.syntaxFlags.add(B.HasPercentageAlpha):n.syntaxFlags.add(B.HasPercentageValues);let r=t[4].value;return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=t[4].value;return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function Xh(t,e,n){if(bt(t)&&lt(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(tt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,1,0,100);return e===1||e===2?r=V(t[4].value,.8,-2147483647,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,1,0,100);return e===1||e===2?r=V(t[4].value,1,-2147483647,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function Yh(t,e){return Fn(t,Xh,y.Lab,[],e)}function Zh(t,e,n){if(bt(t)&&lt(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(e===2){const r=zr(t);return r!==!1&&(nt(t)&&n.syntaxFlags.add(B.HasDimensionValues),r)}if(tt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,1,0,100);return e===1?r=V(t[4].value,100/150,0,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,1,0,100);return e===1?r=V(t[4].value,1,0,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function Jh(t,e){return Fn(t,Zh,y.LCH,[],e)}const ki=new Map;for(const[t,e]of Object.entries(Dh))ki.set(t,e);function Qh(t){const e=ki.get(lt(t));return!!e&&{colorNotation:y.RGB,channels:[e[0]/255,e[1]/255,e[2]/255],alpha:1,syntaxFlags:new Set([B.ColorKeyword,B.NamedColor])}}function t0(t,e,n){if(bt(t)&&lt(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(tt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,100,0,1);return e===1||e===2?r=V(t[4].value,250,-2147483647,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,1,0,1);return e===1||e===2?r=V(t[4].value,1,-2147483647,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function e0(t,e){return Fn(t,t0,y.OKLab,[],e)}function n0(t,e,n){if(bt(t)&&lt(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(e===2){const r=zr(t);return r!==!1&&(nt(t)&&n.syntaxFlags.add(B.HasDimensionValues),r)}if(tt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,100,0,1);return e===1?r=V(t[4].value,250,0,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,1,0,1);return e===1?r=V(t[4].value,1,0,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function r0(t,e){return Fn(t,n0,y.OKLCH,[],e)}function s0(t,e,n){if(tt(t)){e===3?n.syntaxFlags.add(B.HasPercentageAlpha):n.syntaxFlags.add(B.HasPercentageValues);const r=V(t[4].value,100,0,1);return[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,255,0,1);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function a0(t,e,n){if(bt(t)&&t[4].value.toLowerCase()==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(tt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,100,-2147483647,2147483647);return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,255,-2147483647,2147483647);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function o0(t,e){if(t.value.some(n=>L(n)&&be(n.value))){const n=i0(t);if(n!==!1)return(!n.syntaxFlags.has(B.HasNumberValues)||!n.syntaxFlags.has(B.HasPercentageValues))&&n}else{const n=l0(t,e);if(n!==!1)return n}return!1}function i0(t){return Ci(t,s0,y.RGB,[B.LegacyRGB])}function l0(t,e){return Fn(t,a0,y.RGB,[],e)}function c0(t){const e=ja(t);if(Ah(e))return gi(e);let n=t;return n=mi(n),n[0]<1e-6&&(n=[0,0,0]),n[0]>.999999&&(n=[1,0,0]),Lr(Ph(n,u0,h0))}function u0(t){return t=ui(t),t=Ba(t),Ir(t)}function h0(t){return t=_r(t),t=Oa(t),hi(t)}function f0(t,e){let n=!1;for(let o=0;o<t.value.length;o++){const i=t.value[o];if(!ae(i)&&!oe(i)&&(n||(n=e(i),!n)))return!1}if(!n)return!1;n.channels=Pt(n.channels),n.channels=c0(wi(n).channels),n.colorNotation=y.sRGB;const r={colorNotation:y.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([B.ContrastColor,B.Experimental])},s=bi(n.channels,[1,1,1]),a=bi(n.channels,[0,0,0]);return r.channels=s>a?[1,1,1]:[0,0,0],r}function Ee(t){if(se(t))switch(lt(t.getName())){case"rgb":case"rgba":return o0(t,Ee);case"hsl":case"hsla":return Gh(t,Ee);case"hwb":return e=Ee,Fn(t,Kh,y.HWB,[],e);case"lab":return Yh(t,Ee);case"lch":return Jh(t,Ee);case"oklab":return e0(t,Ee);case"oklch":return r0(t,Ee);case"color":return Wh(t,Ee);case"color-mix":return _h(t,Ee);case"contrast-color":return f0(t,Ee)}var e;if(L(t)){if(Pl(t.value))return Uh(t.value);if(bt(t.value)){const n=Qh(t.value[4].value);return n!==!1?n:lt(t.value[4].value)==="transparent"&&{colorNotation:y.RGB,channels:[0,0,0],alpha:0,syntaxFlags:new Set([B.ColorKeyword])}}}return!1}const{CloseParen:Fi,Comment:xi,Dimension:p0,EOF:Si,Function:Ai,Ident:d0,Number:m0,OpenParen:Pi,Percentage:g0,Whitespace:Mi}=v,{HasNoneKeywords:Va}=B,Di="relative-color",v0=8,er=10,Ka=16,b0=100,Xa=255,Bi=new RegExp(`^${xr}(${Gn}|${ws})\\s+`),w0=/(?:hsla?|hwb)$/,$0=new RegExp(`^(?:${Oo}|${ru})$`),y0=new RegExp(So),N0=new RegExp(xr),Ri=new RegExp(`^${au}`),Oi=new RegExp(`^${xr}`),E0=new RegExp(kr);function Wi(t,e={}){if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{colorSpace:n="",format:r=""}=e,s=new Map([["color",["r","g","b","alpha"]],["hsl",["h","s","l","alpha"]],["hsla",["h","s","l","alpha"]],["hwb",["h","w","b","alpha"]],["lab",["l","a","b","alpha"]],["lch",["l","c","h","alpha"]],["oklab",["l","a","b","alpha"]],["oklch",["l","c","h","alpha"]],["rgb",["r","g","b","alpha"]],["rgba",["r","g","b","alpha"]]]).get(n);if(!s)return new P;const a=new Set,o=[[],[],[],[]];let i=0,c=0,l=!1;for(;t.length;){const f=t.shift();if(!Array.isArray(f))throw new TypeError(`${f} is not an array.`);const[d,p,,,m]=f,$=o[i];if(Array.isArray($))switch(d){case p0:{const b=Xi(f,e);S(b)?$.push(b):$.push(p);break}case Ai:{$.push(p),l=!0,c++,y0.test(p)&&a.add(c);break}case d0:{if(!s.includes(p))return new P;$.push(p),l||i++;break}case m0:{$.push(Number(m?.value)),l||i++;break}case Pi:{$.push(p),c++;break}case Fi:{l&&($[$.length-1]===" "?$.splice(-1,1,p):$.push(p),a.has(c)&&a.delete(c),c--,c===0&&(l=!1,i++));break}case g0:{$.push(Number(m?.value)/b0),l||i++;break}case Mi:{if($.length&&l){const b=$[$.length-1];(typeof b=="number"||S(b)&&!b.endsWith("(")&&b!==" ")&&$.push(p)}break}default:d!==xi&&d!==Si&&l&&$.push(p)}}const h=[];for(const f of o)if(f.length===1){const[d]=f;Un(d)&&h.push(d)}else if(f.length){const d=Ki(f.join(""),{format:r});h.push(d)}return h}function C0(t,e={}){const{currentColor:n="",format:r=""}=e;if(S(t)){if(t=t.toLowerCase().trim(),!t)return new P;if(!Oi.test(t))return t}else return new P;const s=gt({namespace:Di,name:"extractOriginColor",value:t},e),a=mt(s);if(a instanceof st)return a.isNull?a:a.item;if(/currentcolor/.test(t))if(n)t=t.replace(/currentcolor/g,n);else return F(s,null),new P;let o="";if(Ri.test(t)&&([,o]=t.match(Ri)),e.colorSpace=o,Bi.test(t)){const[,i]=t.match(Bi),[,c]=t.split(i);if(/^[a-z]+$/.test(i)){if(!/^transparent$/.test(i)&&!Object.prototype.hasOwnProperty.call(Zn,i))return F(s,null),new P}else if(r===rt){const l=nr(i,e);S(l)&&(t=t.replace(i,l))}if(r===rt){const l=Ke({css:c}),h=Wi(l,e);if(h instanceof P)return F(s,null),h;const[f,d,p,m]=h;let $="";Un(m)?$=` ${f} ${d} ${p} / ${m})`:$=` ${h.join(" ")})`,c!==$&&(t=t.replace(c,$))}}else{const[,i]=t.split(Oi),c=Ke({css:i}),l=[];let h=0;for(;c.length;){const[N,E]=c.shift();switch(N){case Ai:case Pi:{l.push(E),h++;break}case Fi:{const D=l[l.length-1];D===" "?l.splice(-1,1,E):S(D)&&l.push(E),h--;break}case Mi:{const D=l[l.length-1];S(D)&&!D.endsWith("(")&&D!==" "&&l.push(E);break}default:N!==xi&&N!==Si&&l.push(E)}if(h===0)break}const f=Ya(l.join("").trim(),e);if(f instanceof P)return F(s,null),f;const d=Wi(c,e);if(d instanceof P)return F(s,null),d;const[p,m,$,b]=d;let w="";Un(b)?w=` ${p} ${m} ${$} / ${b})`:w=` ${d.join(" ")})`,t=t.replace(i,`${f}${w}`)}return F(s,t),t}function Ya(t,e={}){const{format:n=""}=e;if(S(t)){if(E0.test(t)){if(n===rt)return t;throw new SyntaxError(`Unexpected token ${ia} found.`)}else if(!N0.test(t))return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string.`);const r=gt({namespace:Di,name:"resolveRelativeColor",value:t},e),s=mt(r);if(s instanceof st)return s.isNull?s:s.item;const a=C0(t,e);if(a instanceof P)return F(r,null),a;if(t=a,n===rt)return t.startsWith("rgba(")?t=t.replace(/^rgba\(/,"rgb("):t.startsWith("hsla(")&&(t=t.replace(/^hsla\(/,"hsl(")),t;const o=Ke({css:t}),i=Tl(o),c=Ee(i);if(!c)return F(r,null),new P;const{alpha:l,channels:h,colorNotation:f,syntaxFlags:d}=c;let p;Number.isNaN(Number(l))?d instanceof Set&&d.has(Va)?p=g:p=0:p=M(Number(l),v0);let m,$,b;[m,$,b]=h;let w;if($0.test(f)){const N=d instanceof Set&&d.has(Va);Number.isNaN(m)?N?m=g:m=0:m=M(m,Ka),Number.isNaN($)?N?$=g:$=0:$=M($,Ka),Number.isNaN(b)?N?b=g:b=0:b=M(b,Ka),p===1?w=`${f}(${m} ${$} ${b})`:w=`${f}(${m} ${$} ${b} / ${p})`}else if(w0.test(f)){Number.isNaN(m)&&(m=0),Number.isNaN($)&&($=0),Number.isNaN(b)&&(b=0);let[N,E,D]=Wr(`${f}(${m} ${$} ${b} / ${p})`);N=M(N/Xa,er),E=M(E/Xa,er),D=M(D/Xa,er),p===1?w=`color(srgb ${N} ${E} ${D})`:w=`color(srgb ${N} ${E} ${D} / ${p})`}else{const N=f==="rgb"?"srgb":f,E=d instanceof Set&&d.has(Va);Number.isNaN(m)?E?m=g:m=0:m=M(m,er),Number.isNaN($)?E?$=g:$=0:$=M($,er),Number.isNaN(b)?E?b=g:b=0:b=M(b,er),p===1?w=`color(${N} ${m} ${$} ${b})`:w=`color(${N} ${m} ${$} ${b} / ${p})`}return F(r,w),w}const k0="resolve",jr="rgba(0, 0, 0, 0)",F0=new RegExp(gs),x0=new RegExp(Io),S0=new RegExp(kr),nr=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{currentColor:n="",format:r=ot,nullable:s=!1}=e,a=gt({namespace:k0,name:"resolve",value:t},e),o=mt(a);if(o instanceof st)return o.isNull?o:o.item;if(S0.test(t)){if(r===rt)return F(a,t),t;const p=Tr(t,e);if(p instanceof P)switch(r){case"hex":case"hexAlpha":return F(a,p),p;default:{if(s)return F(a,p),p;const m=jr;return F(a,m),m}}else t=p}if(e.format!==r&&(e.format=r),t=t.toLowerCase(),x0.test(t)){const p=Ya(t,e);if(r===ot){let m;return p instanceof P?s?m=p:m=jr:m=p,F(a,m),m}if(r===rt){let m="";return p instanceof P?m="":m=p,F(a,m),m}p instanceof P?t="":t=p}F0.test(t)&&(t=Gr(t,e));let i="",c=NaN,l=NaN,h=NaN,f=NaN;if(t==="transparent")switch(r){case rt:return F(a,t),t;case"hex":return F(a,null),new P;case"hexAlpha":{const p="#00000000";return F(a,p),p}case ot:default:{const p=jr;return F(a,p),p}}else if(t==="currentcolor"){if(r===rt)return F(a,t),t;if(n){let p;if(n.startsWith(jn)?p=As(n,e):n.startsWith(ut)?p=Ze(n,e):p=Cn(n,e),p instanceof P)return F(a,p),p;[i,c,l,h,f]=p}else if(r===ot){const p=jr;return F(a,p),p}}else if(r===rt)if(t.startsWith(jn)){const p=As(t,e);return F(a,p),p}else if(t.startsWith(ut)){const[p,m,$,b,w]=Ze(t,e);let N="";return w===1?N=`color(${p} ${m} ${$} ${b})`:N=`color(${p} ${m} ${$} ${b} / ${w})`,F(a,N),N}else{const p=Cn(t,e);if(S(p))return F(a,p),p;const[m,$,b,w,N]=p;let E="";return m==="rgb"?N===1?E=`${m}(${$}, ${b}, ${w})`:E=`${m}a(${$}, ${b}, ${w}, ${N})`:N===1?E=`${m}(${$} ${b} ${w})`:E=`${m}(${$} ${b} ${w} / ${N})`,F(a,E),E}else if(t.startsWith(jn)){/currentcolor/.test(t)&&n&&(t=t.replace(/currentcolor/g,n)),/transparent/.test(t)&&(t=t.replace(/transparent/g,jr));const p=As(t,e);if(p instanceof P)return F(a,p),p;[i,c,l,h,f]=p}else if(t.startsWith(ut)){const p=Ze(t,e);if(p instanceof P)return F(a,p),p;[i,c,l,h,f]=p}else if(t){const p=Cn(t,e);if(p instanceof P)return F(a,p),p;[i,c,l,h,f]=p}let d="";switch(r){case"hex":{if(Number.isNaN(c)||Number.isNaN(l)||Number.isNaN(h)||Number.isNaN(f)||f===0)return F(a,null),new P;d=ei([c,l,h,1]);break}case"hexAlpha":{if(Number.isNaN(c)||Number.isNaN(l)||Number.isNaN(h)||Number.isNaN(f))return F(a,null),new P;d=ei([c,l,h,f]);break}case ot:default:switch(i){case"rgb":{f===1?d=`${i}(${c}, ${l}, ${h})`:d=`${i}a(${c}, ${l}, ${h}, ${f})`;break}case"lab":case"lch":case"oklab":case"oklch":{f===1?d=`${i}(${c} ${l} ${h})`:d=`${i}(${c} ${l} ${h} / ${f})`;break}default:f===1?d=`color(${i} ${c} ${l} ${h})`:d=`color(${i} ${c} ${l} ${h} / ${f})`}}return F(a,d),d},A0=(t,e={})=>{e.nullable=!1;const n=nr(t,e);return n instanceof P?null:n},{CloseParen:P0,Comma:M0,Comment:D0,Delim:B0,EOF:R0,Function:O0,Ident:W0,OpenParen:T0,Whitespace:I0}=v,Ti="util",L0=10,Ms=16,rr=360,Ds=180,_0=new RegExp(`^(?:${Gn})$`),H0=/^(?:(?:ok)?l(?:ab|ch)|color(?:-mix)?|hsla?|hwb|rgba?|var)\(/,U0=new RegExp(ws),Ii=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{delimiter:n=" ",preserveComment:r=!1}=e,s=gt({namespace:Ti,name:"splitValue",value:t},{delimiter:n,preserveComment:r}),a=mt(s);if(a instanceof st)return a.item;let o;n===","?o=/^,$/:n==="/"?o=/^\/$/:o=/^\s+$/;const i=Ke({css:t});let c=0,l="";const h=[];for(;i.length;){const[f,d]=i.shift();switch(f){case M0:{o.test(d)&&c===0?(h.push(l.trim()),l=""):l+=d;break}case B0:{o.test(d)&&c===0?(h.push(l.trim()),l=""):l+=d;break}case D0:{r&&(n===","||n==="/")&&(l+=d);break}case O0:case T0:{l+=d,c++;break}case P0:{l+=d,c--;break}case I0:{o.test(d)?c===0?l&&(h.push(l.trim()),l=""):l+=" ":l.endsWith(" ")||(l+=" ");break}default:f===R0?(h.push(l.trim()),l=""):l+=d}}return F(s,h),h},z0=t=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const e=gt({namespace:Ti,name:"extractDashedIdent",value:t}),n=mt(e);if(n instanceof st)return n.item;const r=Ke({css:t}),s=new Set;for(;r.length;){const[o,i]=r.shift();o===W0&&i.startsWith("--")&&s.add(i)}const a=[...s];return F(e,a),a},fn=(t,e={})=>{if(S(t)&&(t=t.toLowerCase().trim(),t&&S(t))){if(/^[a-z]+$/.test(t)){if(/^(?:currentcolor|transparent)$/.test(t)||Object.prototype.hasOwnProperty.call(Zn,t))return!0}else if(_0.test(t)||U0.test(t)||H0.test(t)&&(e.nullable=!0,e.format||(e.format=rt),nr(t,e)))return!0}return!1},Li=(t,e=!1)=>typeof t>"u"?"":JSON.stringify(t,(n,r)=>{let s;return typeof r>"u"?s=null:typeof r=="function"?e?s=r.toString().replace(/\s/g,"").substring(0,Ms):s=r.name:r instanceof Map||r instanceof Set?s=[...r]:typeof r=="bigint"?s=r.toString():s=r,s}),M=(t,e=0)=>{if(!Number.isFinite(t))throw new TypeError(`${t} is not a finite number.`);if(Number.isFinite(e)){if(e<0||e>Ms)throw new RangeError(`${e} is not between 0 and ${Ms}.`)}else throw new TypeError(`${e} is not a finite number.`);if(e===0)return Math.round(t);let n;return e===Ms?n=t.toPrecision(6):e<L0?n=t.toPrecision(4):n=t.toPrecision(5),parseFloat(n)},_i=(t,e,n="shorter")=>{if(!Number.isFinite(t))throw new TypeError(`${t} is not a finite number.`);if(!Number.isFinite(e))throw new TypeError(`${e} is not a finite number.`);switch(n){case"decreasing":{e>t&&(t+=rr);break}case"increasing":{e<t&&(e+=rr);break}case"longer":{e>t&&e<t+Ds?t+=rr:e>t+Ds*-1&&e<=t&&(e+=rr);break}case"shorter":default:e>t+Ds?t+=rr:e<t+Ds*-1&&(e+=rr)}return[t,e]},j0=4096;var Bs,Rs;class st{constructor(e,n=!1){z(this,Bs),z(this,Rs),C(this,Rs,e),C(this,Bs,!!n)}get item(){return u(this,Rs)}get isNull(){return u(this,Bs)}}Bs=new WeakMap,Rs=new WeakMap;class P extends st{constructor(){super(Symbol("null"),!0)}}const sr=new qc({max:j0}),F=(t,e)=>{t&&(e===null?sr.set(t,new P):e instanceof st?sr.set(t,e):sr.set(t,new st(e)))},mt=t=>{if(t&&sr.has(t)){const e=sr.get(t);return e instanceof st?e:(sr.delete(t),!1)}return!1},gt=(t,e={})=>{const{customProperty:n={},dimension:r={}}=e;let s="";return t&&Object.keys(t).length&&typeof n.callback!="function"&&typeof r.callback!="function"&&(t.opt=Li(e),s=Li(t)),s},{CloseParen:G0,Comment:Hi,Dimension:q0,EOF:V0,Function:K0,OpenParen:X0,Whitespace:Ui}=v,zi="css-calc",Y0=3,Os=16,ji=100,Z0=new RegExp(gs),Gi=new RegExp(`^calc\\((${Lt})\\)$`),J0=new RegExp(So),Q0=new RegExp(kr),Ws=new RegExp(Qc),qi=/\s[*+/-]\s/,Ts=new RegExp(`^(${Lt})(${zn}|${Cr})$`),ar=new RegExp(`^(${Lt})(${zn}|${Cr}|%)$`),pn=new RegExp(`^(${Lt})%$`);var dn,or,ir,Je,lr,cr,ze,xn,Sn,Qe,tn,mn,gn,en,An,Pn;class t1{constructor(){z(this,dn),z(this,or),z(this,ir),z(this,Je),z(this,lr),z(this,cr),z(this,ze),z(this,xn),z(this,Sn),z(this,Qe),z(this,tn),z(this,mn),z(this,gn),z(this,en),z(this,An),z(this,Pn),C(this,dn,!1),C(this,or,[]),C(this,ir,[]),C(this,Je,!1),C(this,lr,[]),C(this,cr,[]),C(this,ze,!1),C(this,xn,[]),C(this,Sn,[]),C(this,Qe,[]),C(this,tn,[]),C(this,mn,!1),C(this,gn,[]),C(this,en,[]),C(this,An,[]),C(this,Pn,[])}get hasNum(){return u(this,dn)}set hasNum(e){C(this,dn,!!e)}get numSum(){return u(this,or)}get numMul(){return u(this,ir)}get hasPct(){return u(this,Je)}set hasPct(e){C(this,Je,!!e)}get pctSum(){return u(this,lr)}get pctMul(){return u(this,cr)}get hasDim(){return u(this,ze)}set hasDim(e){C(this,ze,!!e)}get dimSum(){return u(this,xn)}get dimSub(){return u(this,Sn)}get dimMul(){return u(this,Qe)}get dimDiv(){return u(this,tn)}get hasEtc(){return u(this,mn)}set hasEtc(e){C(this,mn,!!e)}get etcSum(){return u(this,gn)}get etcSub(){return u(this,en)}get etcMul(){return u(this,An)}get etcDiv(){return u(this,Pn)}clear(){C(this,dn,!1),C(this,or,[]),C(this,ir,[]),C(this,Je,!1),C(this,lr,[]),C(this,cr,[]),C(this,ze,!1),C(this,xn,[]),C(this,Sn,[]),C(this,Qe,[]),C(this,tn,[]),C(this,mn,!1),C(this,gn,[]),C(this,en,[]),C(this,An,[]),C(this,Pn,[])}sort(e=[]){const n=[...e];return n.length>1&&n.sort((r,s)=>{let a;if(ar.test(r)&&ar.test(s)){const[,o,i]=r.match(ar),[,c,l]=s.match(ar);i===l?Number(o)===Number(c)?a=0:Number(o)>Number(c)?a=1:a=-1:i>l?a=1:a=-1}else r===s?a=0:r>s?a=1:a=-1;return a}),n}multiply(){const e=[];let n;if(u(this,dn)){n=1;for(const r of u(this,ir))if(n*=r,n===0||!Number.isFinite(n)||Number.isNaN(n))break;!u(this,Je)&&!u(this,ze)&&!this.hasEtc&&(Number.isFinite(n)&&(n=M(n,Os)),e.push(n))}if(u(this,Je)){typeof n!="number"&&(n=1);for(const r of u(this,cr))if(n*=r,n===0||!Number.isFinite(n)||Number.isNaN(n))break;Number.isFinite(n)&&(n=`${M(n,Os)}%`),!u(this,ze)&&!this.hasEtc&&e.push(n)}if(u(this,ze)){let r="",s="",a="";u(this,Qe).length&&(u(this,Qe).length===1?[s]=u(this,Qe):s=`${this.sort(u(this,Qe)).join(" * ")}`),u(this,tn).length&&(u(this,tn).length===1?[a]=u(this,tn):a=`${this.sort(u(this,tn)).join(" * ")}`),Number.isFinite(n)?(s?a?a.includes("*")?r=Tt(`calc(${n} * ${s} / (${a}))`,{toCanonicalUnits:!0}):r=Tt(`calc(${n} * ${s} / ${a})`,{toCanonicalUnits:!0}):r=Tt(`calc(${n} * ${s})`,{toCanonicalUnits:!0}):a.includes("*")?r=Tt(`calc(${n} / (${a}))`,{toCanonicalUnits:!0}):r=Tt(`calc(${n} / ${a})`,{toCanonicalUnits:!0}),e.push(r.replace(/^calc/,""))):(!e.length&&n!==void 0&&e.push(n),s?(a?a.includes("*")?r=Tt(`calc(${s} / (${a}))`,{toCanonicalUnits:!0}):r=Tt(`calc(${s} / ${a})`,{toCanonicalUnits:!0}):r=Tt(`calc(${s})`,{toCanonicalUnits:!0}),e.length?e.push("*",r.replace(/^calc/,"")):e.push(r.replace(/^calc/,""))):(r=Tt(`calc(${a})`,{toCanonicalUnits:!0}),e.length?e.push("/",r.replace(/^calc/,"")):e.push("1","/",r.replace(/^calc/,""))))}if(u(this,mn)){if(u(this,An).length){!e.length&&n!==void 0&&e.push(n);const r=this.sort(u(this,An)).join(" * ");e.length?e.push(`* ${r}`):e.push(`${r}`)}if(u(this,Pn).length){const r=this.sort(u(this,Pn)).join(" * ");r.includes("*")?e.length?e.push(`/ (${r})`):e.push(`1 / (${r})`):e.length?e.push(`/ ${r}`):e.push(`1 / ${r}`)}}return e.length?e.join(" "):""}sum(){const e=[];if(u(this,dn)){let n=0;for(const r of u(this,or))if(n+=r,!Number.isFinite(n)||Number.isNaN(n))break;e.push(n)}if(u(this,Je)){let n=0;for(const r of u(this,lr))if(n+=r,!Number.isFinite(n))break;Number.isFinite(n)&&(n=`${n}%`),e.length?e.push(`+ ${n}`):e.push(n)}if(u(this,ze)){let n,r,s;u(this,xn).length&&(r=this.sort(u(this,xn)).join(" + ")),u(this,Sn).length&&(s=this.sort(u(this,Sn)).join(" + ")),r?s?s.includes("-")?n=Tt(`calc(${r} - (${s}))`,{toCanonicalUnits:!0}):n=Tt(`calc(${r} - ${s})`,{toCanonicalUnits:!0}):n=Tt(`calc(${r})`,{toCanonicalUnits:!0}):n=Tt(`calc(-1 * (${s}))`,{toCanonicalUnits:!0}),e.length?e.push("+",n.replace(/^calc/,"")):e.push(n.replace(/^calc/,""))}if(u(this,mn)){if(u(this,gn).length){const n=this.sort(u(this,gn)).map(r=>{let s;return qi.test(r)&&!r.startsWith("(")&&!r.endsWith(")")?s=`(${r})`:s=r,s}).join(" + ");e.length?u(this,gn).length>1?e.push(`+ (${n})`):e.push(`+ ${n}`):e.push(`${n}`)}if(u(this,en).length){const n=this.sort(u(this,en)).map(r=>{let s;return qi.test(r)&&!r.startsWith("(")&&!r.endsWith(")")?s=`(${r})`:s=r,s}).join(" + ");e.length?u(this,en).length>1?e.push(`- (${n})`):e.push(`- ${n}`):u(this,en).length>1?e.push(`-1 * (${n})`):e.push(`-1 * ${n}`)}}return e.length?e.join(" "):""}}dn=new WeakMap,or=new WeakMap,ir=new WeakMap,Je=new WeakMap,lr=new WeakMap,cr=new WeakMap,ze=new WeakMap,xn=new WeakMap,Sn=new WeakMap,Qe=new WeakMap,tn=new WeakMap,mn=new WeakMap,gn=new WeakMap,en=new WeakMap,An=new WeakMap,Pn=new WeakMap;const Vi=(t=[],e=!1)=>{if(t.length<Y0)throw new Error(`Unexpected array length ${t.length}.`);const n=t.shift();if(!S(n)||!n.endsWith("("))throw new Error(`Unexpected token ${n}.`);const r=t.pop();if(r!==")")throw new Error(`Unexpected token ${r}.`);if(t.length===1){const[l]=t;if(!Un(l))throw new Error(`Unexpected token ${l}.`);return`${n}${l}${r}`}const s=[],a=new t1;let o="";const i=t.length;for(let l=0;l<i;l++){const h=t[l];if(!Un(h))throw new Error(`Unexpected token ${h}.`);if(h==="*"||h==="/")o=h;else if(h==="+"||h==="-"){const f=a.multiply();f&&s.push(f,h),a.clear(),o=""}else{const f=Number(h),d=`${h}`;switch(o){case"/":{if(Number.isFinite(f))a.hasNum=!0,a.numMul.push(1/f);else if(pn.test(d)){const[,p]=d.match(pn);a.hasPct=!0,a.pctMul.push(ji*ji/Number(p))}else Ts.test(d)?(a.hasDim=!0,a.dimDiv.push(d)):(a.hasEtc=!0,a.etcDiv.push(d));break}case"*":default:if(Number.isFinite(f))a.hasNum=!0,a.numMul.push(f);else if(pn.test(d)){const[,p]=d.match(pn);a.hasPct=!0,a.pctMul.push(Number(p))}else Ts.test(d)?(a.hasDim=!0,a.dimMul.push(d)):(a.hasEtc=!0,a.etcMul.push(d))}}if(l===i-1){const f=a.multiply();f&&s.push(f),a.clear(),o=""}}let c="";if(e&&(s.includes("+")||s.includes("-"))){const l=[];a.clear(),o="";const h=s.length;for(let f=0;f<h;f++){const d=s[f];if(Un(d))if(d==="+"||d==="-")o=d;else{const p=Number(d),m=`${d}`;switch(o){case"-":{if(Number.isFinite(p))a.hasNum=!0,a.numSum.push(-1*p);else if(pn.test(m)){const[,$]=m.match(pn);a.hasPct=!0,a.pctSum.push(-1*Number($))}else Ts.test(m)?(a.hasDim=!0,a.dimSub.push(m)):(a.hasEtc=!0,a.etcSub.push(m));break}case"+":default:if(Number.isFinite(p))a.hasNum=!0,a.numSum.push(p);else if(pn.test(m)){const[,$]=m.match(pn);a.hasPct=!0,a.pctSum.push(Number($))}else Ts.test(m)?(a.hasDim=!0,a.dimSum.push(m)):(a.hasEtc=!0,a.etcSum.push(m))}}if(f===h-1){const p=a.sum();p&&l.push(p),a.clear(),o=""}}c=l.join(" ").replace(/\+\s-/g,"- ")}else c=s.join(" ").replace(/\+\s-/g,"- ");return c.startsWith("(")&&c.endsWith(")")&&c.lastIndexOf("(")===0&&c.indexOf(")")===c.length-1&&(c=c.replace(/^\(/,"").replace(/\)$/,"")),`${n}${c}${r}`},Ki=(t,e={})=>{const{format:n=""}=e;if(S(t)){if(!Ws.test(t)||n!==rt)return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string.`);const r=gt({namespace:zi,name:"serializeCalc",value:t},e),s=mt(r);if(s instanceof st)return s.item;const a=Ke({css:t}).map(c=>{const[l,h]=c;let f="";return l!==Ui&&l!==Hi&&(f=h),f}).filter(c=>c);let o=a.findLastIndex(c=>/\($/.test(c));for(;o;){const c=a.findIndex((f,d)=>f===")"&&d>o),l=a.slice(o,c+1);let h=Vi(l);Ws.test(h)&&(h=Tt(h,{toCanonicalUnits:!0})),a.splice(o,c-o+1,h),o=a.findLastIndex(f=>/\($/.test(f))}const i=Vi(a,!0);return F(r,i),i},Xi=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const[,,,,n={}]=t,{unit:r,value:s}=n,{dimension:a={}}=e;if(r==="px")return`${s}${r}`;const o=Number(s);if(r&&Number.isFinite(o)){let i;if(Object.hasOwnProperty.call(a,r)?i=a[r]:typeof a.callback=="function"&&(i=a.callback(r)),i=Number(i),Number.isFinite(i))return`${o*i}px`}return new P},e1=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{format:n=""}=e,r=new Set;let s=0;const a=[];for(;t.length;){const o=t.shift();if(!Array.isArray(o))throw new TypeError(`${o} is not an array.`);const[i="",c=""]=o;switch(i){case q0:{if(n===rt&&!r.has(s))a.push(c);else{const l=Xi(o,e);S(l)?a.push(l):a.push(c)}break}case K0:case X0:{a.push(c),s++,J0.test(c)&&r.add(s);break}case G0:{a.length&&a[a.length-1]===" "?a.splice(-1,1,c):a.push(c),r.has(s)&&r.delete(s),s--;break}case Ui:{if(a.length){const l=a[a.length-1];S(l)&&!l.endsWith("(")&&l!==" "&&a.push(c)}break}default:i!==Hi&&i!==V0&&a.push(c)}}return a},Gr=(t,e={})=>{const{format:n=""}=e;if(S(t)){if(Q0.test(t)){if(n===rt)return t;{const c=Tr(t,e);return S(c)?c:""}}else if(!Z0.test(t))return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string.`);const r=gt({namespace:zi,name:"cssCalc",value:t},e),s=mt(r);if(s instanceof st)return s.item;const a=Ke({css:t}),o=e1(a,e);let i=Tt(o.join(""),{toCanonicalUnits:!0});if(Ws.test(t)){if(ar.test(i)){const[,c,l]=i.match(ar);i=`${M(Number(c),Os)}${l}`}i&&!Ws.test(i)&&n===rt&&(i=`calc(${i})`)}if(n===rt){if(/\s[-+*/]\s/.test(i)&&!i.includes("NaN"))i=Ki(i,e);else if(Gi.test(i)){const[,c]=i.match(Gi);i=`calc(${M(Number(c),Os)})`}}return F(r,i),i},n1="css-gradient",Is=`${Lt}(?:${zn})`,Yi=`${Is}|${_t}`,r1=`${Lt}(?:${Cr})|0`,Ht=`${r1}|${_t}`,Zi=`${xo}(?:${Cr}|%)|0`,s1=`${xo}(?:${Cr})|0`,te="center",Za="left|right",Ja="top|bottom",vn="start|end",ur=`${Za}|x-(?:${vn})`,hr=`${Ja}|y-(?:${vn})`,qr=`block-(?:${vn})`,Vr=`inline-(?:${vn})`,a1=`${te}|${ur}|${hr}|${qr}|${Vr}|${Ht}`,o1=[`(?:${te}|${ur})\\s+(?:${te}|${hr})`,`(?:${te}|${hr})\\s+(?:${te}|${ur})`,`(?:${te}|${ur}|${Ht})\\s+(?:${te}|${hr}|${Ht})`,`(?:${te}|${qr})\\s+(?:${te}|${Vr})`,`(?:${te}|${Vr})\\s+(?:${te}|${qr})`,`(?:${te}|${vn})\\s+(?:${te}|${vn})`].join("|"),i1=[`(?:${ur})\\s+(?:${Ht})\\s+(?:${hr})\\s+(?:${Ht})`,`(?:${hr})\\s+(?:${Ht})\\s+(?:${ur})\\s+(?:${Ht})`,`(?:${qr})\\s+(?:${Ht})\\s+(?:${Vr})\\s+(?:${Ht})`,`(?:${Vr})\\s+(?:${Ht})\\s+(?:${qr})\\s+(?:${Ht})`,`(?:${vn})\\s+(?:${Ht})\\s+(?:${vn})\\s+(?:${Ht})`].join("|"),Ji="(?:clos|farth)est-(?:corner|side)",Ls=[`${Ji}(?:\\s+${Ji})?`,`${s1}`,`(?:${Zi})\\s+(?:${Zi})`].join("|"),_s="circle|ellipse",Qi=`from\\s+${Is}`,nn=`at\\s+(?:${a1}|${o1}|${i1})`,tl=`to\\s+(?:(?:${Za})(?:\\s(?:${Ja}))?|(?:${Ja})(?:\\s(?:${Za}))?)`,Ce=`in\\s+(?:${Wo}|${Ro})`,el=/^(?:repeating-)?(?:conic|linear|radial)-gradient\(/,l1=/^((?:repeating-)?(?:conic|linear|radial)-gradient)\(/,c1=t=>{if(S(t)&&(t=t.trim(),el.test(t))){const[,e]=t.match(l1);return e}return""},u1=(t,e)=>{if(S(t)&&S(e)){t=t.trim(),e=e.trim();let n="";if(/^(?:repeating-)?linear-gradient$/.test(e)?n=[`(?:${Is}|${tl})(?:\\s+${Ce})?`,`${Ce}(?:\\s+(?:${Is}|${tl}))?`].join("|"):/^(?:repeating-)?radial-gradient$/.test(e)?n=[`(?:${_s})(?:\\s+(?:${Ls}))?(?:\\s+${nn})?(?:\\s+${Ce})?`,`(?:${Ls})(?:\\s+(?:${_s}))?(?:\\s+${nn})?(?:\\s+${Ce})?`,`${nn}(?:\\s+${Ce})?`,`${Ce}(?:\\s+${_s})(?:\\s+(?:${Ls}))?(?:\\s+${nn})?`,`${Ce}(?:\\s+${Ls})(?:\\s+(?:${_s}))?(?:\\s+${nn})?`,`${Ce}(?:\\s+${nn})?`].join("|"):/^(?:repeating-)?conic-gradient$/.test(e)&&(n=[`${Qi}(?:\\s+${nn})?(?:\\s+${Ce})?`,`${nn}(?:\\s+${Ce})?`,`${Ce}(?:\\s+${Qi})?(?:\\s+${nn})?`].join("|")),n)return new RegExp(`^(?:${n})$`).test(t)}return!1},nl=(t,e,n={})=>{if(Array.isArray(t)&&t.length>1){const r=/^(?:repeating-)?conic-gradient$/.test(e)?Yi:Ht,s=new RegExp(`^(?:${r})$`),a=new RegExp(`(?:\\s+(?:${r})){1,2}$`),o=[];for(const c of t)if(S(c))if(s.test(c))o.push("hint");else{const l=c.replace(a,"");if(fn(l,n))o.push("color");else return!1}const i=o.join(",");return/^color(?:,(?:hint,)?color)+$/.test(i)}return!1},h1=(t,e={})=>{if(S(t)){t=t.trim();const n=gt({namespace:n1,name:"parseGradient",value:t},e),r=mt(n);if(r instanceof st)return r.isNull?null:r.item;const s=c1(t),a=t.replace(el,"").replace(/\)$/,"");if(s&&a){const[o="",...i]=Ii(a,{delimiter:","}),c=/^(?:repeating-)?conic-gradient$/.test(s)?Yi:Ht,l=new RegExp(`(?:\\s+(?:${c})){1,2}$`);let h=!1;if(l.test(o)){const f=o.replace(l,"");fn(f,e)&&(h=!0)}else fn(o,e)&&(h=!0);if(h){if(i.unshift(o),nl(i,s,e)){const f={value:t,type:s,colorStopList:i};return F(n,f),f}}else if(i.length>1){const f=o;if(u1(f,s)&&nl(i,s,e)){const d={value:t,type:s,gradientLine:f,colorStopList:i};return F(n,d),d}}}return F(n,null),null}return null},f1=(t,e={})=>h1(t,e)!==null,je="convert",p1=new RegExp(gs),d1=new RegExp(Io),m1=new RegExp(kr),rn=(t,e={})=>{if(S(t)){if(t=t.trim(),!t)return new P}else return new P;const n=gt({namespace:je,name:"preProcess",value:t},e),r=mt(n);if(r instanceof st)return r.isNull?r:r.item;if(m1.test(t)){const s=Tr(t,e);if(S(s))t=s;else return F(n,null),new P}if(d1.test(t)){const s=Ya(t,e);if(S(s))t=s;else return F(n,null),new P}else p1.test(t)&&(t=Gr(t,e));if(t.startsWith("color-mix")){const s=structuredClone(e);s.format=ot,s.nullable=!0;const a=nr(t,s);return F(n,a),a}return F(n,t),t},g1=t=>Rr(t),v1=(t,e={})=>{if(S(t)){const o=rn(t,e);if(o instanceof P)return null;t=o.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const{alpha:n=!1}=e,r=gt({namespace:je,name:"colorToHex",value:t},e),s=mt(r);if(s instanceof st)return s.isNull?null:s.item;let a;return e.nullable=!0,n?(e.format="hexAlpha",a=nr(t,e)):(e.format="hex",a=nr(t,e)),S(a)?(F(r,a),a):(F(r,null),null)},b1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:je,name:"colorToHsl",value:t},e),r=mt(n);if(r instanceof st)return r.item;e.format="hsl";const s=Fa(t,e);return F(n,s),s},w1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:je,name:"colorToHwb",value:t},e),r=mt(n);if(r instanceof st)return r.item;e.format="hwb";const s=xa(t,e);return F(n,s),s},$1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:je,name:"colorToLab",value:t},e),r=mt(n);if(r instanceof st)return r.item;const s=Sa(t,e);return F(n,s),s},y1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:je,name:"colorToLch",value:t},e),r=mt(n);if(r instanceof st)return r.item;const s=Aa(t,e);return F(n,s),s},N1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:je,name:"colorToOklab",value:t},e),r=mt(n);if(r instanceof st)return r.item;const s=Pa(t,e);return F(n,s),s},E1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:je,name:"colorToOklch",value:t},e),r=mt(n);if(r instanceof st)return r.item;const s=Ma(t,e);return F(n,s),s},C1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:je,name:"colorToRgb",value:t},e),r=mt(n);if(r instanceof st)return r.item;const s=Wr(t,e);return F(n,s),s},rl=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:je,name:"colorToXyz",value:t},e),r=mt(n);if(r instanceof st)return r.item;let s;return t.startsWith("color(")?[,...s]=vt(t,e):[,...s]=Nt(t,e),F(n,s),s},k1=(t,e={})=>(e.d50=!0,rl(t,e)),F1={colorToHex:v1,colorToHsl:b1,colorToHwb:w1,colorToLab:$1,colorToLch:y1,colorToOklab:N1,colorToOklch:E1,colorToRgb:C1,colorToXyz:rl,colorToXyzD50:k1,numberToHex:g1};/*!
 * CSS color - Resolve, parse, convert CSS color.
 * @license MIT
 * @copyright asamuzaK (Kazz)
 * @see {@link https://github.com/asamuzaK/cssColor/blob/main/LICENSE}
 */const Qa={cssCalc:Gr,cssVar:xu,extractDashedIdent:z0,isColor:fn,isGradient:f1,splitValue:Ii},x1=Qa.isColor,S1=Qa.cssCalc;export{F1 as convert,S1 as cssCalc,x1 as isColor,A0 as resolve,Qa as utils};
