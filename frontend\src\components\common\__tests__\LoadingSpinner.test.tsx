import { describe, it, expect } from 'vitest';
import { render, screen } from '@/test/utils';
import LoadingSpinner, { 
  <PERSON><PERSON>oader, 
  <PERSON>line<PERSON>oader, 
  <PERSON>tonLoader, 
  OverlayLoader,
  SkeletonCard,
  SkeletonList,
  SkeletonText
} from '../LoadingSpinner';

describe('LoadingSpinner', () => {
  it('renders default spinner', () => {
    render(<LoadingSpinner />);
    expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
  });

  it('renders with custom text', () => {
    render(<LoadingSpinner text="Loading data..." />);
    expect(screen.getByText('Loading data...')).toBeInTheDocument();
  });

  it('renders different sizes', () => {
    const { rerender } = render(<LoadingSpinner size="sm" />);
    expect(screen.getByTestId('mock-icon')).toHaveClass('w-4', 'h-4');

    rerender(<LoadingSpinner size="lg" />);
    expect(screen.getByTestId('mock-icon')).toHaveClass('w-8', 'h-8');
  });

  it('renders different variants', () => {
    const { rerender } = render(<LoadingSpinner variant="brain" />);
    expect(screen.getByTestId('mock-icon')).toBeInTheDocument();

    rerender(<LoadingSpinner variant="dots" />);
    expect(screen.getAllByTestId('mock-icon')).toHaveLength(0); // dots variant doesn't use icons

    rerender(<LoadingSpinner variant="pulse" />);
    expect(screen.queryByTestId('mock-icon')).not.toBeInTheDocument(); // pulse variant doesn't use icons
  });

  it('renders fullscreen variant', () => {
    render(<LoadingSpinner fullScreen />);
    expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
  });
});

describe('Specialized Loading Components', () => {
  it('renders PageLoader', () => {
    render(<PageLoader text="Loading page..." />);
    expect(screen.getByText('Loading page...')).toBeInTheDocument();
  });

  it('renders InlineLoader', () => {
    render(<InlineLoader text="Loading..." />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders ButtonLoader', () => {
    render(<ButtonLoader />);
    expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
  });

  it('renders OverlayLoader', () => {
    render(<OverlayLoader text="Processing..." />);
    expect(screen.getByText('Processing...')).toBeInTheDocument();
  });
});

describe('Skeleton Components', () => {
  it('renders SkeletonCard', () => {
    render(<SkeletonCard />);
    expect(document.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  it('renders SkeletonList with default count', () => {
    render(<SkeletonList />);
    expect(document.querySelectorAll('.animate-pulse')).toHaveLength(3);
  });

  it('renders SkeletonList with custom count', () => {
    render(<SkeletonList count={5} />);
    expect(document.querySelectorAll('.animate-pulse')).toHaveLength(5);
  });

  it('renders SkeletonText with default lines', () => {
    render(<SkeletonText />);
    expect(document.querySelectorAll('.animate-pulse')).toHaveLength(3);
  });

  it('renders SkeletonText with custom lines', () => {
    render(<SkeletonText lines={2} />);
    expect(document.querySelectorAll('.animate-pulse')).toHaveLength(2);
  });
});
