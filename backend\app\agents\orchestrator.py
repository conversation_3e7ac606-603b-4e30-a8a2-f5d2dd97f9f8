"""Main categorization agent using Mirascope for AI-powered input classification."""

import json
import logging
from typing import AsyncGenerator, Dict, Any, List
from datetime import datetime

from mirascope import llm
from pydantic import BaseModel, Field

from app.models.pydantic_models import (
    UserInput, 
    CategoryDecision, 
    InputCategory, 
    AIResponse,
    ProcessingState,
    AnimationStep
)
from app.config import settings

logger = logging.getLogger(__name__)


class CategoryAnalysis(BaseModel):
    """Structured response for input categorization."""
    category: str = Field(..., description="One of: task, event, ai_question")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    reasoning: str = Field(..., description="Detailed reasoning for categorization")
    extracted_details: Dict[str, Any] = Field(default={}, description="Extracted information")


class OrchestratorAgent:
    """
    Main categorization agent with visual feedback streaming.
    Follows the mermaid diagram workflow exactly.
    """
    
    def __init__(self):
        self.history: List[Dict[str, Any]] = []
        self.processing_steps: List[str] = []
        
    @llm.call(
        provider="openai",  # OpenRouter is OpenAI compatible
        model=settings.default_model,
        response_model=CategoryAnalysis,  # Structured output for non-tool calling models
        base_url="https://openrouter.ai/api/v1",
        api_key=settings.openrouter_api_key
    )
    async def categorize_input(self, user_input: UserInput) -> str:
        """
        Analyze user input and categorize into Task, Event, or AI Question.
        CRITICAL: No keyword hardcoding - pure AI decision making.
        """
        return f"""
        You are an intelligent categorization agent. Analyze the user's input and categorize it into exactly one of these three categories:

        1. **TASK** - Something the user needs to do or accomplish
           Examples: homework, chores, work items, personal goals, to-do items
           
        2. **EVENT** - Scheduled items, reminders, appointments with dates/times
           Examples: meetings, exams, appointments, deadlines, social events
           
        3. **AI_QUESTION** - Questions that need answers or information requests
           Examples: factual questions, explanations, research requests, how-to questions

        User input: "{user_input.text}"

        Analyze this step by step:
        1. What is the user trying to accomplish?
        2. Does this involve scheduling or time-based activities? → EVENT
        3. Does this involve doing or completing something? → TASK  
        4. Does this involve getting information or answers? → AI_QUESTION

        Provide your categorization with detailed reasoning and confidence score.
        Also extract any relevant details like dates, priorities, or question types.
        
        Be very confident in your decision - this categorization drives the entire workflow.
        """
    
    async def process_with_visual_feedback(
        self, 
        user_input: UserInput
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process input with visual feedback following the mermaid diagram EXACTLY.
        Streams processing updates for real-time UI animations.
        """
        try:
            # Step 1: Analyzing input animation (from mermaid diagram)
            yield {
                "step": "analyzing", 
                "message": "🎭 Analyzing input...", 
                "animation": "typing_indicator",
                "timestamp": datetime.now().isoformat()
            }
            
            # Step 2: Categorizing animation
            yield {
                "step": "categorizing", 
                "message": "🤖 Categorizing...", 
                "animation": "brain_animation",
                "timestamp": datetime.now().isoformat()
            }
            
            # Step 3: Get AI categorization
            logger.info(f"Categorizing input: {user_input.text}")
            category_result = await self.categorize_input(user_input)
            
            # Parse the structured response
            if hasattr(category_result, 'category'):
                category_data = category_result
            else:
                # Fallback parsing if needed
                category_data = CategoryAnalysis(
                    category="task",
                    confidence=0.8,
                    reasoning="Fallback categorization",
                    extracted_details={}
                )
            
            # Step 4: Category decision with visual feedback
            yield {
                "step": "category_decision",
                "message": f"🎯 Category Decision: {category_data.category.upper()}",
                "animation": "selection_highlight",
                "category": category_data.category,
                "confidence": category_data.confidence,
                "timestamp": datetime.now().isoformat()
            }
            
            # Step 5: Route to appropriate processing with category-specific animations
            if category_data.category == "task":
                yield {
                    "step": "task_identified", 
                    "message": "📝 Task identified!", 
                    "animation": "green_pulse",
                    "timestamp": datetime.now().isoformat()
                }
                result = await self._process_task(user_input, category_data)
                
            elif category_data.category == "event":
                yield {
                    "step": "event_identified", 
                    "message": "📋 Event identified!", 
                    "animation": "blue_pulse",
                    "timestamp": datetime.now().isoformat()
                }
                result = await self._process_event(user_input, category_data)
                
            else:  # ai_question
                yield {
                    "step": "question_identified", 
                    "message": "❓ Question identified!", 
                    "animation": "purple_pulse",
                    "timestamp": datetime.now().isoformat()
                }
                result = await self._process_question(user_input, category_data)
            
            # Final step: Success animation
            yield {
                "step": "complete", 
                "message": "✅ Processing complete!", 
                "animation": "success_confetti",
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in orchestrator processing: {e}")
            yield {
                "step": "error",
                "message": f"❌ Error: {str(e)}",
                "animation": "shake_animation",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _process_task(self, user_input: UserInput, category_data: CategoryAnalysis) -> Dict[str, Any]:
        """Process task-type inputs following mermaid diagram."""
        # TODO: Integrate with task agent and tools
        return {
            "type": "task",
            "title": user_input.text,
            "category": category_data.category,
            "confidence": category_data.confidence,
            "reasoning": category_data.reasoning
        }
    
    async def _process_event(self, user_input: UserInput, category_data: CategoryAnalysis) -> Dict[str, Any]:
        """Process event-type inputs following mermaid diagram."""
        # TODO: Integrate with calendar agent and tools
        return {
            "type": "event",
            "title": user_input.text,
            "category": category_data.category,
            "confidence": category_data.confidence,
            "reasoning": category_data.reasoning
        }
    
    async def _process_question(self, user_input: UserInput, category_data: CategoryAnalysis) -> Dict[str, Any]:
        """Process question-type inputs following mermaid diagram."""
        # TODO: Integrate with search agent and tools
        return {
            "type": "ai_question",
            "question": user_input.text,
            "category": category_data.category,
            "confidence": category_data.confidence,
            "reasoning": category_data.reasoning
        }
